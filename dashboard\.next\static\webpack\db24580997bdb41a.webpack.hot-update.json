{"c": ["app/layout", "app/(dashboards)/school-admin/timetable/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5C(dashboards)%5C%5Cschool-admin%5C%5Ctimetable%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./src/app/(dashboards)/school-admin/timetable/page.tsx", "(app-pages-browser)/./src/app/services/SubjectServices.tsx", "(app-pages-browser)/./src/app/services/TimetableServices.tsx", "(app-pages-browser)/./src/components/modals/TimetableModal.tsx", "(app-pages-browser)/./src/components/ui/Toast.tsx"]}