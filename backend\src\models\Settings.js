const mongoose = require('mongoose');

const settingsSchema = new mongoose.Schema({
  general: {
    platform_name: { type: String, required: true },
    support_email: { type: String, required: true },
    default_language: { type: String, required: true, default: 'en' },
    maintenance_mode: { type: <PERSON><PERSON>an, default: false },
    maintenance_message: { type: String, default: '' },
  },

  financials: {
    price_per_credit: { type: Number, required: true },
    payment_gateway: { type: String, required: true }, // e.g. 'stripe', 'paypal'
    late_payment_fee: { type: Number, required: true },
    payment_due_period: { type: Number, required: true }, // in days
  },

  notifications: {
    methods: {
      sms: { type: Boolean, default: false },
      email: { type: Boolean, default: true },
      in_app: { type: Boolean, default: true },
    },
    notification_frequency: {
      type: String,
      enum: ['instant', 'daily', 'weekly'],
      default: 'instant',
    },
    system_announcement_banner: {
      enabled: { type: <PERSON>olean, default: false },
      text: { type: String, default: '' },
      banner_type: {
        type: String,
        enum: ['info', 'warning', 'error', 'success'],
        default: 'info',
      },
    },
  },

  security: {
    password_min_length: { type: Number, default: 8 },
    password_require_special_char: { type: Boolean, default: true },
    session_timeout_minutes: { type: Number, default: 30 },
    two_factor_auth: { type: Boolean, default: false },
    login_attempt_limit: { type: Number, default: 5 },
    lockout_duration_minutes: { type: Number, default: 15 },
  },

}, {
  timestamps: true,
});

const Settings = mongoose.models.Settings || mongoose.model('Settings', settingsSchema);
module.exports = Settings;
