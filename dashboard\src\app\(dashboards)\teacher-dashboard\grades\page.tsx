"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Percent, Plus, Filter, Search, BookOpen, Users } from "lucide-react";
import { motion } from "framer-motion";
import TeacherLayout from "@/components/Dashboard/Layouts/TeacherLayout";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import CircularLoader from "@/components/widgets/CircularLoader";
import DataTableFix from "@/components/utils/TableFix";
import { 
  getGradeRecords, 
  getGradeStats, 
  createGrade,
  updateGrade,
  deleteGrade,
  deleteMultipleGrades,
  GradeRecord, 
  GradeStats 
} from "@/app/services/GradeServices";
import { getTeacherStudents, getTeacherSubjects, getTeacherClasses } from "@/app/services/TeacherAssignmentServices";
import { getExamTypes } from "@/app/services/ExamTypeServices";
import GradeModal from "@/components/modals/GradeModal";
import DeleteConfirmationModal from "@/components/modals/DeleteConfirmationModal";
import { useToast, ToastContainer } from "@/components/ui/Toast";

interface SelectedSchool {
  school_id: string;
  school_name: string;
  access_granted_at: string;
}

const navigation = {
  icon: Percent,
  baseHref: "/teacher-dashboard/grades",
  title: "Student Grades"
};

export default function TeacherGradesPage() {
  const { logout, user } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toasts, removeToast, showSuccess, showError } = useToast();

  // State management
  const [selectedSchool, setSelectedSchool] = useState<SelectedSchool | null>(null);
  const [loadingSchool, setLoadingSchool] = useState(true);
  const [loadingData, setLoadingData] = useState(false);
  const [gradeRecords, setGradeRecords] = useState<GradeRecord[]>([]);
  const [stats, setStats] = useState<GradeStats | null>(null);

  // Filter states
  const [selectedClass, setSelectedClass] = useState(searchParams.get('class') || 'all');
  const [selectedSubject, setSelectedSubject] = useState('all');
  const [selectedTerm, setSelectedTerm] = useState('all');
  const [selectedExamType, setSelectedExamType] = useState('all');

  // Modal states
  const [isGradeModalOpen, setIsGradeModalOpen] = useState(false);
  const [gradeToEdit, setGradeToEdit] = useState<GradeRecord | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [gradeToDelete, setGradeToDelete] = useState<GradeRecord | null>(null);
  const [selectedGrades, setSelectedGrades] = useState<string[]>([]);
  const [deleteType, setDeleteType] = useState<"single" | "multiple" | "all">("single");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form data
  const [students, setStudents] = useState<any[]>([]);
  const [subjects, setSubjects] = useState<any[]>([]);
  const [examTypes, setExamTypes] = useState<any[]>([]);
  const [classes, setClasses] = useState<any[]>([]);

  // Get school ID from user context
  const schoolId = selectedSchool?.school_id;

  // Load selected school from localStorage
  useEffect(() => {
    const savedSchool = localStorage.getItem('teacher_selected_school');
    if (savedSchool) {
      setSelectedSchool(JSON.parse(savedSchool));
    }
    setLoadingSchool(false);
  }, []);

  // Fetch grade data from API
  useEffect(() => {
    const fetchGradeData = async () => {
      if (!schoolId) {
        showError("Error", "No school ID found");
        setLoadingData(false);
        return;
      }

      try {
        setLoadingData(true);

        // Build filters
        const filters: any = {};
        if (selectedClass !== 'all') filters.class_id = selectedClass;
        if (selectedSubject !== 'all') filters.subject_id = selectedSubject;
        if (selectedTerm !== 'all') filters.term = selectedTerm;
        if (selectedExamType !== 'all') filters.exam_type = selectedExamType;

        // Fetch records and stats in parallel
        const [recordsResponse, statsResponse] = await Promise.all([
          getGradeRecords(schoolId as string, filters),
          getGradeStats(schoolId as string, filters)
        ]);

        setGradeRecords(recordsResponse.grade_records);
        setStats(statsResponse.stats);
      } catch (error) {
        console.error("Error fetching grade data:", error);
        showError("Error", "Failed to load grade data");
      } finally {
        setLoadingData(false);
      }
    };

    fetchGradeData();
  }, [schoolId, selectedClass, selectedSubject, selectedTerm, selectedExamType]);

  // Fetch additional data for modals
  useEffect(() => {
    const fetchAdditionalData = async () => {
      if (!schoolId) return;

      try {
        // Fetch data with individual error handling - only teacher's assigned data
        const results = await Promise.allSettled([
          getTeacherStudents(schoolId as string),
          getTeacherSubjects(schoolId as string),
          getTeacherClasses(schoolId as string),
          getExamTypes()
        ]);

        // Handle each result individually
        if (results[0].status === 'fulfilled') {
          setStudents(results[0].value);
        } else {
          console.error("Failed to fetch teacher students:", results[0].reason);
          setStudents([]);
        }

        if (results[1].status === 'fulfilled') {
          setSubjects(results[1].value);
        } else {
          console.error("Failed to fetch teacher subjects:", results[1].reason);
          setSubjects([]);
        }

        if (results[2].status === 'fulfilled') {
          setClasses(results[2].value);
        } else {
          console.error("Failed to fetch teacher classes:", results[2].reason);
          setClasses([]);
        }

        if (results[3].status === 'fulfilled') {
          setExamTypes(results[3].value);
        } else {
          console.error("Failed to fetch exam types:", results[3].reason);
          setExamTypes([]);
        }

        // Show warning if any critical data failed to load
        const anyDataFailed = results.some(result => result.status === 'rejected');
        if (anyDataFailed) {
          showError("Warning", "Some form data could not be loaded. Some features may be limited.");
        }
      } catch (error) {
        console.error("Error fetching additional data:", error);
        showError("Error", "Failed to load form data");
      }
    };

    fetchAdditionalData();
  }, [schoolId]);

  // Handle school change
  const handleSchoolChange = () => {
    router.push('/teacher-dashboard');
  };

  // CRUD Functions
  const handleCreateGrade = () => {
    setGradeToEdit(null);
    setIsGradeModalOpen(true);
  };

  const handleEditGrade = (grade: GradeRecord) => {
    setGradeToEdit(grade);
    setIsGradeModalOpen(true);
  };

  const handleDeleteSingle = (grade: GradeRecord) => {
    setGradeToDelete(grade);
    setDeleteType("single");
    setIsDeleteModalOpen(true);
  };

  const handleDeleteMultiple = () => {
    setDeleteType("multiple");
    setIsDeleteModalOpen(true);
  };

  const handleDeleteAll = () => {
    setDeleteType("all");
    setIsDeleteModalOpen(true);
  };

  // Modal submission function
  const handleGradeSubmit = async (data: any) => {
    if (!schoolId) {
      showError("Error", "No school ID found");
      return;
    }

    setIsSubmitting(true);
    try {
      if (gradeToEdit) {
        await updateGrade(gradeToEdit._id, data);
      } else {
        await createGrade(data);
      }

      // Refresh grade data
      const filters: any = {};
      if (selectedClass !== 'all') filters.class_id = selectedClass;
      if (selectedSubject !== 'all') filters.subject_id = selectedSubject;
      if (selectedTerm !== 'all') filters.term = selectedTerm;
      if (selectedExamType !== 'all') filters.exam_type = selectedExamType;

      const [recordsResponse, statsResponse] = await Promise.all([
        getGradeRecords(schoolId as string, filters),
        getGradeStats(schoolId as string, filters)
      ]);

      setGradeRecords(recordsResponse.grade_records);
      setStats(statsResponse.stats);
      setIsGradeModalOpen(false);
      setGradeToEdit(null);
      
      // Show success notification
      showSuccess(
        gradeToEdit ? "Grade Updated" : "Grade Added",
        gradeToEdit ? "Grade has been updated successfully." : "New grade has been added successfully."
      );
    } catch (error) {
      console.error("Error submitting grade:", error);
      showError("Error", "Failed to save grade. Please try again.");
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  // Delete confirmation function
  const handleDeleteConfirm = async () => {
    if (!schoolId) {
      showError("Error", "No school ID found");
      return;
    }

    setIsSubmitting(true);
    try {
      if (deleteType === "single" && gradeToDelete) {
        await deleteGrade(gradeToDelete._id);
      } else if (deleteType === "multiple") {
        await deleteMultipleGrades(selectedGrades);
      } else if (deleteType === "all") {
        await deleteMultipleGrades(gradeRecords.map(g => g._id));
      }

      // Refresh grade data
      const filters: any = {};
      if (selectedClass !== 'all') filters.class_id = selectedClass;
      if (selectedSubject !== 'all') filters.subject_id = selectedSubject;
      if (selectedTerm !== 'all') filters.term = selectedTerm;
      if (selectedExamType !== 'all') filters.exam_type = selectedExamType;

      const [recordsResponse, statsResponse] = await Promise.all([
        getGradeRecords(schoolId as string, filters),
        getGradeStats(schoolId as string, filters)
      ]);

      setGradeRecords(recordsResponse.grade_records);
      setStats(statsResponse.stats);
      setIsDeleteModalOpen(false);
      setGradeToDelete(null);
      setSelectedGrades([]);
      
      // Show success notification
      if (deleteType === "single") {
        showSuccess("Grade Deleted", "Grade record has been deleted successfully.");
      } else {
        showSuccess("Grades Deleted", `${selectedGrades.length} grade records have been deleted successfully.`);
      }
    } catch (error) {
      console.error("Error deleting grade(s):", error);
      showError("Error", "Failed to delete grade record(s). Please try again.");
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loadingSchool) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <CircularLoader />
      </div>
    );
  }

  if (!selectedSchool) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">No School Selected</h2>
          <button
            onClick={() => router.push('/teacher-dashboard')}
            className="px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600"
          >
            Select School
          </button>
        </div>
      </div>
    );
  }

  return (
    <ProtectedRoute allowedRoles={["teacher"]}>
      <TeacherLayout
        navigation={navigation}
        selectedSchool={{
          _id: selectedSchool.school_id,
          name: selectedSchool.school_name
        }}
        onSchoolChange={handleSchoolChange}
        onLogout={logout}
      >
        <div className="space-y-6">
          {/* Header */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center">
                  <Percent className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-foreground">Student Grades</h1>
                  <p className="text-foreground/60">
                    Manage grades for {selectedSchool.school_name}
                  </p>
                </div>
              </div>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleCreateGrade}
                className="flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600"
              >
                <Plus size={16} />
                <span>Add Grade</span>
              </motion.button>
            </div>
          </div>

          {/* Stats Cards */}
          {stats && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="bg-widget rounded-lg border border-stroke p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-foreground/60">Total Grades</p>
                    <p className="text-2xl font-bold text-foreground">{stats.totalGrades}</p>
                  </div>
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <Percent className="h-5 w-5 text-blue-600" />
                  </div>
                </div>
              </div>
              
              <div className="bg-widget rounded-lg border border-stroke p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-foreground/60">Average Grade</p>
                    <p className="text-2xl font-bold text-foreground">{stats.averageScore || 'N/A'}</p>
                  </div>
                  <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                    <BookOpen className="h-5 w-5 text-green-600" />
                  </div>
                </div>
              </div>
              
              <div className="bg-widget rounded-lg border border-stroke p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-foreground/60">Students Graded</p>
                    <p className="text-2xl font-bold text-foreground">{students.length}</p>
                  </div>
                  <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                    <Users className="h-5 w-5 text-purple-600" />
                  </div>
                </div>
              </div>
              
              <div className="bg-widget rounded-lg border border-stroke p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-foreground/60">Subjects</p>
                    <p className="text-2xl font-bold text-foreground">{subjects.length}</p>
                  </div>
                  <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                    <BookOpen className="h-5 w-5 text-orange-600" />
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Grades Table */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-foreground">
                Grade Records
              </h2>
              
              <div className="flex items-center space-x-4">
                {/* Filters */}
                <select
                  value={selectedClass}
                  onChange={(e) => setSelectedClass(e.target.value)}
                  className="px-3 py-2 border border-stroke rounded-md text-sm"
                >
                  <option value="all">All Classes</option>
                  {classes.map((classItem) => (
                    <option key={classItem._id} value={classItem._id}>
                      {classItem.name} - {classItem.level}
                    </option>
                  ))}
                </select>
                
                <select
                  value={selectedSubject}
                  onChange={(e) => setSelectedSubject(e.target.value)}
                  className="px-3 py-2 border border-stroke rounded-md text-sm"
                >
                  <option value="all">All Subjects</option>
                  {subjects.map((subject) => (
                    <option key={subject._id} value={subject._id}>
                      {subject.name}
                    </option>
                  ))}
                </select>
                
                <select
                  value={selectedTerm}
                  onChange={(e) => setSelectedTerm(e.target.value)}
                  className="px-3 py-2 border border-stroke rounded-md text-sm"
                >
                  <option value="all">All Terms</option>
                  <option value="1st Term">1st Term</option>
                  <option value="2nd Term">2nd Term</option>
                  <option value="3rd Term">3rd Term</option>
                </select>
                
                <select
                  value={selectedExamType}
                  onChange={(e) => setSelectedExamType(e.target.value)}
                  className="px-3 py-2 border border-stroke rounded-md text-sm"
                >
                  <option value="all">All Exam Types</option>
                  {examTypes.map((type) => (
                    <option key={type._id} value={type._id}>
                      {type.type}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <DataTableFix
              data={gradeRecords as any[]}
              columns={[
                {
                  header: "Student",
                  accessor: (item: any) => (
                    <div>
                      <p className="font-medium">{item.student_name}</p>
                      <p className="text-sm text-foreground/60">{item.student_id}</p>
                    </div>
                  )
                },
                {
                  header: "Subject",
                  accessor: (item: any) => item.subject_name
                },
                {
                  header: "Exam Type",
                  accessor: (item: any) => (
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                      {item.exam_type}
                    </span>
                  )
                },
                {
                  header: "Score",
                  accessor: (item: any) => (
                    <span className="font-medium">{item.score}/100</span>
                  )
                },
                {
                  header: "Grade",
                  accessor: (item: any) => (
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      item.grade === 'A+' || item.grade === 'A' ? 'bg-green-100 text-green-800' :
                      item.grade === 'B+' || item.grade === 'B' ? 'bg-blue-100 text-blue-800' :
                      item.grade === 'C+' || item.grade === 'C' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {item.grade}
                    </span>
                  )
                },
                {
                  header: "Term",
                  accessor: (item: any) => item.term
                },
                {
                  header: "Academic Year",
                  accessor: (item: any) => item.academic_year
                }
              ]}
              actions={[
                {
                  label: "edit",
                  onClick: handleEditGrade
                },
                {
                  label: "delete",
                  onClick: handleDeleteSingle
                }
              ]}
              loading={loadingData}
              onSelectionChange={(selectedRows: any[]) => {
                setSelectedGrades(selectedRows.map(row => row._id));
              }}
              handleDeleteMultiple={() => handleDeleteMultiple()}
              handleDeleteAll={handleDeleteAll}
              idAccessor="_id"
              enableBulkActions={true}
            />
          </div>
        </div>

        {/* Grade Modal */}
        <GradeModal
          isOpen={isGradeModalOpen}
          onClose={() => {
            setIsGradeModalOpen(false);
            setGradeToEdit(null);
          }}
          onSubmit={handleGradeSubmit}
          grade={gradeToEdit}
          students={students}
          subjects={subjects}
          examTypes={examTypes}
          loading={isSubmitting}
        />

        {/* Delete Confirmation Modal */}
        <DeleteConfirmationModal
          isOpen={isDeleteModalOpen}
          onClose={() => {
            setIsDeleteModalOpen(false);
            setGradeToDelete(null);
          }}
          onConfirm={handleDeleteConfirm}
          title={
            deleteType === "single" ? "Delete Grade" :
            deleteType === "multiple" ? "Delete Selected Grades" :
            "Delete All Grades"
          }
          message={
            deleteType === "single" ? "Are you sure you want to delete this grade record?" :
            deleteType === "multiple" ? `Are you sure you want to delete ${selectedGrades.length} grade records?` :
            "Are you sure you want to delete all grade records? This action cannot be undone."
          }
          loading={isSubmitting}
          itemName={gradeToDelete?.student_name}
          itemCount={deleteType === "multiple" ? selectedGrades.length : undefined}
          type={deleteType}
        />

        {/* Toast Notifications */}
        <ToastContainer toasts={toasts} onClose={removeToast} />
      </TeacherLayout>
    </ProtectedRoute>
  );
}
