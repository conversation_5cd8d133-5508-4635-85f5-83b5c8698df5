import Cookies from "js-cookie";
import { BASE_API_URL } from "../services/AuthContext";

// Interface pour les options de requête
interface RequestOptions extends RequestInit {
  headers?: Record<string, string>;
}

// Fonction pour gérer la déconnexion automatique
const handleUnauthorized = () => {
  // Supprimer le token
  Cookies.remove("idToken");
  
  // Rediriger vers login
  if (typeof window !== 'undefined') {
    window.location.href = '/login';
  }
};

// Fonction pour obtenir le token
const getAuthToken = (): string | null => {
  return Cookies.get("idToken") || null;
};

// Intercepteur HTTP personnalisé
export const authenticatedFetch = async (
  url: string, 
  options: RequestOptions = {}
): Promise<Response> => {
  const token = getAuthToken();
  
  // Si pas de token, rediriger immédiatement
  if (!token) {
    handleUnauthorized();
    throw new Error("No authentication token found");
  }

  // Ajouter le token aux headers
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers,
    'Authorization': `Bearer ${token}`
  };

  // Construire l'URL complète si nécessaire
  const fullUrl = url.startsWith('http') ? url : `${BASE_API_URL}${url}`;

  try {
    const response = await fetch(fullUrl, {
      ...options,
      headers
    });

    // Vérifier si la réponse indique une erreur d'authentification
    if (response.status === 401) {
      console.warn("Token expired or invalid, redirecting to login");
      handleUnauthorized();
      throw new Error("Authentication failed");
    }

    // Vérifier si la réponse indique un token expiré
    if (response.status === 403) {
      const errorData = await response.clone().json().catch(() => ({}));
      if (errorData.message?.includes('token') || errorData.message?.includes('expired')) {
        console.warn("Token expired, redirecting to login");
        handleUnauthorized();
        throw new Error("Token expired");
      }
    }

    return response;
  } catch (error) {
    // Si erreur réseau ou autre, vérifier si c'est lié à l'auth
    if (error instanceof TypeError && error.message.includes('fetch')) {
      console.error("Network error during authenticated request:", error);
    }
    throw error;
  }
};

// Wrapper pour les requêtes GET
export const authenticatedGet = async (url: string): Promise<Response> => {
  return authenticatedFetch(url, { method: 'GET' });
};

// Wrapper pour les requêtes POST
export const authenticatedPost = async (url: string, data: any): Promise<Response> => {
  return authenticatedFetch(url, {
    method: 'POST',
    body: JSON.stringify(data)
  });
};

// Wrapper pour les requêtes PUT
export const authenticatedPut = async (url: string, data: any): Promise<Response> => {
  return authenticatedFetch(url, {
    method: 'PUT',
    body: JSON.stringify(data)
  });
};

// Wrapper pour les requêtes DELETE
export const authenticatedDelete = async (url: string, data?: any): Promise<Response> => {
  const options: RequestOptions = { method: 'DELETE' };
  if (data) {
    options.body = JSON.stringify(data);
  }
  return authenticatedFetch(url, options);
};

// Fonction pour vérifier si l'utilisateur est toujours authentifié
export const checkAuthStatus = async (): Promise<boolean> => {
  const token = getAuthToken();
  
  if (!token) {
    return false;
  }

  try {
    // Faire une requête simple pour vérifier le token
    const response = await authenticatedGet('/user/check-auth');
    return response.ok;
  } catch (error) {
    console.error("Auth check failed:", error);
    return false;
  }
};

// Fonction pour décoder et vérifier l'expiration du token JWT
export const isTokenExpired = (): boolean => {
  const token = getAuthToken();
  
  if (!token) {
    return true;
  }

  try {
    // Décoder le token JWT (partie payload)
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    
    // Vérifier si le token est expiré
    return payload.exp < currentTime;
  } catch (error) {
    console.error("Error decoding token:", error);
    return true;
  }
};

// Fonction pour nettoyer la session
export const clearSession = () => {
  Cookies.remove("idToken");
  if (typeof window !== 'undefined') {
    localStorage.clear();
    sessionStorage.clear();
  }
};
