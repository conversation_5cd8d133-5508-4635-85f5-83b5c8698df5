/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CProjet%5Cscholarify%5Cdashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjet%5Cscholarify%5Cdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CProjet%5Cscholarify%5Cdashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjet%5Cscholarify%5Cdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CProjet%5Cscholarify%5Cdashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjet%5Cscholarify%5Cdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Bricolage_Grotesque%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22bricolage%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cservices%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Cutils%5C%5CThemeInitializer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Bricolage_Grotesque%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22bricolage%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cservices%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Cutils%5C%5CThemeInitializer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/services/AuthContext.tsx */ \"(rsc)/./src/app/services/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/utils/ThemeInitializer.tsx */ \"(rsc)/./src/utils/ThemeInitializer.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Bricolage_Grotesque%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22bricolage%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cservices%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Cutils%5C%5CThemeInitializer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"223ead8c6b9a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcUHJvamV0XFxzY2hvbGFyaWZ5XFxkYXNoYm9hcmRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjIyM2VhZDhjNmI5YVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Bricolage_Grotesque_arguments_subsets_latin_weight_400_700_variableName_bricolage___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Bricolage_Grotesque\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"400\",\"700\"]}],\"variableName\":\"bricolage\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Bricolage_Grotesque\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"400\\\",\\\"700\\\"]}],\\\"variableName\\\":\\\"bricolage\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Bricolage_Grotesque_arguments_subsets_latin_weight_400_700_variableName_bricolage___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Bricolage_Grotesque_arguments_subsets_latin_weight_400_700_variableName_bricolage___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _utils_ThemeInitializer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/ThemeInitializer */ \"(rsc)/./src/utils/ThemeInitializer.tsx\");\n/* harmony import */ var _services_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./services/AuthContext */ \"(rsc)/./src/app/services/AuthContext.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"Scholarify Admin\",\n    description: \"Admin dashboard for Scholarify\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Bricolage_Grotesque_arguments_subsets_latin_weight_400_700_variableName_bricolage___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_ThemeInitializer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_services_AuthContext__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUN1QjtBQVNqQkE7QUFQa0Q7QUFDRjtBQUUvQyxNQUFNRyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUlhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFXWCxnTUFBbUI7OzhCQUNsQyw4REFBQ0MsK0RBQWdCQTs7Ozs7OEJBQ2pCLDhEQUFDQywrREFBWUE7OEJBQ1JLOzs7Ozs7Ozs7Ozs7Ozs7OztBQUtiIiwic291cmNlcyI6WyJEOlxcUHJvamV0XFxzY2hvbGFyaWZ5XFxkYXNoYm9hcmRcXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xyXG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XHJcbmltcG9ydCB7IEJyaWNvbGFnZV9Hcm90ZXNxdWUgfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xyXG5pbXBvcnQgVGhlbWVJbml0aWFsaXplciBmcm9tIFwiQC91dGlscy9UaGVtZUluaXRpYWxpemVyXCI7XHJcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gXCIuL3NlcnZpY2VzL0F1dGhDb250ZXh0XCI7XHJcblxyXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xyXG4gIHRpdGxlOiBcIlNjaG9sYXJpZnkgQWRtaW5cIixcclxuICBkZXNjcmlwdGlvbjogXCJBZG1pbiBkYXNoYm9hcmQgZm9yIFNjaG9sYXJpZnlcIixcclxufTtcclxuY29uc3QgYnJpY29sYWdlID0gQnJpY29sYWdlX0dyb3Rlc3F1ZSh7IHN1YnNldHM6IFtcImxhdGluXCJdLCB3ZWlnaHQ6IFtcIjQwMFwiLCBcIjcwMFwiXSB9KTtcclxuXHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcclxuICBjaGlsZHJlbixcclxufTogUmVhZG9ubHk8e1xyXG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XHJcbn0+KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxyXG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2JyaWNvbGFnZS5jbGFzc05hbWV9PlxyXG4gICAgICAgIDxUaGVtZUluaXRpYWxpemVyIC8+XHJcbiAgICAgICAgPEF1dGhQcm92aWRlcj5cclxuICAgICAgICAgICAge2NoaWxkcmVufVxyXG4gICAgICAgIDwvQXV0aFByb3ZpZGVyPlxyXG4gICAgICA8L2JvZHk+XHJcbiAgICA8L2h0bWw+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiYnJpY29sYWdlIiwiVGhlbWVJbml0aWFsaXplciIsIkF1dGhQcm92aWRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/services/AuthContext.tsx":
/*!******************************************!*\
  !*** ./src/app/services/AuthContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthContext: () => (/* binding */ AuthContext),
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   BASE_API_URL: () => (/* binding */ BASE_API_URL)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const BASE_API_URL = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call BASE_API_URL() from the server but BASE_API_URL is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projet\\scholarify\\dashboard\\src\\app\\services\\AuthContext.tsx",
"BASE_API_URL",
);const AuthContext = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthContext() from the server but AuthContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projet\\scholarify\\dashboard\\src\\app\\services\\AuthContext.tsx",
"AuthContext",
);const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projet\\scholarify\\dashboard\\src\\app\\services\\AuthContext.tsx",
"AuthProvider",
);

/***/ }),

/***/ "(rsc)/./src/utils/ThemeInitializer.tsx":
/*!****************************************!*\
  !*** ./src/utils/ThemeInitializer.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\utils\\\\ThemeInitializer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projet\\scholarify\\dashboard\\src\\utils\\ThemeInitializer.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZXQlNUMlNUNzY2hvbGFyaWZ5JTVDJTVDZGFzaGJvYXJkJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXBhZ2UuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1Byb2pldCU1QyU1Q3NjaG9sYXJpZnklNUMlNUNkYXNoYm9hcmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtc2VnbWVudC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDUHJvamV0JTVDJTVDc2Nob2xhcmlmeSU1QyU1Q2Rhc2hib2FyZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZXQlNUMlNUNzY2hvbGFyaWZ5JTVDJTVDZGFzaGJvYXJkJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDaHR0cC1hY2Nlc3MtZmFsbGJhY2slNUMlNUNlcnJvci1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDUHJvamV0JTVDJTVDc2Nob2xhcmlmeSU1QyU1Q2Rhc2hib2FyZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2xheW91dC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1Byb2pldCU1QyU1Q3NjaG9sYXJpZnklNUMlNUNkYXNoYm9hcmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZXQlNUMlNUNzY2hvbGFyaWZ5JTVDJTVDZGFzaGJvYXJkJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbWV0YWRhdGElNUMlNUNtZXRhZGF0YS1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDUHJvamV0JTVDJTVDc2Nob2xhcmlmeSU1QyU1Q2Rhc2hib2FyZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9PQUFvSTtBQUNwSTtBQUNBLDBPQUF1STtBQUN2STtBQUNBLDBPQUF1STtBQUN2STtBQUNBLG9SQUE2SjtBQUM3SjtBQUNBLHdPQUFzSTtBQUN0STtBQUNBLDRQQUFpSjtBQUNqSjtBQUNBLGtRQUFvSjtBQUNwSjtBQUNBLHNRQUFxSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcUHJvamV0XFxcXHNjaG9sYXJpZnlcXFxcZGFzaGJvYXJkXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXBhZ2UuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFByb2pldFxcXFxzY2hvbGFyaWZ5XFxcXGRhc2hib2FyZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1zZWdtZW50LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxQcm9qZXRcXFxcc2Nob2xhcmlmeVxcXFxkYXNoYm9hcmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcUHJvamV0XFxcXHNjaG9sYXJpZnlcXFxcZGFzaGJvYXJkXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcaHR0cC1hY2Nlc3MtZmFsbGJhY2tcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFByb2pldFxcXFxzY2hvbGFyaWZ5XFxcXGRhc2hib2FyZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGxheW91dC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFByb2pldFxcXFxzY2hvbGFyaWZ5XFxcXGRhc2hib2FyZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG1ldGFkYXRhXFxcXGFzeW5jLW1ldGFkYXRhLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxQcm9qZXRcXFxcc2Nob2xhcmlmeVxcXFxkYXNoYm9hcmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxtZXRhZGF0YS1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcUHJvamV0XFxcXHNjaG9sYXJpZnlcXFxcZGFzaGJvYXJkXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Bricolage_Grotesque%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22bricolage%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cservices%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Cutils%5C%5CThemeInitializer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Bricolage_Grotesque%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22bricolage%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cservices%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Cutils%5C%5CThemeInitializer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/services/AuthContext.tsx */ \"(ssr)/./src/app/services/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/utils/ThemeInitializer.tsx */ \"(ssr)/./src/utils/ThemeInitializer.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Bricolage_Grotesque%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22bricolage%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cservices%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Cutils%5C%5CThemeInitializer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/services/AuthContext.tsx":
/*!******************************************!*\
  !*** ./src/app/services/AuthContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthContext: () => (/* binding */ AuthContext),\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   BASE_API_URL: () => (/* binding */ BASE_API_URL)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _UserServices__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./UserServices */ \"(ssr)/./src/app/services/UserServices.tsx\");\n/* harmony import */ var _utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/httpInterceptor */ \"(ssr)/./src/app/utils/httpInterceptor.tsx\");\n/* __next_internal_client_entry_do_not_use__ BASE_API_URL,AuthContext,AuthProvider auto */ \n\n\n\n\nconst BASE_API_URL = process.env.BASE_API_URL || \"https://scolarify.onrender.com/api\";\n// Create a context for authentication\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)(undefined);\n// composant AuthProvider qui fournit le contexte d'authentification\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [redirectAfterLogin, setRedirectAfterLogin] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [authCheckInterval, setAuthCheckInterval] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Fonction pour forcer la déconnexion\n    const forceLogout = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"AuthProvider.useCallback[forceLogout]\": ()=>{\n            console.warn(\"Force logout triggered\");\n            setUser(null);\n            (0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__.clearSession)();\n            if (authCheckInterval) {\n                clearInterval(authCheckInterval);\n                setAuthCheckInterval(null);\n            }\n            if (false) {}\n        }\n    }[\"AuthProvider.useCallback[forceLogout]\"], [\n        authCheckInterval\n    ]);\n    // Fonction pour vérifier le statut d'authentification\n    const checkAuthStatus = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"AuthProvider.useCallback[checkAuthStatus]\": async ()=>{\n            try {\n                // Vérifier si le token existe\n                const token = js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(\"idToken\");\n                if (!token) {\n                    forceLogout();\n                    return;\n                }\n                // Vérifier si le token est expiré\n                if ((0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__.isTokenExpired)()) {\n                    console.warn(\"Token expired, logging out\");\n                    forceLogout();\n                    return;\n                }\n                // Vérifier avec le serveur\n                const currentUser = await (0,_UserServices__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)();\n                if (!currentUser) {\n                    forceLogout();\n                    return;\n                }\n                setUser(currentUser);\n            } catch (error) {\n                console.error(\"Auth check failed:\", error);\n                forceLogout();\n            }\n        }\n    }[\"AuthProvider.useCallback[checkAuthStatus]\"], [\n        forceLogout\n    ]);\n    // vérifier si un utilisateur est déja connecté\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const checkUserLoggedIn = {\n                \"AuthProvider.useEffect.checkUserLoggedIn\": async ()=>{\n                    try {\n                        // Vérifier d'abord si le token existe et n'est pas expiré\n                        const token = js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(\"idToken\");\n                        if (!token || (0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__.isTokenExpired)()) {\n                            setUser(null);\n                            setLoading(false);\n                            return;\n                        }\n                        const user = await (0,_UserServices__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)();\n                        if (user) {\n                            setUser(user);\n                            // Démarrer la vérification périodique\n                            const interval = setInterval(checkAuthStatus, 60000); // Vérifier toutes les minutes\n                            setAuthCheckInterval(interval);\n                        } else {\n                            js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"idToken\");\n                            setUser(null);\n                        }\n                    } catch (error) {\n                        console.error(\"Error verifying token:\", error);\n                        js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"idToken\");\n                        setUser(null);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.checkUserLoggedIn\"];\n            checkUserLoggedIn();\n            // Cleanup interval on unmount\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    if (authCheckInterval) {\n                        clearInterval(authCheckInterval);\n                    }\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        checkAuthStatus,\n        authCheckInterval\n    ]);\n    const login = async (email, password, rememberMe, redirectUrl)=>{\n        try {\n            const response = await fetch(`${BASE_API_URL}/auth/login`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email: email,\n                    password: password\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                console.error(\"Login error:\", data.message || \"Unknown error\");\n                throw new Error(data.message || \"Login failed\");\n            }\n            const { idToken } = data;\n            if (!idToken) {\n                throw new Error(\"No idToken received\");\n            }\n            // Stocker le token dans les cookies\n            js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].set(\"idToken\", idToken, {\n                expires: rememberMe ? 30 : 7\n            }); // Expire dans 7 jours\n            const user = await (0,_UserServices__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)(); // Vérifier si l'utilisateur est connecté à nouveau après la connexion réussie\n            if (user) {\n                setUser(user);\n            }\n            // Si une URL de redirection est fournie, stocke-la\n            if (redirectUrl) {\n                setRedirectAfterLogin(redirectUrl);\n            }\n        } catch (error) {\n            console.error(\"Login failed:\", error);\n            throw error;\n        }\n    };\n    const logout = async ()=>{\n        setUser(null);\n        js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"idToken\"); // Supprimer le token des cookies\n        setRedirectAfterLogin(null); // Réinitialiser l'URL de redirection\n        return Promise.resolve();\n    };\n    const isAuthentiacted = !!user; // Vérifier si l'utilisateur est authentifié\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            isAuthenticated: isAuthentiacted,\n            loading,\n            setRedirectAfterLogin,\n            redirectAfterLogin,\n            login,\n            logout,\n            checkAuthStatus,\n            forceLogout\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\services\\\\AuthContext.tsx\",\n        lineNumber: 184,\n        columnNumber: 9\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/services/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/services/UserServices.tsx":
/*!*******************************************!*\
  !*** ./src/app/services/UserServices.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   deleteAllUsers: () => (/* binding */ deleteAllUsers),\n/* harmony export */   deleteMultipleUsers: () => (/* binding */ deleteMultipleUsers),\n/* harmony export */   deleteUser: () => (/* binding */ deleteUser),\n/* harmony export */   forget_password: () => (/* binding */ forget_password),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getMonthlyUserStarts: () => (/* binding */ getMonthlyUserStarts),\n/* harmony export */   getParents: () => (/* binding */ getParents),\n/* harmony export */   getTokenFromCookie: () => (/* binding */ getTokenFromCookie),\n/* harmony export */   getTotalUsers: () => (/* binding */ getTotalUsers),\n/* harmony export */   getUserById: () => (/* binding */ getUserById),\n/* harmony export */   getUserBy_id: () => (/* binding */ getUserBy_id),\n/* harmony export */   getUserCountWithChange: () => (/* binding */ getUserCountWithChange),\n/* harmony export */   getUsers: () => (/* binding */ getUsers),\n/* harmony export */   handleUserSearch: () => (/* binding */ handleUserSearch),\n/* harmony export */   registerParent: () => (/* binding */ registerParent),\n/* harmony export */   resend_Code: () => (/* binding */ resend_Code),\n/* harmony export */   resetParentPasswordService: () => (/* binding */ resetParentPasswordService),\n/* harmony export */   reset_password: () => (/* binding */ reset_password),\n/* harmony export */   updateUser: () => (/* binding */ updateUser),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verify_otp: () => (/* binding */ verify_otp)\n/* harmony export */ });\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var jwt_decode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jwt-decode */ \"(ssr)/./node_modules/jwt-decode/build/esm/index.js\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"(ssr)/./src/app/services/AuthContext.tsx\");\n/* harmony import */ var _utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/httpInterceptor */ \"(ssr)/./src/app/utils/httpInterceptor.tsx\");\n\n\n\n\nfunction getTokenFromCookie(name) {\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(name);\n    return token;\n}\nasync function getCurrentUser() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        if (!token) {\n            return null;\n        }\n        const decodedUser = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_1__.jwtDecode)(token);\n        const email = decodedUser.email;\n        const response = await (0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_3__.authenticatedGet)(`/user/get-user-email/${email}`);\n        if (!response.ok) {\n            console.error(\"Error fetching user:\", response.statusText);\n            return null;\n        }\n        const user = await response.json();\n        return user;\n    } catch (error) {\n        console.error(\"Error fetching current user:\", error);\n        return null;\n    }\n}\nasync function getParents() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/get-parents`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${token}`\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching parents:\", response.statusText);\n            throw new Error(\"Failed to fetch parents data\");\n        }\n        const parentsList = await response.json();\n        const parents = parentsList.map((user)=>({\n                _id: user._id,\n                user_id: user.user_id,\n                firebaseUid: user.firebaseUid,\n                name: user.name,\n                email: user.email,\n                phone: user.phone,\n                role: user.role,\n                avatar: user.avatar,\n                address: user.address,\n                school_ids: user.school_ids,\n                student_ids: user.student_ids,\n                isVerified: user.isVerified,\n                verificationCode: user.verificationCode,\n                verificationCodeExpires: user.verificationCodeExpires,\n                lastLogin: user.lastLogin,\n                createdAt: user.createdAt,\n                updatedAt: user.updatedAt\n            }));\n        return parents;\n    } catch (error) {\n        console.error(\"Error fetching parents:\", error);\n        throw new Error(\"Failed to fetch parents data\");\n    }\n}\nasync function getUsers() {\n    try {\n        const token = getTokenFromCookie(\"idToken\"); // Assuming this function gets the token from cookies\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/get-users`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${token}`\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching users:\", response.statusText);\n            throw new Error(\"Failed to fetch users data\");\n        }\n        const usersList = await response.json();\n        const users = usersList.map((user)=>{\n            return {\n                _id: user._id,\n                user_id: user.user_id,\n                firebaseUid: user.firebaseUid,\n                name: user.name,\n                email: user.email,\n                phone: user.phone,\n                role: user.role,\n                avatar: user.avatar,\n                address: user.address,\n                school_ids: user.school_ids,\n                isVerified: user.isVerified,\n                verificationCode: user.verificationCode,\n                verificationCodeExpires: user.verificationCodeExpires,\n                lastLogin: user.lastLogin,\n                createdAt: user.createdAt,\n                updatedAt: user.updatedAt\n            };\n        });\n        return users;\n    } catch (error) {\n        console.error(\"Error fetching users:\", error);\n        throw new Error(\"Failed to fetch users data\");\n    }\n}\nasync function createUser(userData) {\n    try {\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/register-user`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${getTokenFromCookie(\"idToken\")}`\n            },\n            body: JSON.stringify(userData)\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to create user data\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = errorBody?.message || errorMessage;\n            } catch (parseError) {\n                // If parsing the error body fails, use default message\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            console.error(\"Error creating user:\", errorMessage);\n            throw new Error(errorMessage);\n        }\n        const data = await response.json(); // Parse the response as JSON\n        return data; // Return the response data (usually the created user object)\n    } catch (error) {\n        console.error(\"Error creating user:\", error);\n        throw new Error(error instanceof Error ? error.message : \"Failed to create user data\");\n    }\n}\nasync function updateUser(user_id, userData) {\n    try {\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/update-user/${user_id}`, {\n            method: \"PUT\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${getTokenFromCookie(\"idToken\")}`\n            },\n            body: JSON.stringify(userData)\n        });\n        if (!response.ok) {\n            console.error(\"Error updating user:\", response.statusText);\n            throw new Error(\"Failed to update user data\");\n        }\n        const data = await response.json(); // Parse the response as JSON\n        return data; // Return the updated user data (this could be the user object with updated fields)\n    } catch (error) {\n        console.error(\"Error updating user:\", error);\n        throw new Error(\"Failed to update user data\");\n    }\n}\nasync function getUserById(userId) {\n    const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/get-user/${userId}`, {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${getTokenFromCookie(\"idToken\")}`\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching user:\", response.statusText);\n        throw new Error(\"Failed to fetch user data\");\n    }\n    const data = await response.json();\n    const user = {\n        _id: data._id,\n        user_id: data.user_id,\n        name: data.name,\n        email: data.email,\n        phone: data.phone,\n        role: data.role,\n        address: data.address,\n        school_ids: data.school_ids,\n        isVerified: data.isVerified,\n        description: data.description,\n        firebaseUid: data.firebaseUid,\n        createdAt: data.createdAt,\n        updatedAt: data.updatedAt\n    };\n    return user;\n}\nasync function verifyPassword(password, email) {\n    const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/auth/verify-password`, {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n            email: email,\n            password: password\n        })\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching user:\", response.statusText);\n        return false;\n    }\n    return true;\n}\nasync function deleteUser(user_id) {\n    try {\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/delete-user/${user_id}`, {\n            method: \"DELETE\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${getTokenFromCookie(\"idToken\")}`\n            }\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to delete user\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = errorBody?.message || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            console.error(\"Error deleting user:\", errorMessage);\n            throw new Error(errorMessage);\n        }\n        const result = await response.json();\n        return result; // Might return a success message or deleted user data\n    } catch (error) {\n        console.error(\"Error deleting user:\", error);\n        throw new Error(error instanceof Error ? error.message : \"Failed to delete user\");\n    }\n}\nasync function getUserBy_id(_id) {\n    const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/get-user-by-id/${_id}`, {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${getTokenFromCookie(\"idToken\")}`\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching user:\", response.statusText);\n        throw new Error(\"Failed to fetch user data\");\n    }\n    const data = await response.json();\n    const user = {\n        _id: data._id,\n        user_id: data.user_id,\n        name: data.name,\n        email: data.email,\n        phone: data.phone,\n        role: data.role,\n        address: data.address,\n        school_ids: data.school_ids,\n        isVerified: data.isVerified,\n        description: data.description,\n        firebaseUid: data.firebaseUid,\n        createdAt: data.createdAt,\n        updatedAt: data.updatedAt\n    };\n    return user;\n}\nasync function forget_password(email) {\n    try {\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/auth/forgot-password`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email: email\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to send reset password email: check your email\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = errorBody?.message || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error sending reset password email:\", error);\n        throw new Error(\"Failed to send reset password email\");\n    }\n}\nasync function verify_otp(code, email) {\n    try {\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/auth/verify-code`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                code: code,\n                email: email\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to verify OTP\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = errorBody?.message || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error verifying OTP:\", error);\n        throw new Error(\"Failed to verify OTP\");\n    }\n}\nasync function resend_Code(email) {\n    try {\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/auth/resend-code`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email: email\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to resend code\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = errorBody?.message || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error resending code:\", error);\n        throw new Error(\"Failed to resend code\");\n    }\n}\nasync function reset_password(newPassword, email, code) {\n    try {\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/auth/reset-password`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email: email,\n                code: code,\n                newPassword: newPassword\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to reset password\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = errorBody?.message || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error resetting password:\", error);\n        throw new Error(\"Failed to reset password\");\n    }\n}\nasync function handleUserSearch(query) {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/search-users?query=${encodeURIComponent(query)}`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${token}`\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error searching users:\", response.statusText);\n            throw new Error(\"Failed to search users\");\n        }\n        const results = await response.json();\n        const users = results.map((user)=>{\n            return {\n                _id: user._id,\n                user_id: user.user_id,\n                firebaseUid: user.firebaseUid,\n                name: user.name,\n                email: user.email,\n                phone: user.phone,\n                role: user.role,\n                avatar: user.avatar,\n                address: user.address,\n                school_ids: user.school_ids,\n                isVerified: user.isVerified,\n                verificationCode: user.verificationCode,\n                verificationCodeExpires: user.verificationCodeExpires,\n                lastLogin: user.lastLogin,\n                createdAt: user.createdAt,\n                updatedAt: user.updatedAt\n            };\n        });\n        return users;\n    } catch (error) {\n        console.error(\"Error searching users:\", error);\n        throw new Error(\"Failed to search users\");\n    }\n}\nasync function registerParent(parentData) {\n    try {\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/register-parent`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${getTokenFromCookie(\"idToken\")}`\n            },\n            body: JSON.stringify(parentData)\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to register parent\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = errorBody?.message || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            console.error(\"Error registering parent:\", errorMessage);\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data; // This includes user object and generatedPassword (if new)\n    } catch (error) {\n        console.error(\"Error in registerParent service:\", error);\n        throw new Error(error instanceof Error ? error.message : \"Failed to register parent\");\n    }\n}\nasync function deleteMultipleUsers(userIds) {\n    const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/delete-users`, {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${getTokenFromCookie(\"idToken\")}`\n        },\n        body: JSON.stringify({\n            ids: userIds\n        })\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting multiple users:\", response.statusText);\n        throw new Error(\"Failed to delete multiple users\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function deleteAllUsers() {\n    const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/delete-all-users`, {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${getTokenFromCookie(\"idToken\")}`\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting all users:\", response.statusText);\n        throw new Error(\"Failed to delete all users\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function getTotalUsers() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/total-users`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${token}`\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching total users:\", response.statusText);\n            throw new Error(\"Failed to fetch total users\");\n        }\n        const data = await response.json();\n        return data.totalUsers;\n    } catch (error) {\n        console.error(\"Error fetching total users:\", error);\n        throw error;\n    }\n}\nasync function getUserCountWithChange() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/users-count-change`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${token}`\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching user count change:\", response.statusText);\n            throw new Error(\"Failed to fetch user count change\");\n        }\n        const data = await response.json();\n        return {\n            totalUsersThisMonth: data.totalUsersThisMonth,\n            percentageChange: data.percentageChange\n        };\n    } catch (error) {\n        console.error(\"Error fetching user count change:\", error);\n        throw error;\n    }\n}\nasync function getMonthlyUserStarts() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/monthly-user-starts`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${token}`\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Failed to fetch monthly user stats:\", response.statusText);\n            throw new Error(\"Failed to fetch monthly user stats\");\n        }\n        const data = await response.json();\n        // data has type { [year: string]: MonthlyUserStat[] }\n        return data;\n    } catch (error) {\n        console.error(\"Error fetching monthly user stats:\", error);\n        throw error;\n    }\n}\nasync function resetParentPasswordService({ email, phone }) {\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"idToken\");\n    if (!token) {\n        throw new Error(\"You must be logged in to perform this action.\");\n    }\n    if (!email && !phone) {\n        throw new Error(\"Email or phone number is required to reset password.\");\n    }\n    const payload = {};\n    if (email) payload.email = email;\n    if (phone) payload.phone = phone;\n    const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/reset-parent-password`, {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${token}`\n        },\n        body: JSON.stringify(payload)\n    });\n    if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || \"Failed to reset password\");\n    }\n    const result = await response.json();\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/services/UserServices.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/utils/httpInterceptor.tsx":
/*!*******************************************!*\
  !*** ./src/app/utils/httpInterceptor.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticatedDelete: () => (/* binding */ authenticatedDelete),\n/* harmony export */   authenticatedFetch: () => (/* binding */ authenticatedFetch),\n/* harmony export */   authenticatedGet: () => (/* binding */ authenticatedGet),\n/* harmony export */   authenticatedPost: () => (/* binding */ authenticatedPost),\n/* harmony export */   authenticatedPut: () => (/* binding */ authenticatedPut),\n/* harmony export */   checkAuthStatus: () => (/* binding */ checkAuthStatus),\n/* harmony export */   clearSession: () => (/* binding */ clearSession),\n/* harmony export */   isTokenExpired: () => (/* binding */ isTokenExpired)\n/* harmony export */ });\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _services_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/AuthContext */ \"(ssr)/./src/app/services/AuthContext.tsx\");\n\n\n// Fonction pour gérer la déconnexion automatique\nconst handleUnauthorized = ()=>{\n    // Supprimer le token\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"idToken\");\n    // Rediriger vers login\n    if (false) {}\n};\n// Fonction pour obtenir le token\nconst getAuthToken = ()=>{\n    return js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"idToken\") || null;\n};\n// Intercepteur HTTP personnalisé\nconst authenticatedFetch = async (url, options = {})=>{\n    const token = getAuthToken();\n    // Si pas de token, rediriger immédiatement\n    if (!token) {\n        handleUnauthorized();\n        throw new Error(\"No authentication token found\");\n    }\n    // Ajouter le token aux headers\n    const headers = {\n        'Content-Type': 'application/json',\n        ...options.headers,\n        'Authorization': `Bearer ${token}`\n    };\n    // Construire l'URL complète si nécessaire\n    const fullUrl = url.startsWith('http') ? url : `${_services_AuthContext__WEBPACK_IMPORTED_MODULE_1__.BASE_API_URL}${url}`;\n    try {\n        const response = await fetch(fullUrl, {\n            ...options,\n            headers\n        });\n        // Vérifier si la réponse indique une erreur d'authentification\n        if (response.status === 401) {\n            console.warn(\"Token expired or invalid, redirecting to login\");\n            handleUnauthorized();\n            throw new Error(\"Authentication failed\");\n        }\n        // Vérifier si la réponse indique un token expiré\n        if (response.status === 403) {\n            const errorData = await response.clone().json().catch(()=>({}));\n            if (errorData.message?.includes('token') || errorData.message?.includes('expired')) {\n                console.warn(\"Token expired, redirecting to login\");\n                handleUnauthorized();\n                throw new Error(\"Token expired\");\n            }\n        }\n        return response;\n    } catch (error) {\n        // Si erreur réseau ou autre, vérifier si c'est lié à l'auth\n        if (error instanceof TypeError && error.message.includes('fetch')) {\n            console.error(\"Network error during authenticated request:\", error);\n        }\n        throw error;\n    }\n};\n// Wrapper pour les requêtes GET\nconst authenticatedGet = async (url)=>{\n    return authenticatedFetch(url, {\n        method: 'GET'\n    });\n};\n// Wrapper pour les requêtes POST\nconst authenticatedPost = async (url, data)=>{\n    return authenticatedFetch(url, {\n        method: 'POST',\n        body: JSON.stringify(data)\n    });\n};\n// Wrapper pour les requêtes PUT\nconst authenticatedPut = async (url, data)=>{\n    return authenticatedFetch(url, {\n        method: 'PUT',\n        body: JSON.stringify(data)\n    });\n};\n// Wrapper pour les requêtes DELETE\nconst authenticatedDelete = async (url, data)=>{\n    const options = {\n        method: 'DELETE'\n    };\n    if (data) {\n        options.body = JSON.stringify(data);\n    }\n    return authenticatedFetch(url, options);\n};\n// Fonction pour vérifier si l'utilisateur est toujours authentifié\nconst checkAuthStatus = async ()=>{\n    const token = getAuthToken();\n    if (!token) {\n        return false;\n    }\n    try {\n        // Faire une requête simple pour vérifier le token\n        const response = await authenticatedGet('/user/check-auth');\n        return response.ok;\n    } catch (error) {\n        console.error(\"Auth check failed:\", error);\n        return false;\n    }\n};\n// Fonction pour décoder et vérifier l'expiration du token JWT\nconst isTokenExpired = ()=>{\n    const token = getAuthToken();\n    if (!token) {\n        return true;\n    }\n    try {\n        // Décoder le token JWT (partie payload)\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        const currentTime = Math.floor(Date.now() / 1000);\n        // Vérifier si le token est expiré\n        return payload.exp < currentTime;\n    } catch (error) {\n        console.error(\"Error decoding token:\", error);\n        return true;\n    }\n};\n// Fonction pour nettoyer la session\nconst clearSession = ()=>{\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"idToken\");\n    if (false) {}\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/utils/httpInterceptor.tsx\n");

/***/ }),

/***/ "(ssr)/./src/utils/ThemeInitializer.tsx":
/*!****************************************!*\
  !*** ./src/utils/ThemeInitializer.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeInitializer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction ThemeInitializer() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"ThemeInitializer.useEffect\": ()=>{\n            if (localStorage.getItem(\"theme\") === \"dark\") {\n                document.documentElement.classList.add(\"dark\");\n            } else {\n                document.documentElement.classList.remove(\"dark\");\n            }\n        }\n    }[\"ThemeInitializer.useEffect\"], []);\n    return null; // This component only runs once to apply theme\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdXRpbHMvVGhlbWVJbml0aWFsaXplci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OzZEQUNrQztBQUVuQixTQUFTQztJQUN0QkQsZ0RBQVNBO3NDQUFDO1lBQ1IsSUFBSUUsYUFBYUMsT0FBTyxDQUFDLGFBQWEsUUFBUTtnQkFDNUNDLFNBQVNDLGVBQWUsQ0FBQ0MsU0FBUyxDQUFDQyxHQUFHLENBQUM7WUFDekMsT0FBTztnQkFDTEgsU0FBU0MsZUFBZSxDQUFDQyxTQUFTLENBQUNFLE1BQU0sQ0FBQztZQUM1QztRQUNGO3FDQUFHLEVBQUU7SUFFTCxPQUFPLE1BQU0sK0NBQStDO0FBQzlEIiwic291cmNlcyI6WyJEOlxcUHJvamV0XFxzY2hvbGFyaWZ5XFxkYXNoYm9hcmRcXHNyY1xcdXRpbHNcXFRoZW1lSW5pdGlhbGl6ZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5pbXBvcnQgeyB1c2VFZmZlY3QgfSBmcm9tIFwicmVhY3RcIjtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFRoZW1lSW5pdGlhbGl6ZXIoKSB7XHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChsb2NhbFN0b3JhZ2UuZ2V0SXRlbShcInRoZW1lXCIpID09PSBcImRhcmtcIikge1xyXG4gICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuY2xhc3NMaXN0LmFkZChcImRhcmtcIik7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuY2xhc3NMaXN0LnJlbW92ZShcImRhcmtcIik7XHJcbiAgICB9XHJcbiAgfSwgW10pO1xyXG5cclxuICByZXR1cm4gbnVsbDsgLy8gVGhpcyBjb21wb25lbnQgb25seSBydW5zIG9uY2UgdG8gYXBwbHkgdGhlbWVcclxufVxyXG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwiVGhlbWVJbml0aWFsaXplciIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJkb2N1bWVudCIsImRvY3VtZW50RWxlbWVudCIsImNsYXNzTGlzdCIsImFkZCIsInJlbW92ZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/ThemeInitializer.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/js-cookie","vendor-chunks/jwt-decode"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CProjet%5Cscholarify%5Cdashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjet%5Cscholarify%5Cdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();