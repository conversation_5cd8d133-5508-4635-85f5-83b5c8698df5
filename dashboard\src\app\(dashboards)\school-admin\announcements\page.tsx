"use client";

import { Megaphone } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import { Suspense, useEffect, useState } from "react";
import DataTableFix from "@/components/utils/TableFix";
import { useRouter } from "next/navigation";
import CreateAnnouncementModal from "./components/CreateAnnouncementModal";
import DeleteAnnouncementModal from "./components/DeleteAnnouncementModal";
import BulkDeleteModal from "@/components/modals/BulkDeleteModal";
import CircularLoader from "@/components/widgets/CircularLoader";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import { 
  AnnouncementSchema, 
  AnnouncementCreateSchema,
  getAnnouncementsBySchool,
  createAnnouncement,
  updateAnnouncement,
  deleteAnnouncement,
  deleteMultipleAnnouncements
} from "@/app/services/AnnouncementServices";
import { verifyPassword } from "@/app/services/UserServices";
import NotificationCard from "@/components/NotificationCard";
import { motion } from "framer-motion";

const BASE_URL = "/school-admin";

const navigation = {
  icon: Megaphone,
  baseHref: `${BASE_URL}/announcements`,
  title: "Announcements"
};

export default function Page() {
  const { logout, user } = useAuth();
  const router = useRouter();

  // State management
  const [announcements, setAnnouncements] = useState<AnnouncementSchema[]>([]);
  const [selectedAnnouncements, setSelectedAnnouncements] = useState<AnnouncementSchema[]>([]);
  const [loadingData, setLoadingData] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isBulkDeleteModalOpen, setIsBulkDeleteModalOpen] = useState(false);
  const [announcementToDelete, setAnnouncementToDelete] = useState<AnnouncementSchema | null>(null);
  const [announcementToEdit, setAnnouncementToEdit] = useState<AnnouncementSchema | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<"success" | "failure" | null>(null);
  const [bulkDeleteType, setBulkDeleteType] = useState<"selected" | "all">("selected");
  const [tableKey, setTableKey] = useState(0);

  // Notification state
  const [isNotificationCard, setIsNotificationCard] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState("");
  const [notificationType, setNotificationType] = useState<"success" | "error">("success");

  // Fetch announcements data
  useEffect(() => {
    const fetchAnnouncements = async () => {
      try {
        if (user && user.school_ids && user.school_ids.length > 0) {
          const schoolId = user.school_ids[0];
          const announcementsData = await getAnnouncementsBySchool(schoolId);
          setAnnouncements(announcementsData);
        }
      } catch (error) {
        console.error("Error fetching announcements:", error);
        setNotificationMessage("Failed to load announcements");
        setNotificationType("error");
        setIsNotificationCard(true);
      } finally {
        setLoadingData(false);
      }
    };

    if (user) {
      fetchAnnouncements();
    }
  }, [user]);

  // Handle create/edit announcement
  const handleSaveAnnouncement = async (announcementData: AnnouncementCreateSchema) => {
    setIsSubmitting(true);
    setSubmitStatus(null);

    try {
      if (user && user.school_ids && user.school_ids.length > 0) {
        const dataWithSchool = {
          ...announcementData,
          school_id: user.school_ids[0],
          author_id: user._id
        };

        if (announcementToEdit) {
          // Update existing announcement
          const updatedAnnouncement = await updateAnnouncement(announcementToEdit.announcement_id, dataWithSchool);
          setAnnouncements(prev => 
            prev.map(ann => ann._id === announcementToEdit._id ? updatedAnnouncement : ann)
          );
          setNotificationMessage("Announcement updated successfully!");
        } else {
          // Create new announcement
          const newAnnouncement = await createAnnouncement(dataWithSchool);
          setAnnouncements(prev => [newAnnouncement, ...prev]);
          setNotificationMessage("Announcement created successfully!");
        }

        setNotificationType("success");
        setIsNotificationCard(true);
        setSubmitStatus("success");

        // Close modal after success
        setTimeout(() => {
          setIsModalOpen(false);
          setAnnouncementToEdit(null);
          setSubmitStatus(null);
        }, 2000);
      }
    } catch (error) {
      console.error("Error saving announcement:", error);
      setNotificationMessage("Failed to save announcement");
      setNotificationType("error");
      setIsNotificationCard(true);
      setSubmitStatus("failure");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete single announcement
  const handleDelete = async (password: string) => {
    if (!announcementToDelete) return;

    setIsSubmitting(true);
    setSubmitStatus(null);

    // Verify password
    const passwordVerified = user ? await verifyPassword(password, user.email) : false;
    if (!passwordVerified) {
      setNotificationMessage("Invalid Password!");
      setNotificationType("error");
      setIsNotificationCard(true);
      setIsSubmitting(false);
      setSubmitStatus("failure");
      return;
    }

    try {
      await deleteAnnouncement(announcementToDelete.announcement_id);
      setAnnouncements(prev => prev.filter(ann => ann._id !== announcementToDelete._id));
      setNotificationMessage("Announcement deleted successfully!");
      setNotificationType("success");
      setIsNotificationCard(true);
      setSubmitStatus("success");

      // Close modal after success
      setTimeout(() => {
        setIsDeleteModalOpen(false);
        setAnnouncementToDelete(null);
        setSubmitStatus(null);
      }, 2000);
    } catch (error) {
      console.error("Error deleting announcement:", error);
      setNotificationMessage("Failed to delete announcement");
      setNotificationType("error");
      setIsNotificationCard(true);
      setSubmitStatus("failure");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle bulk delete
  const handleDeleteMultiple = async (selectedIds: string[]) => {
    if (selectedIds.length === 0) {
      setNotificationMessage("No announcements selected for deletion.");
      setNotificationType("error");
      setIsNotificationCard(true);
      return;
    }
    setBulkDeleteType("selected");
    setIsBulkDeleteModalOpen(true);
  };

  // Handle delete all
  const handleDeleteAll = async () => {
    if (announcements.length === 0) {
      setNotificationMessage("No announcements to delete.");
      setNotificationType("error");
      setIsNotificationCard(true);
      return;
    }
    setBulkDeleteType("all");
    setIsBulkDeleteModalOpen(true);
  };

  // Handle bulk delete confirmation
  const handleBulkDeleteConfirm = async (password: string) => {
    setIsSubmitting(true);
    setSubmitStatus(null);

    // Verify password
    const passwordVerified = user ? await verifyPassword(password, user.email) : false;
    if (!passwordVerified) {
      setNotificationMessage("Invalid Password!");
      setNotificationType("error");
      setIsNotificationCard(true);
      setIsSubmitting(false);
      setSubmitStatus("failure");
      return;
    }

    try {
      if (bulkDeleteType === "all") {
        // Delete all announcements for this school
        const announcementIds = announcements.map(ann => ann.announcement_id);
        await deleteMultipleAnnouncements(announcementIds);
        setAnnouncements([]);
        setNotificationMessage("All announcements deleted successfully!");
      } else {
        // Delete selected announcements
        const selectedIds = selectedAnnouncements.map(ann => ann.announcement_id);
        await deleteMultipleAnnouncements(selectedIds);
        setAnnouncements(prev => prev.filter(ann => !selectedIds.includes(ann.announcement_id)));
        setNotificationMessage(`${selectedAnnouncements.length} announcement(s) deleted successfully!`);
      }

      setNotificationType("success");
      setIsNotificationCard(true);
      setSubmitStatus("success");
      setSelectedAnnouncements([]);
      setTableKey(prev => prev + 1);

      // Close modal after success
      setTimeout(() => {
        setIsBulkDeleteModalOpen(false);
        setSubmitStatus(null);
      }, 2000);
    } catch (error) {
      console.error("Error in bulk delete:", error);
      setNotificationMessage("Failed to delete announcements");
      setNotificationType("error");
      setIsNotificationCard(true);
      setSubmitStatus("failure");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get priority badge color
  const getPriorityBadge = (priority: string) => {
    const colors = {
      urgent: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
      high: "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300",
      medium: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300",
      low: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300"
    };
    return colors[priority as keyof typeof colors] || colors.medium;
  };

  // Get status badge
  const getStatusBadge = (isPublished: boolean) => {
    return isPublished 
      ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
      : "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300";
  };

  // Table columns
  const columns = [
    { header: "Title", accessor: (row: AnnouncementSchema) => row.title },
    { 
      header: "Priority", 
      accessor: (row: AnnouncementSchema) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityBadge(row.priority)}`}>
          {row.priority.charAt(0).toUpperCase() + row.priority.slice(1)}
        </span>
      )
    },
    { 
      header: "Target", 
      accessor: (row: AnnouncementSchema) => (
        <span className="text-sm text-foreground/70">
          {row.target_audience.charAt(0).toUpperCase() + row.target_audience.slice(1)}
        </span>
      )
    },
    { 
      header: "Status", 
      accessor: (row: AnnouncementSchema) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadge(row.is_published)}`}>
          {row.is_published ? "Published" : "Draft"}
        </span>
      )
    },
    { 
      header: "Created", 
      accessor: (row: AnnouncementSchema) => 
        row.published_at ? new Date(row.published_at).toLocaleDateString() : "N/A"
    },
  ];

  // Table actions
  const actions = [
    {
      label: "View",
      onClick: (announcement: AnnouncementSchema) => {
        router.push(`${BASE_URL}/announcements/view?id=${announcement.announcement_id}`);
      },
    },
    {
      label: "Edit",
      onClick: (announcement: AnnouncementSchema) => {
        setAnnouncementToEdit(announcement);
        setIsModalOpen(true);
      },
    },
    {
      label: "Delete",
      onClick: (announcement: AnnouncementSchema) => {
        setAnnouncementToDelete(announcement);
        setIsDeleteModalOpen(true);
      },
    },
  ];

  return (
    <ProtectedRoute allowedRoles={["admin"]}>
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        <Suspense fallback={<CircularLoader />}>
          <div className="space-y-6">
            {/* Notification */}
            {isNotificationCard && (
              <NotificationCard
                message={notificationMessage}
                type={notificationType}
                onClose={() => setIsNotificationCard(false)}
                isVisible={isNotificationCard}
                isFixed={true}
              />
            )}

            {/* Header with Add Button */}
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-2xl font-bold text-foreground">Announcements</h1>
                <p className="text-foreground/60">Manage school announcements and communications</p>
              </div>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: 'spring', stiffness: 300 }}
                onClick={() => setIsModalOpen(true)}
                className="px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600"
              >
                Add New Announcement
              </motion.button>
            </div>

            {/* Modals */}
            {isModalOpen && (
              <CreateAnnouncementModal
                onClose={() => {
                  setIsModalOpen(false);
                  setAnnouncementToEdit(null);
                  setSubmitStatus(null);
                }}
                onSave={handleSaveAnnouncement}
                initialData={announcementToEdit}
                submitStatus={submitStatus}
                isSubmitting={isSubmitting}
              />
            )}

            {isDeleteModalOpen && announcementToDelete && (
              <DeleteAnnouncementModal
                announcementTitle={announcementToDelete.title}
                onClose={() => {
                  setIsDeleteModalOpen(false);
                  setAnnouncementToDelete(null);
                  setSubmitStatus(null);
                }}
                onDelete={handleDelete}
                submitStatus={submitStatus}
                isSubmitting={isSubmitting}
              />
            )}

            {isBulkDeleteModalOpen && (
              <BulkDeleteModal
                isOpen={isBulkDeleteModalOpen}
                onClose={() => {
                  setIsBulkDeleteModalOpen(false);
                  setSubmitStatus(null);
                }}
                onConfirm={handleBulkDeleteConfirm}
                title={bulkDeleteType === "all" ? "Delete All Announcements" : "Delete Selected Announcements"}
                message={
                  bulkDeleteType === "all"
                    ? `Are you sure you want to delete all announcements? This action cannot be undone.`
                    : `Are you sure you want to delete the selected announcements? This action cannot be undone.`
                }
                itemCount={bulkDeleteType === "all" ? announcements.length : selectedAnnouncements.length}
                itemType="announcements"
                isDeleteAll={bulkDeleteType === "all"}
                submitStatus={submitStatus}
                isSubmitting={isSubmitting}
                requirePassword={true}
              />
            )}

            {/* Data Table */}
            <DataTableFix<AnnouncementSchema>
              key={tableKey}
              columns={columns}
              data={announcements}
              actions={actions}
              defaultItemsPerPage={10}
              loading={loadingData}
              onLoadingChange={setLoadingData}
              onSelectionChange={setSelectedAnnouncements}
              handleDeleteMultiple={handleDeleteMultiple}
              handleDeleteAll={handleDeleteAll}
              idAccessor="_id"
              enableBulkActions={true}
            />
          </div>
        </Suspense>
      </SchoolLayout>
    </ProtectedRoute>
  );
}
