{"c": ["app/layout", "app/(auth)/login/page", "app/(dashboards)/school-admin/dashboard/page", "app/(dashboards)/school-admin/school/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/utils.mjs", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left-right.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-no-axes-gantt.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock-4.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check-2.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/megaphone.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/milestone.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/notebook-pen.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/percent.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-cog.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js", "(app-pages-browser)/./node_modules/recharts/es6/chart/BarChart.js", "(app-pages-browser)/./src/app/services/AnnouncementServices.tsx", "(app-pages-browser)/./src/app/services/ClassServices.tsx", "(app-pages-browser)/./src/app/services/TeacherServices.tsx", "(app-pages-browser)/./src/components/Dashboard/SidebarGroup.tsx", "(app-pages-browser)/./src/components/utils/TopClassesChart.tsx", "(app-pages-browser)/./src/components/widgets/NotificationCenter.tsx", "(app-pages-browser)/./src/components/widgets/RecentAnnouncements.tsx", "(app-pages-browser)/./src/components/widgets/SchoolPoints.tsx"]}