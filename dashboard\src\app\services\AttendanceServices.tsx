import { getTokenFromCookie } from "./UserServices";

const BASE_API_URL = process.env.BASE_API_URL || "https://scolarify.onrender.com/api";

export interface AttendanceRecord extends Record<string, unknown> {
  _id: string;
  student_name: string;
  student_id: string;
  class_name: string;
  subject_name: string;
  teacher_name: string;
  period_number: number;
  status: 'Present' | 'Absent' | 'Late' | 'Excused';
  date: string;
  academic_year: string;
}

export interface AttendanceStats {
  totalStudents: number;
  presentToday: number;
  absentToday: number;
  lateToday: number;
  excusedToday: number;
  attendanceRate: number;
}

export interface AttendanceFilters {
  date?: string;
  class_id?: string;
  status?: string;
  student_id?: string;
  start_date?: string;
  end_date?: string;
  page?: number;
  limit?: number;
}

export interface AttendanceResponse {
  attendance_records: AttendanceRecord[];
  pagination: {
    current_page: number;
    total_pages: number;
    total_records: number;
    per_page: number;
  };
  message: string;
}

// Get attendance records for a school with filters
export async function getAttendanceRecords(schoolId: string, filters: AttendanceFilters = {}): Promise<AttendanceResponse> {
  const token = getTokenFromCookie("idToken");

  try {
    // Build query string
    const queryParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const queryString = queryParams.toString();
    const url = `${BASE_API_URL}/attendance/school/${schoolId}${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching attendance records:", response.statusText);
      throw new Error("Failed to fetch attendance records");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Fetch attendance records error:", error);
    throw new Error("Failed to fetch attendance records");
  }
}

// Get attendance statistics for a school
export async function getAttendanceStats(schoolId: string, date?: string): Promise<{
  stats: AttendanceStats;
  date: string;
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const queryParams = new URLSearchParams();
    if (date) {
      queryParams.append('date', date);
    }

    const queryString = queryParams.toString();
    const url = `${BASE_API_URL}/attendance/school/${schoolId}/stats${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching attendance stats:", response.statusText);
      throw new Error("Failed to fetch attendance statistics");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Fetch attendance stats error:", error);
    throw new Error("Failed to fetch attendance statistics");
  }
}

// Create attendance record (using legacy route)
export async function createAttendance(attendanceData: any): Promise<{
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/attendance/create-attendance`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(attendanceData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error creating attendance:", errorData);
      throw new Error(errorData.message || "Failed to create attendance");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Create attendance error:", error);
    throw error;
  }
}

// Update attendance record (using legacy route)
export async function updateAttendance(attendanceId: string, attendanceData: any): Promise<{
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/attendance/update-attendance/${attendanceId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(attendanceData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error updating attendance:", errorData);
      throw new Error(errorData.message || "Failed to update attendance");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Update attendance error:", error);
    throw error;
  }
}

// Delete attendance record (using legacy route)
export async function deleteAttendance(attendanceId: string): Promise<{
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/attendance/delete-attendance/${attendanceId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error deleting attendance:", errorData);
      throw new Error(errorData.message || "Failed to delete attendance");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Delete attendance error:", error);
    throw error;
  }
}

// Delete multiple attendance records (using legacy route)
export async function deleteMultipleAttendances(attendanceIds: string[]): Promise<{
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/attendance/delete-attendances`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ ids: attendanceIds }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error deleting multiple attendances:", errorData);
      throw new Error(errorData.message || "Failed to delete attendances");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Delete multiple attendances error:", error);
    throw error;
  }
}
