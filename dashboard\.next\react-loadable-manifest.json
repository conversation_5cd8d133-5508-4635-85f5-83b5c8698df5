{"..\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts": {"id": "..\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts", "files": ["static/chunks/_app-pages-browser_node_modules_next_dist_client_dev_noop-turbopack-hmr_js.js"]}, "app\\(dashboards)\\teacher-dashboard\\attendance\\page.tsx -> @/app/services/TeacherAssignmentServices": {"id": "app\\(dashboards)\\teacher-dashboard\\attendance\\page.tsx -> @/app/services/TeacherAssignmentServices", "files": ["static/chunks/_app-pages-browser_src_app_services_TeacherAssignmentServices_tsx.js"]}, "app\\(dashboards)\\teacher-dashboard\\attendance\\page.tsx -> @/app/services/TeacherPermissionServices": {"id": "app\\(dashboards)\\teacher-dashboard\\attendance\\page.tsx -> @/app/services/TeacherPermissionServices", "files": []}, "app\\(dashboards)\\teacher-dashboard\\classes\\page.tsx -> @/app/services/TeacherPermissionServices": {"id": "app\\(dashboards)\\teacher-dashboard\\classes\\page.tsx -> @/app/services/TeacherPermissionServices", "files": []}}