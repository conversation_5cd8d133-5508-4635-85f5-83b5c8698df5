# Scholarify Dashboard

Ce projet est le tableau de bord d'administration pour la plateforme Scholarify, développé avec Next.js.

## Fonctionnalités

### Chatbot Intégré

Le dashboard inclut un chatbot interactif avec les caractéristiques suivantes :

- **Interface utilisateur moderne**
  - Widget flottant en bas à droite
  - Design responsive (mobile-first)
  - Thème couleur teal avec support du mode sombre/clair
  - Animations fluides avec Framer Motion
  - Scrollbars personnalisées

- **Fonctionnalités**
  - Switch entre mode technique et utilisateur
  - Défilement automatique vers les nouveaux messages
  - Indicateur de réflexion animé
  - Zone de saisie auto-redimensionnable
  - Historique des messages

- **Architecture technique**
  - Moteur de recherche Typesense
  - Composants React modulaires
  - Styles avec Tailwind CSS
  - Gestion d'état avec React hooks

## Installation

```bash
# Installation des dépendances
npm install

# Lancement du serveur de développement
npm run dev
```

## Structure du projet

Les principaux composants du chatbot se trouvent dans `src/components/chatbot/` :

- `ChatbotWidget.tsx` - Composant principal du chatbot
- `ChatHeader.tsx` - En-tête avec switch de mode
- `ChatMessage.tsx` - Affichage des messages
- `ChatInput.tsx` - Zone de saisie
- `ThinkingIndicator.tsx` - Animation de réflexion

## Configuration

Le projet utilise plusieurs plugins et configurations :

- Tailwind CSS avec plugin scrollbar
- Framer Motion pour les animations
- TypeScript pour le typage statique

## Prochaines étapes

- [ ] Intégration de l'API de traitement des messages
- [ ] Ajout de tests unitaires et d'intégration
- [ ] Documentation des composants avec Storybook
- [ ] Optimisation des performances

This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
#   a d m i n - p a n e l 
 
 #   d a s h b o a r d 
 
 