export interface PeriodSchema extends Record<string, unknown> {
  _id: string;
  school_id: string;
  period_number: number;
  start_time: string;
  end_time: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface PeriodCreateSchema extends Record<string, unknown> {
  period_number: number;
  start_time: string;
  end_time: string;
  school_id?: string;
}

export interface PeriodUpdateSchema extends Record<string, unknown> {
  period_number?: number;
  start_time?: string;
  end_time?: string;
}

export interface PeriodDeleteSchema extends Record<string, unknown> {
  _id: string;
  period_number: number;
}
