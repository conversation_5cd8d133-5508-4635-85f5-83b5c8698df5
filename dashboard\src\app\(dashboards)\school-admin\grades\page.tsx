"use client";

import { Percent, TrendingUp, Users, BookOpen, Filter, Download, Plus } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import { Suspense, useEffect, useState } from "react";
import DataTableFix from "@/components/utils/TableFix";

import CircularLoader from "@/components/widgets/CircularLoader";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import { motion } from "framer-motion";
import {
  getGradeRecords,
  getGradeStats,
  createGrade,
  updateGrade,
  deleteGrade,
  deleteMultipleGrades,
  GradeRecord,
  GradeStats
} from "@/app/services/GradeServices";
import { getStudentsBySchool } from "@/app/services/StudentServices";
import { getSubjectsBySchoolId } from "@/app/services/SubjectServices";
import { getExamTypes } from "@/app/services/ExamTypeServices";
import GradeModal from "@/components/modals/GradeModal";
import DeleteConfirmationModal from "@/components/modals/DeleteConfirmationModal";
import { useToast, ToastContainer } from "@/components/ui/Toast";



const navigation = {
  icon: Percent,
  baseHref: "/school-admin/grades",
  title: "Grades"
};

export default function GradesPage() {
  const { logout, user } = useAuth();
  const { toasts, removeToast, showSuccess, showError } = useToast();

  // State management
  const [gradeRecords, setGradeRecords] = useState<GradeRecord[]>([]);
  const [stats, setStats] = useState<GradeStats>({
    totalGrades: 0,
    averageScore: 0,
    highestScore: 0,
    lowestScore: 0,
    passRate: 0
  });
  const [loadingData, setLoadingData] = useState(true);
  const [selectedClass, setSelectedClass] = useState('all');
  const [selectedSubject, setSelectedSubject] = useState('all');
  const [selectedTerm, setSelectedTerm] = useState('all');
  const [selectedExamType, setSelectedExamType] = useState('all');

  // Modal states
  const [isGradeModalOpen, setIsGradeModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [gradeToEdit, setGradeToEdit] = useState<GradeRecord | null>(null);
  const [gradeToDelete, setGradeToDelete] = useState<GradeRecord | null>(null);
  const [deleteType, setDeleteType] = useState<"single" | "multiple">("single");
  const [selectedGrades, setSelectedGrades] = useState<GradeRecord[]>([]);

  // Additional data for forms
  const [students, setStudents] = useState<any[]>([]);
  const [subjects, setSubjects] = useState<any[]>([]);
  const [examTypes, setExamTypes] = useState<any[]>([]);

  // Loading states
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [clearSelection, setClearSelection] = useState(false);

  // Get school ID from user
  const schoolId = user?.school_ids?.[0] || user?.school_id;

  // Fetch grade data from API
  useEffect(() => {
    const fetchGradeData = async () => {
      if (!schoolId) {
        showError("Error", "No school ID found");
        setLoadingData(false);
        return;
      }

      try {
        setLoadingData(true);

        // Build filters
        const filters: any = {};
        if (selectedClass !== 'all') filters.class_id = selectedClass;
        if (selectedSubject !== 'all') filters.subject_id = selectedSubject;
        if (selectedTerm !== 'all') filters.term = selectedTerm;
        if (selectedExamType !== 'all') filters.exam_type_id = selectedExamType;

        // Fetch records and stats in parallel
        const [recordsResponse, statsResponse] = await Promise.all([
          getGradeRecords(schoolId as string, filters),
          getGradeStats(schoolId as string, filters)
        ]);

        setGradeRecords(recordsResponse.grade_records);
        setStats(statsResponse.stats);
      } catch (error) {
        console.error("Error fetching grade data:", error);
        showError("Error", "Failed to load grade data");
      } finally {
        setLoadingData(false);
      }
    };

    fetchGradeData();
  }, [schoolId, selectedClass, selectedSubject, selectedTerm, selectedExamType]);

  // Fetch additional data for modals
  useEffect(() => {
    const fetchAdditionalData = async () => {
      if (!schoolId) return;

      try {
        // Fetch data with individual error handling
        const results = await Promise.allSettled([
          getStudentsBySchool(schoolId as string),
          getSubjectsBySchoolId(schoolId as string),
          getExamTypes()
        ]);

        // Handle each result individually
        if (results[0].status === 'fulfilled') {
          setStudents(results[0].value);
        } else {
          console.error("Failed to fetch students:", results[0].reason);
          setStudents([]);
        }

        if (results[1].status === 'fulfilled') {
          setSubjects(results[1].value);
        } else {
          console.error("Failed to fetch subjects:", results[1].reason);
          setSubjects([]);
        }

        if (results[2].status === 'fulfilled') {
          setExamTypes(results[2].value);
        } else {
          console.error("Failed to fetch exam types:", results[2].reason);
          setExamTypes([]);
        }

        // Show warning if any critical data failed to load
        const anyDataFailed = results.some(result => result.status === 'rejected');
        if (anyDataFailed) {
          showError("Warning", "Some form data could not be loaded. Some features may be limited.");
        }
      } catch (error) {
        console.error("Error fetching additional data:", error);
        showError("Error", "Failed to load form data");
      }
    };

    fetchAdditionalData();
  }, [schoolId]); // Removed showError from dependencies

  // CRUD Functions
  const handleCreateGrade = () => {
    setGradeToEdit(null);
    setIsGradeModalOpen(true);
  };

  const handleEditGrade = (grade: GradeRecord) => {
    setGradeToEdit(grade);
    setIsGradeModalOpen(true);
  };

  const handleDeleteGrade = (grade: GradeRecord) => {
    setGradeToDelete(grade);
    setDeleteType("single");
    setIsDeleteModalOpen(true);
  };

  const handleDeleteMultiple = () => {
    setDeleteType("multiple");
    setIsDeleteModalOpen(true);
  };

  const handleSelectionChange = (selectedRows: GradeRecord[]) => {
    setSelectedGrades(selectedRows);
  };

  // Modal submission functions
  const handleGradeSubmit = async (data: any) => {
    if (!schoolId) {
      showError("Error", "No school ID found");
      return;
    }

    setIsSubmitting(true);
    try {
      if (gradeToEdit) {
        // Update existing grade
        await updateGrade(gradeToEdit._id, data);
      } else {
        // Create new grade
        await createGrade({ ...data, school_id: schoolId });
      }

      // Refresh grades list
      const filters: any = {};
      if (selectedClass !== 'all') filters.class_id = selectedClass;
      if (selectedSubject !== 'all') filters.subject_id = selectedSubject;
      if (selectedTerm !== 'all') filters.term = selectedTerm;
      if (selectedExamType !== 'all') filters.exam_type_id = selectedExamType;

      const [recordsResponse, statsResponse] = await Promise.all([
        getGradeRecords(schoolId as string, filters),
        getGradeStats(schoolId as string, filters)
      ]);

      setGradeRecords(recordsResponse.grade_records);
      setStats(statsResponse.stats);
      setIsGradeModalOpen(false);
      setGradeToEdit(null);

      // Show success notification
      showSuccess(
        gradeToEdit ? "Grade Updated" : "Grade Added",
        gradeToEdit ? "Grade has been updated successfully." : "New grade has been added successfully."
      );
    } catch (error) {
      console.error("Error submitting grade:", error);
      showError("Error", "Failed to save grade. Please try again.");
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteConfirm = async () => {
    if (!schoolId) return;

    try {
      if (deleteType === "single" && gradeToDelete) {
        await deleteGrade(gradeToDelete._id);
      } else if (deleteType === "multiple") {
        const selectedIds = selectedGrades.map(g => g._id);
        await deleteMultipleGrades(selectedIds);
        setClearSelection(true);
      }

      // Refresh grades list
      const filters: any = {};
      if (selectedClass !== 'all') filters.class_id = selectedClass;
      if (selectedSubject !== 'all') filters.subject_id = selectedSubject;
      if (selectedTerm !== 'all') filters.term = selectedTerm;
      if (selectedExamType !== 'all') filters.exam_type_id = selectedExamType;

      const [recordsResponse, statsResponse] = await Promise.all([
        getGradeRecords(schoolId as string, filters),
        getGradeStats(schoolId as string, filters)
      ]);

      setGradeRecords(recordsResponse.grade_records);
      setStats(statsResponse.stats);
      setIsDeleteModalOpen(false);
      setGradeToDelete(null);
      setSelectedGrades([]);

      // Show success notification
      if (deleteType === "single") {
        showSuccess("Grade Deleted", "Grade record has been deleted successfully.");
      } else {
        showSuccess("Grades Deleted", `${selectedGrades.length} grade records have been deleted successfully.`);
      }
    } catch (error) {
      console.error("Error deleting grade(s):", error);
      showError("Error", "Failed to delete grade record(s). Please try again.");
      throw error;
    }
  };

  const getGradeColor = (grade: string) => {
    switch (grade) {
      case 'A+':
      case 'A':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'B+':
      case 'B':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
      case 'C+':
      case 'C':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';
      case 'D':
      case 'F':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600 font-semibold';
    if (score >= 80) return 'text-blue-600 font-semibold';
    if (score >= 70) return 'text-yellow-600 font-semibold';
    if (score >= 60) return 'text-orange-600 font-semibold';
    return 'text-red-600 font-semibold';
  };

  // Table columns
  const columns = [
    { 
      header: "Student", 
      accessor: (row: GradeRecord) => (
        <div>
          <p className="font-medium text-foreground">{row.student_name}</p>
          <p className="text-sm text-foreground/60">{row.student_id}</p>
        </div>
      )
    },
    { 
      header: "Class", 
      accessor: (row: GradeRecord) => (
        <span className="text-sm font-medium">{row.class_name}</span>
      )
    },
    { 
      header: "Subject", 
      accessor: (row: GradeRecord) => (
        <span className="text-sm">{row.subject_name}</span>
      )
    },
    { 
      header: "Exam Type", 
      accessor: (row: GradeRecord) => (
        <span className="text-sm">{row.exam_type}</span>
      )
    },
    { 
      header: "Score", 
      accessor: (row: GradeRecord) => (
        <span className={`text-lg font-bold ${getScoreColor(row.score)}`}>
          {row.score}%
        </span>
      )
    },
    { 
      header: "Grade", 
      accessor: (row: GradeRecord) => (
        <span className={`px-2 py-1 rounded-full text-sm font-bold ${getGradeColor(row.grade)}`}>
          {row.grade}
        </span>
      )
    },
    { 
      header: "Term", 
      accessor: (row: GradeRecord) => (
        <span className="text-sm">{row.term}</span>
      )
    },
    // { 
    //   header: "Teacher", 
    //   accessor: (row: GradeRecord) => (
    //     <span className="text-sm">{row.teacher_name}</span>
    //   )
    // }
  ];

  // Actions for the table
  const actions = [
    {
      label: "Edit",
      onClick: (grade: GradeRecord) => {
        handleEditGrade(grade);
      },
    },
    {
      label: "Delete",
      onClick: (grade: GradeRecord) => {
        handleDeleteGrade(grade);
      },
    },
  ];

  // Filter data based on selections
  const filteredRecords = gradeRecords.filter(record => {
    if (selectedClass !== 'all' && record.class_name !== selectedClass) return false;
    if (selectedSubject !== 'all' && record.subject_name !== selectedSubject) return false;
    if (selectedTerm !== 'all' && record.term !== selectedTerm) return false;
    if (selectedExamType !== 'all' && record.exam_type !== selectedExamType) return false;
    return true;
  });

  if (loadingData) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <CircularLoader size={32} color="teal" />
      </div>
    );
  }

  return (
    <ProtectedRoute allowedRoles={["school_admin", "admin", "super"]}>
      <SchoolLayout navigation={navigation} onLogout={logout}>
        <div className="space-y-6">
          {/* Header */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-teal rounded-full flex items-center justify-center">
                  <Percent className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-foreground">Grades Management</h1>
                  <p className="text-foreground/60">
                    Monitor and manage student grades across all subjects
                  </p>
                </div>
              </div>
              
              <div className="text-right">
                <p className="text-2xl font-bold text-foreground">{stats.averageScore}%</p>
                <p className="text-sm text-foreground/60">Average Score</p>
              </div>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Total Grades</p>
                  <p className="text-2xl font-bold text-foreground">{stats.totalGrades}</p>
                </div>
                <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                  <BookOpen className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Average Score</p>
                  <p className="text-2xl font-bold text-foreground">{stats.averageScore}%</p>
                </div>
                <div className="w-10 h-10 bg-teal rounded-lg flex items-center justify-center">
                  <TrendingUp className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Highest Score</p>
                  <p className="text-2xl font-bold text-green-600">{stats.highestScore}%</p>
                </div>
                <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                  <TrendingUp className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Lowest Score</p>
                  <p className="text-2xl font-bold text-red-600">{stats.lowestScore}%</p>
                </div>
                <div className="w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center">
                  <Users className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Pass Rate</p>
                  <p className="text-2xl font-bold text-foreground">{stats.passRate}%</p>
                </div>
                <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                  <Percent className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex flex-wrap items-center gap-4">
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-foreground/60" />
                <span className="text-sm font-medium text-foreground">Filters:</span>
              </div>
              
              <div className="flex items-center space-x-2">
                <label className="text-sm text-foreground/70">Class:</label>
                <select
                  value={selectedClass}
                  onChange={(e) => setSelectedClass(e.target.value)}
                  className="px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground"
                >
                  <option value="all">All Classes</option>
                  <option value="Grade 10A">Grade 10A</option>
                  <option value="Grade 10B">Grade 10B</option>
                  <option value="Grade 11A">Grade 11A</option>
                  <option value="Grade 11B">Grade 11B</option>
                </select>
              </div>

              <div className="flex items-center space-x-2">
                <label className="text-sm text-foreground/70">Subject:</label>
                <select
                  value={selectedSubject}
                  onChange={(e) => setSelectedSubject(e.target.value)}
                  className="px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground"
                >
                  <option value="all">All Subjects</option>
                  <option value="Mathematics">Mathematics</option>
                  <option value="Physics">Physics</option>
                  <option value="Chemistry">Chemistry</option>
                  <option value="Biology">Biology</option>
                  <option value="English">English</option>
                </select>
              </div>

              <div className="flex items-center space-x-2">
                <label className="text-sm text-foreground/70">Term:</label>
                <select
                  value={selectedTerm}
                  onChange={(e) => setSelectedTerm(e.target.value)}
                  className="px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground"
                >
                  <option value="all">All Terms</option>
                  <option value="First Term">First Term</option>
                  <option value="Second Term">Second Term</option>
                  <option value="Third Term">Third Term</option>
                </select>
              </div>

              <div className="flex items-center space-x-2">
                <label className="text-sm text-foreground/70">Exam Type:</label>
                <select
                  value={selectedExamType}
                  onChange={(e) => setSelectedExamType(e.target.value)}
                  className="px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground"
                >
                  <option value="all">All Types</option>
                  <option value="Mid-term Exam">Mid-term Exam</option>
                  <option value="Final Exam">Final Exam</option>
                  <option value="Quiz">Quiz</option>
                  <option value="Lab Test">Lab Test</option>
                  <option value="Assignment">Assignment</option>
                </select>
              </div>

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 text-sm"
              >
                <Download size={14} />
                <span>Export</span>
              </motion.button>
            </div>
          </div>

          {/* Grades Table */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-foreground">
                Grade Records ({filteredRecords.length})
              </h2>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleCreateGrade}
                className="flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600"
              >
                <Plus size={16} />
                <span>Add Grade</span>
              </motion.button>
            </div>

            <Suspense fallback={<CircularLoader size={24} color="teal" />}>
              <DataTableFix<GradeRecord>
                data={filteredRecords}
                columns={columns}
                actions={actions}
                defaultItemsPerPage={15}
                onSelectionChange={handleSelectionChange}
                handleDeleteMultiple={handleDeleteMultiple}
                clearSelection={clearSelection}
                onSelectionCleared={() => setClearSelection(false)}
              />
            </Suspense>
          </div>
        </div>

        {/* Grade Modal */}
        <GradeModal
          isOpen={isGradeModalOpen}
          onClose={() => {
            setIsGradeModalOpen(false);
            setGradeToEdit(null);
          }}
          onSubmit={handleGradeSubmit}
          grade={gradeToEdit}
          students={students}
          subjects={subjects}
          examTypes={examTypes}
          loading={isSubmitting}
        />

        {/* Delete Confirmation Modal */}
        <DeleteConfirmationModal
          isOpen={isDeleteModalOpen}
          onClose={() => {
            setIsDeleteModalOpen(false);
            setGradeToDelete(null);
          }}
          onConfirm={handleDeleteConfirm}
          title={
            deleteType === "single"
              ? "Delete Grade Record"
              : "Delete Selected Grade Records"
          }
          message={
            deleteType === "single"
              ? "Are you sure you want to delete this grade record? This action cannot be undone."
              : `Are you sure you want to delete ${selectedGrades.length} selected grade records? This action cannot be undone.`
          }
          itemName={
            deleteType === "single" && gradeToDelete
              ? `${gradeToDelete.student_name} - ${gradeToDelete.subject_name} (${gradeToDelete.exam_type})`
              : undefined
          }
          itemCount={deleteType === "multiple" ? selectedGrades.length : undefined}
          type={deleteType}
        />

        {/* Toast Notifications */}
        <ToastContainer toasts={toasts} onClose={removeToast} />
      </SchoolLayout>
    </ProtectedRoute>
  );
}
