const Grade = require('../models/Grade'); // Assuming you have a Grade model
const { ensureUniqueId } = require('../utils/generateId'); 
const mongoose = require('mongoose');

const testGradeResponse = (req, res) => {
  res.status(200).json({ message: 'Hi, this is grade' });
};

// // Get all grade records
const getAllGrades = async (req, res) => {
  try {
    const grades = await Grade.find();
    res.json(grades);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// // Create a new grade record
const createGrade = async (req, res) => {
  try {
    // If user is a teacher, verify they can create grades for this class/subject
    if (req.user.role === 'teacher') {
      const { hasPermission } = require('../middleware/teacherMiddleware');

      // Check if teacher has permission to enter/edit grades
      if (!hasPermission(req, 'academic_records', 'enter_edit_grades_assigned_classes')) {
        return res.status(403).json({ message: 'You do not have permission to create grades' });
      }

      // Check if teacher is assigned to the class
      if (!req.teacher || !req.teacher.assigned_class_ids.includes(req.body.class_id)) {
        return res.status(403).json({ message: 'You are not authorized to create grades for this class' });
      }

      // Check if teacher is assigned to the subject (subjects are stored as strings)
      if (!req.teacher.assigned_subjects.includes(req.body.subject)) {
        return res.status(403).json({ message: 'You are not authorized to create grades for this subject' });
      }
    }

    const newGrade = new Grade(req.body);
    await newGrade.save();
    res.status(201).json({ message: 'Grade created successfully', grade: newGrade });
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
};

// // Get a grade record by ID
const getGradeById = async (req, res) => {
  const _id = new mongoose.Types.ObjectId(req.params.id);
  try {
    const grade = await Grade.findById(_id);
    if (!grade) {
      return res.status(404).json({ message: 'Grade record not found' });
    }
    res.json(grade);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// // Update grade record by ID
const updateGradeById = async (req, res) => {
  try {
    // First, get the existing grade to check permissions
    const existingGrade = await Grade.findById(req.params.id);
    if (!existingGrade) {
      return res.status(404).json({ message: 'Grade record not found' });
    }

    // If user is a teacher, verify they can update this grade
    if (req.user.role === 'teacher') {
      const { hasPermission } = require('../middleware/teacherMiddleware');

      // Check if teacher has permission to enter/edit grades
      if (!hasPermission(req, 'academic_records', 'enter_edit_grades_assigned_classes')) {
        return res.status(403).json({ message: 'You do not have permission to update grades' });
      }

      // Check if teacher is assigned to the class
      if (!req.teacher || !req.teacher.assigned_class_ids.includes(existingGrade.class_id.toString())) {
        return res.status(403).json({ message: 'You are not authorized to update grades for this class' });
      }

      // Note: Subject verification would need the actual subject name from the grade record
      // This might require populating the subject or storing subject name in the grade
    }

    const updatedGrade = await Grade.findByIdAndUpdate(req.params.id, req.body, { new: true });
    res.json({ message: 'Grade updated successfully', grade: updatedGrade });
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
};

// // Delete grade record by ID
const deleteGradeById = async (req, res) => {
  try {
    // First, get the existing grade to check permissions
    const existingGrade = await Grade.findById(req.params.id);
    if (!existingGrade) {
      return res.status(404).json({ message: 'Grade record not found' });
    }

    // If user is a teacher, verify they can delete this grade
    if (req.user.role === 'teacher') {
      const { hasPermission } = require('../middleware/teacherMiddleware');

      // Check if teacher has permission to enter/edit grades (includes delete)
      if (!hasPermission(req, 'academic_records', 'enter_edit_grades_assigned_classes')) {
        return res.status(403).json({ message: 'You do not have permission to delete grades' });
      }

      // Check if teacher is assigned to the class
      if (!req.teacher || !req.teacher.assigned_class_ids.includes(existingGrade.class_id.toString())) {
        return res.status(403).json({ message: 'You are not authorized to delete grades for this class' });
      }

      // Note: Subject verification would need the actual subject name from the grade record
    }

    await Grade.findByIdAndDelete(req.params.id);
    res.json({ message: 'Grade record deleted successfully' });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};


// Delete multiple grade records by IDs
const deleteMultipleGrades = async (req, res) => {
  const { ids } = req.body; // Expecting an array of grade IDs in the request body
  if (!Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({ message: 'Invalid input: ids should be a non-empty array' });
  }

  try {
    // Delete grade records where _id is in the provided array of IDs
    const result = await Grade.deleteMany({ _id: { $in: ids } });
    
    if (result.deletedCount === 0) {
      return res.status(404).json({ message: 'No grade records found for the provided IDs' });
    }
    
    res.json({ message: `${result.deletedCount} grade records deleted successfully` });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};
const getGradeRecords = async (req, res) => {
  try {
    const { school_id } = req.params;
    const {
      class_id,
      subject_id,
      term,
      exam_type_id,
      academic_year,
      student_id,
      page = 1,
      limit = 50
    } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Build filter object
    const filter = { school_id };

    // Apply teacher-specific filters using middleware helper
    const { applyTeacherFilters } = require('../middleware/teacherMiddleware');
    applyTeacherFilters(filter, req);

    if (class_id) filter.class_id = class_id;
    if (subject_id) filter.subject_id = subject_id;
    if (term) filter.term = term;
    if (exam_type_id) filter.exam_type = exam_type_id;
    if (academic_year) filter.academic_year = academic_year;
    if (student_id) filter.student_id = student_id;

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Get grade records with populated data
    const gradeRecords = await Grade.find(filter)
      .populate({
        path: 'student_id',
        select: 'first_name last_name student_id class_id',
        populate: {
          path: 'class_id',
          select: 'name grade_level'
        }
      })
      .populate('subject_id', 'name')
      .populate('exam_type', 'name')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    // Get total count for pagination
    const totalRecords = await Grade.countDocuments(filter);

    // Format the response
    const formattedRecords = gradeRecords.map(record => ({
      _id: record._id,
      student_name: record.student_id ?
        `${record.student_id.first_name} ${record.student_id.last_name}` : 'Unknown Student',
      student_id: record.student_id?.student_id || 'N/A',
      class_name: record.student_id?.class_id?.name || 'Unknown Class',
      subject_name: record.subject_id?.name || 'Unknown Subject',
      exam_type: record.exam_type?.name || 'Unknown Exam Type',
      term: record.term,
      academic_year: record.academic_year,
      score: record.score,
      grade: record.grade,
      comments: record.comments,
      date_entered: record.createdAt
    }));

    res.status(200).json({
      grade_records: formattedRecords,
      pagination: {
        current_page: parseInt(page),
        total_pages: Math.ceil(totalRecords / parseInt(limit)),
        total_records: totalRecords,
        per_page: parseInt(limit)
      },
      message: 'Grade records retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching grade records:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get grade statistics for a school
const getGradeStats = async (req, res) => {
  try {
    const { school_id } = req.params;
    const { term, academic_year, class_id, subject_id } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Build filter object
    const filter = { school_id };
    if (term) filter.term = term;
    if (academic_year) filter.academic_year = academic_year;
    if (class_id) filter.class_id = class_id;
    if (subject_id) filter.subject_id = subject_id;

    // Apply teacher-specific filters using middleware helper
    const { applyTeacherFilters } = require('../middleware/teacherMiddleware');
    applyTeacherFilters(filter, req);

    // Get grade statistics
    const gradeStats = await Grade.aggregate([
      { $match: filter },
      {
        $group: {
          _id: null,
          totalGrades: { $sum: 1 },
          averageScore: { $avg: '$score' },
          highestScore: { $max: '$score' },
          lowestScore: { $min: '$score' },
          passCount: {
            $sum: {
              $cond: [{ $gte: ['$score', 60] }, 1, 0]
            }
          }
        }
      }
    ]);

    const stats = gradeStats.length > 0 ? gradeStats[0] : {
      totalGrades: 0,
      averageScore: 0,
      highestScore: 0,
      lowestScore: 0,
      passCount: 0
    };

    // Calculate pass rate
    const passRate = stats.totalGrades > 0 ?
      Math.round((stats.passCount / stats.totalGrades) * 100) : 0;

    // Round average score
    stats.averageScore = Math.round(stats.averageScore || 0);

    res.status(200).json({
      stats: {
        totalGrades: stats.totalGrades,
        averageScore: stats.averageScore,
        highestScore: stats.highestScore || 0,
        lowestScore: stats.lowestScore || 0,
        passRate: passRate
      },
      message: 'Grade statistics retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching grade stats:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};


module.exports = {
  testGradeResponse,
  getAllGrades,
  createGrade,
  getGradeById,
  updateGradeById,
  deleteGradeById,
  deleteMultipleGrades,
  getGradeRecords,
  getGradeStats
};
