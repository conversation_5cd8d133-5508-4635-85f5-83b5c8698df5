# DataTable Improvements - Filament-like Bulk Actions

## Overview
Enhanced the DataTable component to provide intelligent bulk actions similar to Laravel Filament's behavior.

## New Features

### 1. Smart Bulk Action Display
- **Selection Info**: Shows "X of Y selected" when items are selected
- **Conditional Button Display**: Buttons appear/disappear based on selection state
- **Animated Interface**: Smooth animations for better UX

### 2. Selection Management
- **Select All**: Appears when partial selection exists
- **Deselect All**: Always available when items are selected
- **Dynamic Counts**: Shows exact numbers in buttons

### 3. Delete Operations
- **Delete Selected**: Shows when partial selection (not all items)
- **Delete All**: Only shows when ALL items are selected
- **API Integration**: Uses backend bulk delete endpoints

### 4. Dynamic Configuration
- **idAccessor**: Configurable identifier column (default: '_id')
- **enableBulkActions**: Toggle bulk actions on/off
- **handleDeleteMultiple**: Callback for selected items deletion
- **handleDeleteAll**: Callback for complete deletion

## Usage Example

```tsx
<DataTableFix<SchoolSchema>
  columns={columns}
  data={schools}
  actions={actions}
  defaultItemsPerPage={5}
  loading={loadingData}
  onLoadingChange={setLoadingData}
  onSelectionChange={setSelectedSchools}
  handleDeleteMultiple={handleDeleteMultiple}
  handleDeleteAll={handleDeleteAll}
  idAccessor="_id"
  enableBulkActions={true}
/>
```

## Backend Integration

### New Service Functions
- `deleteMultipleSchools(ids: string[])`: Delete specific schools
- `deleteAllSchools()`: Delete all schools (gets all IDs first)

### API Endpoints Used
- `DELETE /school/delete-schools`: Bulk delete endpoint
- Uses existing `deleteMultipleSchools` controller

## UI Behavior

### Selection States
1. **No Selection**: No bulk action bar
2. **Partial Selection**: Shows Select All, Deselect All, Delete Selected
3. **Full Selection**: Shows Deselect All, Delete All

### Visual Design
- Blue-themed selection bar with rounded corners
- Animated entrance/exit
- Clear visual hierarchy
- Responsive design

## Implementation Details

### Key Changes
1. **TableFix.tsx**: Enhanced with smart bulk actions
2. **SchoolServices.tsx**: Added bulk delete functions
3. **schools/page.tsx**: Integrated new functionality

### Props Added
- `idAccessor?: keyof T`: Specify identifier column
- `enableBulkActions?: boolean`: Enable/disable bulk actions
- `handleDeleteMultiple?: (ids: string[]) => void`: Bulk delete handler
- `handleDeleteAll?: () => void`: Delete all handler

## Benefits
- **Better UX**: Intuitive Filament-like interface
- **Flexible**: Configurable for different data types
- **Efficient**: Uses backend bulk operations
- **Responsive**: Works on all screen sizes
- **Accessible**: Clear visual feedback and states
