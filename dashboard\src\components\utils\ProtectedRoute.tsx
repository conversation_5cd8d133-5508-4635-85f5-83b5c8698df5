"use client";

import { useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import useAuth from "@/app/hooks/useAuth";
import CircularLoader from "../widgets/CircularLoader";

interface ProtectedRouteProps {
  children: React.ReactNode;
  allowedRoles?: string[];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children, allowedRoles = [] }) => {
  const { isAuthenticated, loading, setRedirectAfterLogin, user } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (!loading && !isAuthenticated) {
      // Stocker l'URL actuelle pour rediriger après la connexion
      setRedirectAfterLogin(pathname);
      router.push("/login");
    }

    if (user && allowedRoles.length > 0) {
      const hasRequiredRole = allowedRoles.some(role => 
        user.role?.toLowerCase() === role.toLowerCase()
      );
      
      if (!hasRequiredRole) {
        router.push('/unauthorized');
      }
    }
  }, [loading, isAuthenticated, router, pathname, setRedirectAfterLogin, user, allowedRoles]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen w-full absolute top-0 left-0 z-50">
        <CircularLoader size={40} color="teal" />
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // La redirection est gérée par useEffect
  }

  return <>{children}</>;
};

export default ProtectedRoute;