"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/attendance/page",{

/***/ "(app-pages-browser)/./src/app/(dashboards)/school-admin/attendance/page.tsx":
/*!***************************************************************!*\
  !*** ./src/app/(dashboards)/school-admin/attendance/page.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AttendancePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,FileCheck2,Filter,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check-2.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,FileCheck2,Filter,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,FileCheck2,Filter,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,FileCheck2,Filter,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,FileCheck2,Filter,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,FileCheck2,Filter,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,FileCheck2,Filter,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Dashboard/Layouts/SchoolLayout */ \"(app-pages-browser)/./src/components/Dashboard/Layouts/SchoolLayout.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_utils_TableFix__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/utils/TableFix */ \"(app-pages-browser)/./src/components/utils/TableFix.tsx\");\n/* harmony import */ var _components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/widgets/CircularLoader */ \"(app-pages-browser)/./src/components/widgets/CircularLoader.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var _components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/utils/ProtectedRoute */ \"(app-pages-browser)/./src/components/utils/ProtectedRoute.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/services/AttendanceServices */ \"(app-pages-browser)/./src/app/services/AttendanceServices.tsx\");\n/* harmony import */ var _app_services_StudentServices__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/services/StudentServices */ \"(app-pages-browser)/./src/app/services/StudentServices.tsx\");\n/* harmony import */ var _app_services_ClassServices__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/services/ClassServices */ \"(app-pages-browser)/./src/app/services/ClassServices.tsx\");\n/* harmony import */ var _app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/services/SubjectServices */ \"(app-pages-browser)/./src/app/services/SubjectServices.tsx\");\n/* harmony import */ var _app_services_ClassScheduleServices__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/services/ClassScheduleServices */ \"(app-pages-browser)/./src/app/services/ClassScheduleServices.tsx\");\n/* harmony import */ var _components_modals_AttendanceModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/modals/AttendanceModal */ \"(app-pages-browser)/./src/components/modals/AttendanceModal.tsx\");\n/* harmony import */ var _components_modals_DeleteConfirmationModal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/modals/DeleteConfirmationModal */ \"(app-pages-browser)/./src/components/modals/DeleteConfirmationModal.tsx\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst navigation = {\n    icon: _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n    baseHref: \"/school-admin/attendance\",\n    title: \"Attendance\"\n};\nfunction AttendancePage() {\n    var _user_school_ids;\n    _s();\n    const { logout, user } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const { toasts, removeToast, showSuccess, showError } = (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_14__.useToast)();\n    // State management\n    const [attendanceRecords, setAttendanceRecords] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        totalStudents: 0,\n        presentToday: 0,\n        absentToday: 0,\n        lateToday: 0,\n        excusedToday: 0,\n        attendanceRate: 0\n    });\n    const [loadingData, setLoadingData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(new Date().toISOString().split('T')[0]);\n    const [selectedClass, setSelectedClass] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    // Modal states\n    const [isAttendanceModalOpen, setIsAttendanceModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isDeleteModalOpen, setIsDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [attendanceToEdit, setAttendanceToEdit] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [attendanceToDelete, setAttendanceToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [deleteType, setDeleteType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"single\");\n    const [selectedAttendances, setSelectedAttendances] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Additional data for forms\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [schedules, setSchedules] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Loading states\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [clearSelection, setClearSelection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Get school ID from user\n    const schoolId = (user === null || user === void 0 ? void 0 : (_user_school_ids = user.school_ids) === null || _user_school_ids === void 0 ? void 0 : _user_school_ids[0]) || (user === null || user === void 0 ? void 0 : user.school_id);\n    // Fetch attendance data from API\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"AttendancePage.useEffect\": ()=>{\n            const fetchAttendanceData = {\n                \"AttendancePage.useEffect.fetchAttendanceData\": async ()=>{\n                    if (!schoolId) {\n                        showError(\"Error\", \"No school ID found\");\n                        setLoadingData(false);\n                        return;\n                    }\n                    try {\n                        setLoadingData(true);\n                        // Build filters\n                        const filters = {};\n                        if (selectedDate) filters.date = selectedDate;\n                        if (selectedClass !== 'all') filters.class_id = selectedClass;\n                        if (selectedStatus !== 'all') filters.status = selectedStatus;\n                        // Fetch records and stats in parallel\n                        const [recordsResponse, statsResponse] = await Promise.all([\n                            (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.getAttendanceRecords)(schoolId, filters),\n                            (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.getAttendanceStats)(schoolId, selectedDate)\n                        ]);\n                        setAttendanceRecords(recordsResponse.attendance_records);\n                        setStats(statsResponse.stats);\n                    } catch (error) {\n                        console.error(\"Error fetching attendance data:\", error);\n                        showError(\"Error\", \"Failed to load attendance data\");\n                    } finally{\n                        setLoadingData(false);\n                    }\n                }\n            }[\"AttendancePage.useEffect.fetchAttendanceData\"];\n            fetchAttendanceData();\n        }\n    }[\"AttendancePage.useEffect\"], [\n        schoolId,\n        selectedDate,\n        selectedClass,\n        selectedStatus\n    ]);\n    // Fetch additional data for modals\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"AttendancePage.useEffect\": ()=>{\n            const fetchAdditionalData = {\n                \"AttendancePage.useEffect.fetchAdditionalData\": async ()=>{\n                    if (!schoolId) return;\n                    try {\n                        // Fetch data with individual error handling\n                        const results = await Promise.allSettled([\n                            (0,_app_services_StudentServices__WEBPACK_IMPORTED_MODULE_8__.getStudentsBySchool)(schoolId),\n                            (0,_app_services_ClassServices__WEBPACK_IMPORTED_MODULE_9__.getClassesBySchool)(schoolId),\n                            (0,_app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_10__.getSubjectsBySchoolId)(schoolId),\n                            (0,_app_services_ClassScheduleServices__WEBPACK_IMPORTED_MODULE_11__.getClassSchedulesBySchool)(schoolId)\n                        ]);\n                        // Handle each result individually\n                        if (results[0].status === 'fulfilled') {\n                            setStudents(results[0].value);\n                        } else {\n                            console.error(\"Failed to fetch students:\", results[0].reason);\n                            setStudents([]);\n                        }\n                        if (results[1].status === 'fulfilled') {\n                            setClasses(results[1].value);\n                        } else {\n                            console.error(\"Failed to fetch classes:\", results[1].reason);\n                            setClasses([]);\n                        }\n                        if (results[2].status === 'fulfilled') {\n                            setSubjects(results[2].value);\n                        } else {\n                            console.error(\"Failed to fetch subjects:\", results[2].reason);\n                            setSubjects([]);\n                        }\n                        if (results[3].status === 'fulfilled') {\n                            setSchedules(results[3].value);\n                        } else {\n                            console.error(\"Failed to fetch schedules:\", results[3].reason);\n                            setSchedules([]);\n                        // Don't show error for schedules as it's not critical\n                        }\n                        // Only show error if critical data failed to load\n                        const criticalDataFailed = results[0].status === 'rejected' || results[1].status === 'rejected' || results[2].status === 'rejected';\n                        if (criticalDataFailed) {\n                            showError(\"Warning\", \"Some form data could not be loaded. Some features may be limited.\");\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching additional data:\", error);\n                        showError(\"Error\", \"Failed to load form data\");\n                    }\n                }\n            }[\"AttendancePage.useEffect.fetchAdditionalData\"];\n            fetchAdditionalData();\n        }\n    }[\"AttendancePage.useEffect\"], [\n        schoolId\n    ]); // Removed showError from dependencies\n    // CRUD Functions\n    const handleCreateAttendance = ()=>{\n        setAttendanceToEdit(null);\n        setIsAttendanceModalOpen(true);\n    };\n    const handleEditAttendance = (attendance)=>{\n        setAttendanceToEdit(attendance);\n        setIsAttendanceModalOpen(true);\n    };\n    const handleDeleteAttendance = (attendance)=>{\n        setAttendanceToDelete(attendance);\n        setDeleteType(\"single\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleDeleteMultiple = ()=>{\n        setDeleteType(\"multiple\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleSelectionChange = (selectedRows)=>{\n        setSelectedAttendances(selectedRows);\n    };\n    // Modal submission functions\n    const handleAttendanceSubmit = async (data)=>{\n        if (!schoolId) {\n            showError(\"Error\", \"No school ID found\");\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            if (attendanceToEdit) {\n                // Update existing attendance\n                await (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.updateAttendance)(attendanceToEdit._id, data);\n            } else {\n                // Create new attendance\n                await (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.createAttendance)({\n                    ...data,\n                    school_id: schoolId\n                });\n            }\n            // Refresh attendance list\n            const filters = {};\n            if (selectedDate) filters.date = selectedDate;\n            if (selectedClass !== 'all') filters.class_id = selectedClass;\n            if (selectedStatus !== 'all') filters.status = selectedStatus;\n            const [recordsResponse, statsResponse] = await Promise.all([\n                (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.getAttendanceRecords)(schoolId, filters),\n                (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.getAttendanceStats)(schoolId, selectedDate)\n            ]);\n            setAttendanceRecords(recordsResponse.attendance_records);\n            setStats(statsResponse.stats);\n            setIsAttendanceModalOpen(false);\n            setAttendanceToEdit(null);\n            // Show success notification\n            showSuccess(attendanceToEdit ? \"Attendance Updated\" : \"Attendance Marked\", attendanceToEdit ? \"Attendance record has been updated successfully.\" : \"Attendance has been marked successfully.\");\n        } catch (error) {\n            console.error(\"Error submitting attendance:\", error);\n            showError(\"Error\", \"Failed to save attendance. Please try again.\");\n            throw error;\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleDeleteConfirm = async ()=>{\n        if (!schoolId) return;\n        try {\n            if (deleteType === \"single\" && attendanceToDelete) {\n                await (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.deleteAttendance)(attendanceToDelete._id);\n            } else if (deleteType === \"multiple\") {\n                const selectedIds = selectedAttendances.map((a)=>a._id);\n                await (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.deleteMultipleAttendances)(selectedIds);\n                setClearSelection(true);\n            }\n            // Refresh attendance list\n            const filters = {};\n            if (selectedDate) filters.date = selectedDate;\n            if (selectedClass !== 'all') filters.class_id = selectedClass;\n            if (selectedStatus !== 'all') filters.status = selectedStatus;\n            const [recordsResponse, statsResponse] = await Promise.all([\n                (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.getAttendanceRecords)(schoolId, filters),\n                (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.getAttendanceStats)(schoolId, selectedDate)\n            ]);\n            setAttendanceRecords(recordsResponse.attendance_records);\n            setStats(statsResponse.stats);\n            setIsDeleteModalOpen(false);\n            setAttendanceToDelete(null);\n            setSelectedAttendances([]);\n            // Show success notification\n            if (deleteType === \"single\") {\n                showSuccess(\"Attendance Deleted\", \"Attendance record has been deleted successfully.\");\n            } else {\n                showSuccess(\"Attendances Deleted\", \"\".concat(selectedAttendances.length, \" attendance records have been deleted successfully.\"));\n            }\n        } catch (error) {\n            console.error(\"Error deleting attendance(s):\", error);\n            showError(\"Error\", \"Failed to delete attendance record(s). Please try again.\");\n            throw error;\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'Present':\n                return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';\n            case 'Absent':\n                return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';\n            case 'Late':\n                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';\n            case 'Excused':\n                return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';\n            default:\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';\n        }\n    };\n    // Table columns\n    const columns = [\n        {\n            header: \"Student\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-medium text-foreground\",\n                            children: row.student_name\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-foreground/60\",\n                            children: row.student_id\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Class\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-medium\",\n                    children: row.class_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 320,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Subject\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.subject_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 326,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Period\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: [\n                        \"Period \",\n                        row.period_number\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 332,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Teacher\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.teacher_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 338,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Status\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getStatusColor(row.status)),\n                    children: row.status\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 344,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Date\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: new Date(row.date).toLocaleDateString()\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    // Actions for the table\n    const actions = [\n        {\n            label: \"Edit\",\n            onClick: (attendance)=>{\n                handleEditAttendance(attendance);\n            }\n        },\n        {\n            label: \"Delete\",\n            onClick: (attendance)=>{\n                handleDeleteAttendance(attendance);\n            }\n        }\n    ];\n    console.log(\"attendanceRecords\", attendanceRecords);\n    // Filter data based on selections\n    const filteredRecords = attendanceRecords.filter((record)=>{\n        if (selectedClass !== 'all' && record.class_name !== selectedClass) return false;\n        if (selectedStatus !== 'all' && record.status !== selectedStatus) return false;\n        return true;\n    });\n    if (loadingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: 32,\n                color: \"teal\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                lineNumber: 383,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n            lineNumber: 382,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        allowedRoles: [\n            \"school_admin\",\n            \"admin\",\n            \"super\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            navigation: navigation,\n            onLogout: logout,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-teal rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: \"Attendance Management\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-foreground/60\",\n                                                        children: \"Monitor and track student attendance across all classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-foreground\",\n                                                children: [\n                                                    stats.attendanceRate,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-foreground/60\",\n                                                children: \"Today's Rate\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                            lineNumber: 393,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Total Students\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: stats.totalStudents\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Present Today\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-green-600\",\n                                                        children: stats.presentToday\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Absent Today\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-red-600\",\n                                                        children: stats.absentToday\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                    lineNumber: 440,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Late Today\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-yellow-600\",\n                                                        children: stats.lateToday\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-yellow-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                            lineNumber: 415,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-4 w-4 text-foreground/60\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-foreground\",\n                                                children: \"Filters:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Date:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                value: selectedDate,\n                                                onChange: (e)=>setSelectedDate(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Class:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedClass,\n                                                onChange: (e)=>setSelectedClass(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    classes.map((classItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: classItem._id,\n                                                            children: classItem.name\n                                                        }, classItem._id, false, {\n                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Status:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedStatus,\n                                                onChange: (e)=>setSelectedStatus(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Present\",\n                                                        children: \"Present\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Absent\",\n                                                        children: \"Absent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Late\",\n                                                        children: \"Late\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Excused\",\n                                                        children: \"Excused\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 510,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_20__.motion.button, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Export\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 520,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-foreground\",\n                                            children: [\n                                                \"Attendance Records (\",\n                                                filteredRecords.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_20__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            onClick: handleCreateAttendance,\n                                            className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Mark Attendance\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        size: 24,\n                                        color: \"teal\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 543,\n                                        columnNumber: 33\n                                    }, void 0),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_TableFix__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        data: filteredRecords,\n                                        columns: columns,\n                                        actions: actions,\n                                        defaultItemsPerPage: 15,\n                                        onSelectionChange: handleSelectionChange,\n                                        handleDeleteMultiple: handleDeleteMultiple,\n                                        clearSelection: clearSelection,\n                                        onSelectionCleared: ()=>setClearSelection(false)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 544,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                    lineNumber: 543,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                            lineNumber: 526,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 391,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_AttendanceModal__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    isOpen: isAttendanceModalOpen,\n                    onClose: ()=>{\n                        setIsAttendanceModalOpen(false);\n                        setAttendanceToEdit(null);\n                    },\n                    onSubmit: handleAttendanceSubmit,\n                    attendance: attendanceToEdit,\n                    students: students,\n                    classes: classes,\n                    subjects: subjects,\n                    schedules: schedules,\n                    loading: isSubmitting\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 559,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_DeleteConfirmationModal__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    isOpen: isDeleteModalOpen,\n                    onClose: ()=>{\n                        setIsDeleteModalOpen(false);\n                        setAttendanceToDelete(null);\n                    },\n                    onConfirm: handleDeleteConfirm,\n                    title: deleteType === \"single\" ? \"Delete Attendance Record\" : \"Delete Selected Attendance Records\",\n                    message: deleteType === \"single\" ? \"Are you sure you want to delete this attendance record? This action cannot be undone.\" : \"Are you sure you want to delete \".concat(selectedAttendances.length, \" selected attendance records? This action cannot be undone.\"),\n                    itemName: deleteType === \"single\" && attendanceToDelete ? \"\".concat(attendanceToDelete.student_name, \" - \").concat(attendanceToDelete.subject_name, \" (\").concat(new Date(attendanceToDelete.date).toLocaleDateString(), \")\") : undefined,\n                    itemCount: deleteType === \"multiple\" ? selectedAttendances.length : undefined,\n                    type: deleteType\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 575,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Toast__WEBPACK_IMPORTED_MODULE_14__.ToastContainer, {\n                    toasts: toasts,\n                    onClose: removeToast\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 602,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n            lineNumber: 390,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n        lineNumber: 389,\n        columnNumber: 5\n    }, this);\n}\n_s(AttendancePage, \"ANoYHScOC7tz7PVR3x5l6KpvzZA=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        _components_ui_Toast__WEBPACK_IMPORTED_MODULE_14__.useToast\n    ];\n});\n_c = AttendancePage;\nvar _c;\n$RefreshReg$(_c, \"AttendancePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboards)/school-admin/attendance/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/services/SubjectServices.tsx":
/*!**********************************************!*\
  !*** ./src/app/services/SubjectServices.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSubject: () => (/* binding */ createSubject),\n/* harmony export */   deleteAllSubjects: () => (/* binding */ deleteAllSubjects),\n/* harmony export */   deleteMultipleSubjects: () => (/* binding */ deleteMultipleSubjects),\n/* harmony export */   deleteSubject: () => (/* binding */ deleteSubject),\n/* harmony export */   getAllSubjects: () => (/* binding */ getAllSubjects),\n/* harmony export */   getSubjectById: () => (/* binding */ getSubjectById),\n/* harmony export */   getSubjectsByClassId: () => (/* binding */ getSubjectsByClassId),\n/* harmony export */   getSubjectsBySchoolId: () => (/* binding */ getSubjectsBySchoolId),\n/* harmony export */   updateSubject: () => (/* binding */ updateSubject)\n/* harmony export */ });\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./AuthContext */ \"(app-pages-browser)/./src/app/services/AuthContext.tsx\");\n/* harmony import */ var _UserServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./UserServices */ \"(app-pages-browser)/./src/app/services/UserServices.tsx\");\n\n\n// Get all subjects\nasync function getAllSubjects() {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const res = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/subject/get-subjects\"), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        }\n    });\n    if (!res.ok) throw new Error(\"Failed to fetch subjects\");\n    const data = await res.json();\n    return data.map((subject)=>({\n            _id: subject._id,\n            subject_id: subject.subject_id,\n            subject_code: subject.subject_code,\n            compulsory: subject.compulsory,\n            school_id: subject.school_id,\n            class_id: subject.class_id,\n            name: subject.name,\n            description: subject.description,\n            coefficient: subject.coefficient,\n            department: subject.department || \"\",\n            createdAt: subject.createdAt,\n            updatedAt: subject.updatedAt\n        }));\n}\n// Get subject by ID\nasync function getSubjectById(id) {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const res = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/subject/get-subject/\").concat(id), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        }\n    });\n    if (!res.ok) throw new Error(\"Failed to fetch subject\");\n    const subject = await res.json();\n    return {\n        _id: subject._id,\n        subject_id: subject.subject_id,\n        subject_code: subject.subject_code,\n        compulsory: subject.compulsory,\n        school_id: subject.school_id,\n        class_id: subject.class_id,\n        name: subject.name,\n        description: subject.description,\n        coefficient: subject.coefficient,\n        department: subject.department || \"\",\n        createdAt: subject.createdAt,\n        updatedAt: subject.updatedAt\n    };\n}\n// Create a new subject\nasync function createSubject(data) {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const res = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/subject/create-subject\"), {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        },\n        body: JSON.stringify(data)\n    });\n    if (!res.ok) {\n        let msg = \"Failed to create subject\";\n        try {\n            const err = await res.json();\n            msg = err.message || msg;\n        } catch (e) {}\n        throw new Error(msg);\n    }\n    return await res.json();\n}\n// Update subject by ID\nasync function updateSubject(id, data) {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const res = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/subject/update-subject/\").concat(id), {\n        method: \"PUT\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        },\n        body: JSON.stringify(data)\n    });\n    if (!res.ok) {\n        let msg = \"Failed to update subject\";\n        try {\n            const err = await res.json();\n            msg = err.message || msg;\n        } catch (e) {}\n        throw new Error(msg);\n    }\n    return await res.json();\n}\n// Delete subject by ID\nasync function deleteSubject(id) {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const res = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/subject/delete-subject/\").concat(id), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        }\n    });\n    if (!res.ok) {\n        let msg = \"Failed to delete subject\";\n        try {\n            const err = await res.json();\n            msg = err.message || msg;\n        } catch (e) {}\n        throw new Error(msg);\n    }\n    return await res.json();\n}\n// Delete multiple subjects by IDs\nasync function deleteMultipleSubjects(ids) {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const res = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/subject/delete-subjects\"), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        },\n        body: JSON.stringify({\n            ids\n        })\n    });\n    if (!res.ok) {\n        let msg = \"Failed to delete subjects\";\n        try {\n            const err = await res.json();\n            msg = err.message || msg;\n        } catch (e) {}\n        throw new Error(msg);\n    }\n    return await res.json();\n}\n// Delete all subjects\nasync function deleteAllSubjects() {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const res = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/subject/delete-all-subjects\"), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        }\n    });\n    if (!res.ok) {\n        throw new Error(\"Failed to delete all subjects\");\n    }\n    return await res.json();\n}\n// Get subjects by school ID\nasync function getSubjectsBySchoolId(schoolId) {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const res = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/subject/get-subject-by-school/\").concat(schoolId), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        }\n    });\n    if (!res.ok) throw new Error(\"Failed to fetch subjects by school ID\");\n    const data = await res.json();\n    return data.map((subject)=>({\n            _id: subject._id,\n            subject_id: subject.subject_id,\n            subject_code: subject.subject_code,\n            compulsory: subject.compulsory,\n            school_id: subject.school_id,\n            class_id: subject.class_id,\n            name: subject.name,\n            description: subject.description,\n            coefficient: subject.coefficient,\n            department: subject.department || \"\",\n            createdAt: subject.createdAt,\n            updatedAt: subject.updatedAt\n        }));\n}\n// Get subjects by class ID\nasync function getSubjectsByClassId(classId) {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const res = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/subject/get-subject-by-class/\").concat(classId), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        }\n    });\n    if (!res.ok) throw new Error(\"Failed to fetch subjects by class ID\");\n    const data = await res.json();\n    return data.map((subject)=>({\n            _id: subject._id,\n            subject_id: subject.subject_id,\n            subject_code: subject.subject_code,\n            compulsory: subject.compulsory,\n            school_id: subject.school_id,\n            class_id: subject.class_id,\n            name: subject.name,\n            description: subject.description,\n            coefficient: subject.coefficient,\n            department: subject.department || \"\",\n            createdAt: subject.createdAt,\n            updatedAt: subject.updatedAt\n        }));\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/services/SubjectServices.tsx\n"));

/***/ })

});