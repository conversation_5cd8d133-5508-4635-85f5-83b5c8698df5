"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/teacher-assignment/page",{

/***/ "(app-pages-browser)/./src/app/(dashboards)/school-admin/teacher-assignment/page.tsx":
/*!***********************************************************************!*\
  !*** ./src/app/(dashboards)/school-admin/teacher-assignment/page.tsx ***!
  \***********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TeacherAssignmentPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Filter,Plus,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Filter,Plus,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Filter,Plus,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Filter,Plus,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Filter,Plus,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Dashboard/Layouts/SchoolLayout */ \"(app-pages-browser)/./src/components/Dashboard/Layouts/SchoolLayout.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_utils_TableFix__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/utils/TableFix */ \"(app-pages-browser)/./src/components/utils/TableFix.tsx\");\n/* harmony import */ var _components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/widgets/CircularLoader */ \"(app-pages-browser)/./src/components/widgets/CircularLoader.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var _components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/utils/ProtectedRoute */ \"(app-pages-browser)/./src/components/utils/ProtectedRoute.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/services/TimetableServices */ \"(app-pages-browser)/./src/app/services/TimetableServices.tsx\");\n/* harmony import */ var _app_services_ClassServices__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/services/ClassServices */ \"(app-pages-browser)/./src/app/services/ClassServices.tsx\");\n/* harmony import */ var _app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/services/SubjectServices */ \"(app-pages-browser)/./src/app/services/SubjectServices.tsx\");\n/* harmony import */ var _app_services_TeacherServices__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/services/TeacherServices */ \"(app-pages-browser)/./src/app/services/TeacherServices.tsx\");\n/* harmony import */ var _app_services_PeriodServices__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/services/PeriodServices */ \"(app-pages-browser)/./src/app/services/PeriodServices.tsx\");\n/* harmony import */ var _components_modals_TeacherAssignmentModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/modals/TeacherAssignmentModal */ \"(app-pages-browser)/./src/components/modals/TeacherAssignmentModal.tsx\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst navigation = {\n    icon: _barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n    baseHref: \"/school-admin/teacher-assignment\",\n    title: \"Teacher Assignment\"\n};\nconst DAYS = [\n    'Monday',\n    'Tuesday',\n    'Wednesday',\n    'Thursday',\n    'Friday'\n];\nfunction TeacherAssignmentPage() {\n    var _user_school_ids;\n    _s();\n    const { logout, user } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const { toasts, removeToast, showSuccess, showError } = (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_13__.useToast)();\n    // State management\n    const [assignments, setAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loadingData, setLoadingData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [selectedClass, setSelectedClass] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedTeacher, setSelectedTeacher] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedDay, setSelectedDay] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    // Modal states\n    const [isAssignmentModalOpen, setIsAssignmentModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isDeleteModalOpen, setIsDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [assignmentToEdit, setAssignmentToEdit] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [assignmentToDelete, setAssignmentToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [deleteType, setDeleteType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"single\");\n    const [selectedAssignments, setSelectedAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Additional data for forms\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [teachers, setTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [periods, setPeriods] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Loading states\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [clearSelection, setClearSelection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Get school ID from user\n    const schoolId = (user === null || user === void 0 ? void 0 : (_user_school_ids = user.school_ids) === null || _user_school_ids === void 0 ? void 0 : _user_school_ids[0]) || (user === null || user === void 0 ? void 0 : user.school_id);\n    // Fetch assignment data from API\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"TeacherAssignmentPage.useEffect\": ()=>{\n            const fetchAssignmentData = {\n                \"TeacherAssignmentPage.useEffect.fetchAssignmentData\": async ()=>{\n                    if (!schoolId) {\n                        showError(\"Error\", \"No school ID found\");\n                        setLoadingData(false);\n                        return;\n                    }\n                    try {\n                        setLoadingData(true);\n                        // Build filters\n                        const filters = {};\n                        if (selectedClass !== 'all') filters.class_id = selectedClass;\n                        if (selectedTeacher !== 'all') filters.teacher_id = selectedTeacher;\n                        if (selectedDay !== 'all') filters.day_of_week = selectedDay;\n                        // Fetch assignments (timetable entries)\n                        const response = await (0,_app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_7__.getTimetable)(schoolId, filters);\n                        setAssignments(response.schedule_records);\n                    } catch (error) {\n                        console.error(\"Error fetching assignment data:\", error);\n                        showError(\"Error\", \"Failed to load assignment data\");\n                    } finally{\n                        setLoadingData(false);\n                    }\n                }\n            }[\"TeacherAssignmentPage.useEffect.fetchAssignmentData\"];\n            fetchAssignmentData();\n        }\n    }[\"TeacherAssignmentPage.useEffect\"], [\n        schoolId,\n        selectedClass,\n        selectedTeacher,\n        selectedDay\n    ]);\n    // Fetch additional data for modals\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"TeacherAssignmentPage.useEffect\": ()=>{\n            const fetchAdditionalData = {\n                \"TeacherAssignmentPage.useEffect.fetchAdditionalData\": async ()=>{\n                    if (!schoolId) return;\n                    try {\n                        // Fetch data with individual error handling\n                        const results = await Promise.allSettled([\n                            (0,_app_services_ClassServices__WEBPACK_IMPORTED_MODULE_8__.getClassesBySchool)(schoolId),\n                            (0,_app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_9__.getSubjectsBySchoolId)(schoolId),\n                            (0,_app_services_TeacherServices__WEBPACK_IMPORTED_MODULE_10__.getTeachersBySchool)(schoolId),\n                            (0,_app_services_PeriodServices__WEBPACK_IMPORTED_MODULE_11__.getPeriodsBySchool)(schoolId)\n                        ]);\n                        // Handle each result individually\n                        if (results[0].status === 'fulfilled') {\n                            setClasses(results[0].value);\n                        } else {\n                            console.error(\"Failed to fetch classes:\", results[0].reason);\n                            setClasses([]);\n                        }\n                        if (results[1].status === 'fulfilled') {\n                            setSubjects(results[1].value);\n                        } else {\n                            console.error(\"Failed to fetch subjects:\", results[1].reason);\n                            setSubjects([]);\n                        }\n                        if (results[2].status === 'fulfilled') {\n                            setTeachers(results[2].value);\n                        } else {\n                            console.error(\"Failed to fetch teachers:\", results[2].reason);\n                            setTeachers([]);\n                        }\n                        if (results[3].status === 'fulfilled') {\n                            setPeriods(results[3].value);\n                        } else {\n                            console.error(\"Failed to fetch periods:\", results[3].reason);\n                            setPeriods([]);\n                        }\n                        // Show warning if any critical data failed to load\n                        const anyDataFailed = results.some({\n                            \"TeacherAssignmentPage.useEffect.fetchAdditionalData.anyDataFailed\": (result)=>result.status === 'rejected'\n                        }[\"TeacherAssignmentPage.useEffect.fetchAdditionalData.anyDataFailed\"]);\n                        if (anyDataFailed) {\n                            showError(\"Warning\", \"Some form data could not be loaded. Some features may be limited.\");\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching additional data:\", error);\n                        showError(\"Error\", \"Failed to load form data\");\n                    }\n                }\n            }[\"TeacherAssignmentPage.useEffect.fetchAdditionalData\"];\n            fetchAdditionalData();\n        }\n    }[\"TeacherAssignmentPage.useEffect\"], [\n        schoolId\n    ]);\n    // CRUD Functions\n    const handleCreateAssignment = ()=>{\n        setAssignmentToEdit(null);\n        setIsAssignmentModalOpen(true);\n    };\n    const handleEditAssignment = (assignment)=>{\n        setAssignmentToEdit(assignment);\n        setIsAssignmentModalOpen(true);\n    };\n    const handleDeleteAssignment = (assignment)=>{\n        setAssignmentToDelete(assignment);\n        setDeleteType(\"single\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleDeleteMultiple = ()=>{\n        setDeleteType(\"multiple\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleSelectionChange = (selectedRows)=>{\n        setSelectedAssignments(selectedRows);\n    };\n    // Modal submission function\n    const handleAssignmentSubmit = async (data)=>{\n        if (!schoolId) {\n            showError(\"Error\", \"No school ID found\");\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            // Create new assignment\n            await (0,_app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_7__.createScheduleEntry)(schoolId, data);\n            // Refresh assignments list\n            const filters = {};\n            if (selectedClass !== 'all') filters.class_id = selectedClass;\n            if (selectedTeacher !== 'all') filters.teacher_id = selectedTeacher;\n            if (selectedDay !== 'all') filters.day_of_week = selectedDay;\n            const response = await (0,_app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_7__.getTimetable)(schoolId, filters);\n            setAssignments(response.schedule_records);\n            setIsAssignmentModalOpen(false);\n            setAssignmentToEdit(null);\n            // Show success notification\n            showSuccess(\"Assignment Created\", \"Teacher assignment has been created successfully.\");\n        } catch (error) {\n            console.error(\"Error submitting assignment:\", error);\n            showError(\"Error\", \"Failed to save assignment. Please try again.\");\n            throw error;\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const getDayColor = (day)=>{\n        const colors = {\n            Monday: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',\n            Tuesday: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',\n            Wednesday: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300',\n            Thursday: 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',\n            Friday: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300',\n            Saturday: 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300',\n            Sunday: 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'\n        };\n        return colors[day] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';\n    };\n    // Table columns\n    const columns = [\n        {\n            header: \"Teacher\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-medium text-foreground\",\n                        children: row.teacher_name\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Class\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-medium\",\n                    children: row.class_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Subject\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.subject_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Day\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getDayColor(row.day_of_week)),\n                    children: row.day_of_week\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Period\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-semibold\",\n                            children: [\n                                \"Period \",\n                                row.period_number\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-foreground/60\",\n                            children: [\n                                row.start_time.slice(0, 5),\n                                \" - \",\n                                row.end_time.slice(0, 5)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Type\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.schedule_type\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    // Actions for the table\n    const actions = [\n        {\n            label: \"Edit\",\n            onClick: (assignment)=>{\n                handleEditAssignment(assignment);\n            }\n        },\n        {\n            label: \"Delete\",\n            onClick: (assignment)=>{\n                handleDeleteAssignment(assignment);\n            }\n        }\n    ];\n    // Filter data based on selections\n    const filteredAssignments = assignments.filter((assignment)=>{\n        if (selectedClass !== 'all') {\n            var _classes_find;\n            // Find class name from classes array\n            const selectedClassName = (_classes_find = classes.find((c)=>c._id === selectedClass)) === null || _classes_find === void 0 ? void 0 : _classes_find.name;\n            if (assignment.class_name !== selectedClassName) return false;\n        }\n        if (selectedTeacher !== 'all') {\n            // Find teacher name from teachers array\n            const selectedTeacherObj = teachers.find((t)=>t._id === selectedTeacher);\n            const selectedTeacherName = selectedTeacherObj ? \"\".concat(selectedTeacherObj.first_name, \" \").concat(selectedTeacherObj.last_name) : '';\n            if (assignment.teacher_name !== selectedTeacherName) return false;\n        }\n        if (selectedDay !== 'all' && assignment.day_of_week !== selectedDay) return false;\n        return true;\n    });\n    if (loadingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: 32,\n                color: \"teal\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                lineNumber: 314,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n            lineNumber: 313,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        allowedRoles: [\n            \"school_admin\",\n            \"admin\",\n            \"super\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            navigation: navigation,\n            onLogout: logout,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-teal rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: \"Teacher Assignment\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-foreground/60\",\n                                                        children: \"Assign teachers to classes for specific periods and subjects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-foreground\",\n                                                children: assignments.length\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-foreground/60\",\n                                                children: \"Total Assignments\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Total Teachers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: teachers.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Total Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: classes.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Active Assignments\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: assignments.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Subjects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: subjects.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 text-foreground/60\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-foreground\",\n                                                children: \"Filters:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Class:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedClass,\n                                                onChange: (e)=>setSelectedClass(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    classes.map((classItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: classItem._id,\n                                                            children: classItem.name\n                                                        }, classItem._id, false, {\n                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Teacher:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedTeacher,\n                                                onChange: (e)=>setSelectedTeacher(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Teachers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    teachers.map((teacher)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: teacher._id,\n                                                            children: teacher.first_name && teacher.last_name ? \"\".concat(teacher.first_name, \" \").concat(teacher.last_name) : teacher.name || 'Unknown Teacher'\n                                                        }, teacher._id, false, {\n                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                            lineNumber: 429,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Day:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedDay,\n                                                onChange: (e)=>setSelectedDay(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Days\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    DAYS.map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: day,\n                                                            children: day\n                                                        }, day, false, {\n                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-foreground\",\n                                            children: [\n                                                \"Teacher Assignments (\",\n                                                filteredAssignments.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            onClick: handleCreateAssignment,\n                                            className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                    lineNumber: 469,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"New Assignment\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        size: 24,\n                                        color: \"teal\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 33\n                                    }, void 0),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_TableFix__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        data: filteredAssignments,\n                                        columns: columns,\n                                        actions: actions,\n                                        defaultItemsPerPage: 15,\n                                        onSelectionChange: handleSelectionChange,\n                                        handleDeleteMultiple: handleDeleteMultiple,\n                                        clearSelection: clearSelection,\n                                        onSelectionCleared: ()=>setClearSelection(false)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_TeacherAssignmentModal__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    isOpen: isAssignmentModalOpen,\n                    onClose: ()=>{\n                        setIsAssignmentModalOpen(false);\n                        setAssignmentToEdit(null);\n                    },\n                    onSubmit: handleAssignmentSubmit,\n                    assignment: assignmentToEdit,\n                    classes: classes,\n                    subjects: subjects,\n                    teachers: teachers,\n                    periods: periods,\n                    loading: isSubmitting\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                    lineNumber: 490,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DeleteConfirmationModal, {\n                    isOpen: isDeleteModalOpen,\n                    onClose: ()=>{\n                        setIsDeleteModalOpen(false);\n                        setAssignmentToDelete(null);\n                    },\n                    onConfirm: ()=>{},\n                    title: deleteType === \"single\" ? \"Delete Teacher Assignment\" : \"Delete Selected Assignments\",\n                    message: deleteType === \"single\" ? \"Are you sure you want to delete this teacher assignment? This action cannot be undone.\" : \"Are you sure you want to delete \".concat(selectedAssignments.length, \" selected assignments? This action cannot be undone.\"),\n                    itemName: deleteType === \"single\" && assignmentToDelete ? \"\".concat(assignmentToDelete.teacher_name, \" - \").concat(assignmentToDelete.class_name, \" (\").concat(assignmentToDelete.subject_name, \")\") : undefined,\n                    itemCount: deleteType === \"multiple\" ? selectedAssignments.length : undefined,\n                    type: deleteType\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                    lineNumber: 506,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Toast__WEBPACK_IMPORTED_MODULE_13__.ToastContainer, {\n                    toasts: toasts,\n                    onClose: removeToast\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                    lineNumber: 533,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n            lineNumber: 321,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n        lineNumber: 320,\n        columnNumber: 5\n    }, this);\n}\n_s(TeacherAssignmentPage, \"/n1j+j++YyVrhUTkcwfwhdPaJT8=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        _components_ui_Toast__WEBPACK_IMPORTED_MODULE_13__.useToast\n    ];\n});\n_c = TeacherAssignmentPage;\nvar _c;\n$RefreshReg$(_c, \"TeacherAssignmentPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboards)/school-admin/teacher-assignment/page.tsx\n"));

/***/ })

});