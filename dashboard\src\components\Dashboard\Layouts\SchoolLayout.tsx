"use client";
import React, { useState } from "react";
import {
  FileCheck2, School, Users, LayoutDashboard, ChartNoAxesGantt, Clock4,
  ArrowLeftRight, Percent, Coins, Presentation, NotebookPen, GraduationCap,
  Settings, BookOpen, DollarSign, UserPlus, Megaphone, UserCog, Menu, X,
  CreditCard,
  Milestone
} from "lucide-react";
import Divider from "../../widgets/Divider";
import SearchBox from "../../widgets/SearchBox";
import SidebarButton from "../SideNavButton";
import SidebarGroup from "../SidebarGroup";
import Avatar from "../Avatar";
import Logo from "../../widgets/Logo";
import GoPro from "../GoPro";
import NavigationBar from "../NavigationBar";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import Breadcrumbs from "../BreadCrums";

interface DashboardLayoutProps {
  navigation: {
    icon: React.ElementType;
    baseHref: string;
    title: string;
  };
  showGoPro?: boolean;
  onLogout: () => void;
  children: React.ReactNode;
}

const SchoolLayout: React.FC<DashboardLayoutProps> = ({
  navigation,
  showGoPro = true,
  onLogout,
  children,
}) => {
  const { user } = useAuth();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const avatar = {
    avatarUrl: user?.avatar || "https://img.freepik.com/free-psd/3d-illustration-person-with-sunglasses_23-2149436188.jpg",
    name: user?.name || "School Admin",
    role: user?.role || "admin",
  };
  const BASE_URL = "/school-admin";

  // Individual navigation items (not in groups)
  const individualNavItems = [
    { icon: LayoutDashboard, name: "Dashboard", href: `${BASE_URL}/dashboard` },
    { icon: School, name: "School", href: `${BASE_URL}/school` },
  ];

  // Grouped navigation items
  const navigationGroups = [
    {
      title: "People Management",
      icon: Users,
      items: [
        { icon: UserCog, name: "Staff", href: `${BASE_URL}/staff` },
        { icon: Users, name: "Teachers", href: `${BASE_URL}/teachers` },
        { icon: GraduationCap, name: "Students", href: `${BASE_URL}/students` },
        { icon: UserPlus, name: "Parents", href: `${BASE_URL}/parents` },
      ]
    },
    {
      title: "Academic Records",
      icon: BookOpen,
      items: [
        { icon: Percent, name: "Grades", href: `${BASE_URL}/grades` },
        { icon: Clock4, name: "Time Table", href: `${BASE_URL}/timetable` },
        { icon: ChartNoAxesGantt, name: "Periods", href: `${BASE_URL}/period` },
        { icon: FileCheck2, name: "Attendance", href: `${BASE_URL}/attendance` },
        { icon: NotebookPen, name: "Subjects", href: `${BASE_URL}/subjects` },
        { icon: Presentation, name: "Classes", href: `${BASE_URL}/classes` },
        { icon: Milestone, name: "Discipline", href: `${BASE_URL}/discipline` },
      ]
    },
    {
      title: "Communications",
      icon: Megaphone,
      items: [
        { icon: Megaphone, name: "Announcements", href: `${BASE_URL}/announcements` },
        { icon: BookOpen, name: "Resources", href: `${BASE_URL}/resources` },
      ]
    },
    {
      title: "Financial",
      icon: CreditCard,
      items: [
        { icon: DollarSign, name: "Fee Types", href: `${BASE_URL}/fees` },
        { icon: ArrowLeftRight, name: "Fee Transactions", href: `${BASE_URL}/transaction` },
        { icon: Coins, name: "Buy Credit", href: `${BASE_URL}/buy-credit` },
      ]
    }
  ];
    const settingsLink = {
    icon: Settings,
    name: "Settings",
    href: `${BASE_URL}/settings`
    };

  return (
    <ProtectedRoute>
      <div className="flex h-screen overflow-hidden sm:p-4">
        {/* Mobile Sidebar Toggle */}
        <button
          className="md:hidden p-2 bg-foreground text-background rounded-lg fixed top-4 left-4 z-50"
          onClick={() => setIsSidebarOpen(!isSidebarOpen)}
        >
          {isSidebarOpen ? <X size={24} /> : <Menu size={24} />}
        </button>

        {/* Sidebar */}
        <div
          className={`flex w-[290px] flex-col border border-gray-300 darK:border dark:border-gray-800 h-full shadow-lg p-2 rounded-lg fixed inset-y-0 left-0 z-40 bg-widget transition-transform lg:relative lg:translate-x-0 ${
            isSidebarOpen ? "translate-x-0" : "-translate-x-full"
          }`}
        >
          <div className="flex flex-col gap-3 overflow-auto subtle-scrollbar">
            <div className="flex flex-col items-center gap-2 my-4 ">
              <Logo />
              <Divider />
            </div>

            <SearchBox />

            <div className="flex flex-col gap-1">
              {/* Individual Navigation Items */}
              {individualNavItems.map((item) => (
                <SidebarButton
                  key={item.name}
                  icon={item.icon}
                  name={item.name}
                  href={item.href}
                />
              ))}

              {/* Divider */}
              <div className="my-2">
                <Divider />
              </div>

              {/* Grouped Navigation Items */}
              {navigationGroups.map((group) => (
                <SidebarGroup
                  key={group.title}
                  title={group.title}
                  icon={group.icon}
                  items={group.items}
                  defaultExpanded={group.title === "Academic Records"} // Expand Academic Records by default
                />
              ))}
            </div>
          </div>

          {/* Footer */}
          <div className="mt-auto flex flex-col gap-3">
            {showGoPro && <GoPro visible />}
            <SidebarButton
              icon={settingsLink.icon}
              name={settingsLink.name}
              href={settingsLink.href}
            />
            <Divider />
            <Avatar
              avatarUrl={avatar.avatarUrl}
              name={avatar.name}
              role={avatar.role}
              onLogout={onLogout}
            />
          </div>
        </div>

        {/* Main Content */}
      <div className="sm:px-6 px-2 py-2 w-full flex flex-col gap-4 lg:w-[80%] overflow-auto custom-scrollbar">
        <div className="sticky top-0 z-20 flex items-center justify-between   ">
          <NavigationBar
            icon={navigation.icon}
            baseHref={navigation.baseHref}
            title={navigation.title}
            isSidebarOpen={isSidebarOpen}
            toggleSidebar={() => setIsSidebarOpen(!isSidebarOpen)}
            onLogout={onLogout}
          />
        </div>
        <div className="flex lg:hidden flex-col gap-2">
          <Breadcrumbs baseHref={navigation.baseHref} icon={navigation.icon} />
          <p className="text-2xl font-semibold text-foreground">{navigation.title}</p>
        </div>
        <div className="">{children}</div>
      </div>
      </div>
    </ProtectedRoute>
  );
};

export default SchoolLayout;
