"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/services/SchoolServices.tsx":
/*!*********************************************!*\
  !*** ./src/app/services/SchoolServices.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSchool: () => (/* binding */ createSchool),\n/* harmony export */   deleteAllSchools: () => (/* binding */ deleteAllSchools),\n/* harmony export */   deleteMultipleSchools: () => (/* binding */ deleteMultipleSchools),\n/* harmony export */   deleteSchool: () => (/* binding */ deleteSchool),\n/* harmony export */   getSchoolById: () => (/* binding */ getSchoolById),\n/* harmony export */   getSchoolBy_id: () => (/* binding */ getSchoolBy_id),\n/* harmony export */   getSchoolCountChange: () => (/* binding */ getSchoolCountChange),\n/* harmony export */   getSchoolCredits: () => (/* binding */ getSchoolCredits),\n/* harmony export */   getSchoolPerformance: () => (/* binding */ getSchoolPerformance),\n/* harmony export */   getSchools: () => (/* binding */ getSchools),\n/* harmony export */   getTotalSchools: () => (/* binding */ getTotalSchools),\n/* harmony export */   updateSchool: () => (/* binding */ updateSchool)\n/* harmony export */ });\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./AuthContext */ \"(app-pages-browser)/./src/app/services/AuthContext.tsx\");\n/* harmony import */ var _UserServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./UserServices */ \"(app-pages-browser)/./src/app/services/UserServices.tsx\");\n\n\nasync function getSchools() {\n    try {\n        const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/get-schools\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching schools:\", response.statusText);\n            throw new Error(\"Failed to fetch schools data\");\n        }\n        const schoolsList = await response.json();\n        const schools = schoolsList.map((school)=>{\n            return {\n                _id: school._id,\n                school_id: school.school_id,\n                name: school.name,\n                email: school.email,\n                address: school.address,\n                website: school.website,\n                phone_number: school.phone_number,\n                principal_name: school.principal_name,\n                established_year: school.established_year,\n                description: school.description\n            };\n        });\n        return schools;\n    } catch (error) {\n        console.error(\"Error fetching schools:\", error);\n        throw new Error(\"Failed to fetch schools data\");\n    }\n}\nasync function getSchoolById(schoolId) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/get-school/\").concat(schoolId), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat((0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching school:\", response.statusText);\n        throw new Error(\"Failed to fetch school data\");\n    }\n    const data = await response.json();\n    const school = {\n        school_id: data.school_id,\n        credit: data.credit,\n        name: data.name,\n        email: data.email,\n        address: data.address,\n        website: data.website,\n        phone_number: data.phone_number,\n        principal_name: data.principal_name,\n        established_year: data.established_year,\n        description: data.description\n    };\n    return school;\n}\nasync function getSchoolBy_id(schoolId) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/get-school_id/\").concat(schoolId), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat((0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching school:\", response.statusText);\n        throw new Error(\"Failed to fetch school data\");\n    }\n    const data = await response.json();\n    const school = {\n        _id: data._id,\n        school_id: data.school_id,\n        credit: data.credit,\n        name: data.name,\n        email: data.email,\n        address: data.address,\n        website: data.website,\n        phone_number: data.phone_number,\n        principal_name: data.principal_name,\n        established_year: data.established_year,\n        description: data.description\n    };\n    return school;\n}\nasync function createSchool(schoolData) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/create-school\"), {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat((0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\"))\n        },\n        body: JSON.stringify(schoolData)\n    });\n    if (!response.ok) {\n        console.error(\"Error creating school:\", response.statusText);\n        throw new Error(\"Failed to create school data\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function updateSchool(schoolId, schoolData) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/update-school/\").concat(schoolId), {\n        method: \"PUT\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat((0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\"))\n        },\n        body: JSON.stringify(schoolData)\n    });\n    if (!response.ok) {\n        console.error(\"Error updating school:\", response.statusText);\n        throw new Error(\"Failed to update school data\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function deleteSchool(schoolId) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/delete-school/\").concat(schoolId), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat((0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting school:\", response.statusText);\n        throw new Error(\"Failed to delete school data\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function deleteMultipleSchools(schoolIds) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/delete-schools\"), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat((0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\"))\n        },\n        body: JSON.stringify({\n            ids: schoolIds\n        })\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting multiple schools:\", response.statusText);\n        throw new Error(\"Failed to delete multiple schools\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function deleteAllSchools() {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/delete-all-schools\"), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat((0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting all schools:\", response.statusText);\n        throw new Error(\"Failed to delete all schools\");\n    }\n    const data = await response.json();\n    return data;\n}\n// Get total number of schools\nasync function getTotalSchools() {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/total-schools\"), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching total schools:\", response.statusText);\n        throw new Error(\"Failed to fetch total schools\");\n    }\n    const data = await response.json();\n    return data.totalSchools;\n}\n// Get schools created this month and percentage change from last month\nasync function getSchoolCountChange() {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/schools-count-change\"), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching school count change:\", response.statusText);\n        throw new Error(\"Failed to fetch school count change\");\n    }\n    const data = await response.json();\n    return {\n        totalThisMonth: data.totalSchoolsThisMonth,\n        percentageChange: data.percentageChange\n    };\n}\nasync function getSchoolPerformance() {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/performance\"), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching performance metrics:\", response.statusText);\n        throw new Error(\"Failed to fetch school performance metrics\");\n    }\n    const data = await response.json();\n    return data;\n}\n// Get school credits/points\nasync function getSchoolCredits(schoolId) {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/get-school_id/\").concat(schoolId), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching school credits:\", response.statusText);\n        throw new Error(\"Failed to fetch school credits\");\n    }\n    const data = await response.json();\n    return data.credit || 0;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/services/SchoolServices.tsx\n"));

/***/ })

});