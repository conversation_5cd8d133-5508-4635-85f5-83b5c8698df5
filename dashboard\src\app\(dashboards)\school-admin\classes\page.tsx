"use client";

import { Presentation } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import CircularLoader from "@/components/widgets/CircularLoader";
import React, { Suspense, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import useAuth from "@/app/hooks/useAuth";
import DataTableFix from "@/components/utils/TableFix";
import { getClasses } from "@/app/services/ClassServices";
import { ClassSchema } from "@/app/models/ClassModel";
import { motion } from "framer-motion";
import NotificationCard from "@/components/NotificationCard";
import { createSuccessNotification, createErrorNotification, NotificationState } from "@/app/types/notification";
import PageWrapper from "@/components/Dashboard/ReusableComponents/ClassComponent";
import { getSchoolBy_id } from "@/app/services/SchoolServices";
import { SchoolSchema } from "@/app/models/SchoolModel";

const BASE_URL = "/school-admin";

const navigation = {
  icon: Presentation,
  baseHref: `${BASE_URL}/classes`,
  title: "Classes"
};

export default function Page() {
  const { logout } = useAuth();
  const { user } = useAuth();
  return (
    <Suspense fallback={
      <div>
        <div className="flex justify-center items-center h-screen absolute top-0 left-0 z-50">
          <CircularLoader size={32} color="teal" />
        </div>
      </div>
    }>
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        {/* You might want to pass schoolId from somewhere here */}
        {user && <PageWrapper user={user} />}
      </SchoolLayout>
    </Suspense>
  );
}
