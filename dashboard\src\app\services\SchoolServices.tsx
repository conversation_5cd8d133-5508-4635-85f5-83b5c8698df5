import { BASE_API_URL } from "./AuthContext";
import { getTokenFromCookie } from "./UserServices";
import { SchoolCreateSchema, SchoolSchema, SchoolUpdateSchema } from "../models/SchoolModel";
export async function getSchools() {
    try {
        const token = getTokenFromCookie("idToken");
        const response = await fetch(`${BASE_API_URL}/school/get-schools`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
            },
        });
        if (!response.ok) {
            console.error("Error fetching schools:", response.statusText);
            throw new Error("Failed to fetch schools data");
        }
        const schoolsList = await response.json();
        const schools = schoolsList.map((school: any) => {
            return {
                _id:school._id,
                school_id: school.school_id,
                name: school.name,
                email: school.email,
                address: school.address,
                website: school.website,
                phone_number: school.phone_number,
                principal_name: school.principal_name,
                established_year: school.established_year,
                description: school.description,

            } as SchoolSchema;
        })
        return  schools;
        
    } catch (error) {
        console.error("Error fetching schools:", error);
        throw new Error("Failed to fetch schools data");
        
    }
}

export async function getSchoolById(schoolId: string) {
    const response = await fetch(`${BASE_API_URL}/school/get-school/${schoolId}`, {
        method: "GET",
        headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
        },
    });
    if (!response.ok) {
        console.error("Error fetching school:", response.statusText);
        throw new Error("Failed to fetch school data");
    }
    const data = await response.json();

    const school = {
        school_id: data.school_id,
        credit: data.credit,
        name: data.name,
        email: data.email,
        address: data.address,
        website: data.website,
        phone_number: data.phone_number,
        principal_name: data.principal_name,
        established_year: data.established_year,
        description: data.description,
    } as SchoolSchema;
    return school;
}

export async function getSchoolBy_id(schoolId: string) {
    const response = await fetch(`${BASE_API_URL}/school/get-school_id/${schoolId}`, {
        method: "GET",
        headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
        },
    });
    if (!response.ok) {
        console.error("Error fetching school:", response.statusText);
        throw new Error("Failed to fetch school data");
    }
    const data = await response.json();

    const school = {
        _id: data._id,
        school_id: data.school_id,
        credit: data.credit,
        name: data.name,
        email: data.email,
        address: data.address,
        website: data.website,
        phone_number: data.phone_number,
        principal_name: data.principal_name,
        established_year: data.established_year,
        description: data.description,
    } as SchoolSchema;
    return school;
}

export async function createSchool(schoolData: SchoolCreateSchema) {
    const response = await fetch(`${BASE_API_URL}/school/create-school`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
        },
        body: JSON.stringify(schoolData),
    });
    if (!response.ok) {
        console.error("Error creating school:", response.statusText);
        throw new Error("Failed to create school data");
    }
    const data = await response.json();
    return data;
}

export async function updateSchool(schoolId: string, schoolData: SchoolUpdateSchema) {
    const response = await fetch(`${BASE_API_URL}/school/update-school/${schoolId}`, {
        method: "PUT",
        headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
        },
        body: JSON.stringify(schoolData),
    });
    if (!response.ok) {
        console.error("Error updating school:", response.statusText);
        throw new Error("Failed to update school data");
    }
    const data = await response.json();
    return data;
}

export async function deleteSchool(schoolId: string) {
    const response = await fetch(`${BASE_API_URL}/school/delete-school/${schoolId}`, {
        method: "DELETE",
        headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
        },
    });
    if (!response.ok) {
        console.error("Error deleting school:", response.statusText);
        throw new Error("Failed to delete school data");
    }
    const data = await response.json();
    return data;
}

export async function deleteMultipleSchools(schoolIds: string[]) {
    const response = await fetch(`${BASE_API_URL}/school/delete-schools`, {
        method: "DELETE",
        headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
        },
        body: JSON.stringify({ ids: schoolIds }),
    });
    if (!response.ok) {
        console.error("Error deleting multiple schools:", response.statusText);
        throw new Error("Failed to delete multiple schools");
    }
    const data = await response.json();
    return data;
}

export async function deleteAllSchools() {
    const response = await fetch(`${BASE_API_URL}/school/delete-all-schools`, {
        method: "DELETE",
        headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
        },
    });
    if (!response.ok) {
        console.error("Error deleting all schools:", response.statusText);
        throw new Error("Failed to delete all schools");
    }
    const data = await response.json();
    return data;
}


interface TotalSchoolsResponse {
  totalSchools: number;
}

interface SchoolCountChangeResponse {
  totalSchoolsThisMonth: number;
  percentageChange: number;
}

// Get total number of schools
export async function getTotalSchools(): Promise<number> {
  const token = getTokenFromCookie("idToken");

  const response = await fetch(`${BASE_API_URL}/school/total-schools`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    console.error("Error fetching total schools:", response.statusText);
    throw new Error("Failed to fetch total schools");
  }

  const data: TotalSchoolsResponse = await response.json();
  return data.totalSchools;
}

// Get schools created this month and percentage change from last month
export async function getSchoolCountChange(): Promise<{
  totalThisMonth: number;
  percentageChange: number;
}> {
  const token = getTokenFromCookie("idToken");

  const response = await fetch(`${BASE_API_URL}/school/schools-count-change`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    console.error("Error fetching school count change:", response.statusText);
    throw new Error("Failed to fetch school count change");
  }

  const data: SchoolCountChangeResponse = await response.json();

  return {
    totalThisMonth: data.totalSchoolsThisMonth,
    percentageChange: data.percentageChange,
  };
}

interface SchoolPerformance {
  id: string;
  schoolName: string;
  metrics: {
    "Total number Of credits ever bought": number;
    "Overall Average Grade": number;
  };
}

export async function getSchoolPerformance(): Promise<SchoolPerformance[]> {
  const token = getTokenFromCookie("idToken");

  const response = await fetch(`${BASE_API_URL}/school/performance`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    console.error("Error fetching performance metrics:", response.statusText);
    throw new Error("Failed to fetch school performance metrics");
  }

  const data: SchoolPerformance[] = await response.json();
  return data;
}