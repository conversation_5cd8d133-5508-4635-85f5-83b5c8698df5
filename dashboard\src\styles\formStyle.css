
.asideLogo{
    display: none;
}

.asideForm {
    width: 100%;
}
.navigation-bars{
    width: 100%;
}
.navigation-bars .navigationItem{
    height:5px;
    width: 100%;
    background: #8e9eac;
    border-radius: 20px;
}
.navigation-bars .navigationItem.active{
    background: #17b890;
}

/* Animation de vibration */
@keyframes shake {
    0% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    50% { transform: translateX(5px); }
    75% { transform: translateX(-5px); }
    100% { transform: translateX(0); }
  }
  
.animate-shake {
animation: shake 0.3s ease-in-out;
}

@media (min-width: 1024px) {
    .asideLogo {
        width: 50%;
        display: block;
    }
    .asideForm {
        width: 50%;
    }
    
}

