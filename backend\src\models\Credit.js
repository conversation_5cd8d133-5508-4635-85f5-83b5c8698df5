const mongoose = require('mongoose');

const creditSchema = new mongoose.Schema({
  student_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Student',
    required: true
  },
  school_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'School',
    required: true
  },
  academicYear_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'AcademicYear',
    required: true
  },
  amountPaid: {
    type: Number,
    required: true,
    default: 0
  },
  paidAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Fix: use the correct snake_case field names in the index
creditSchema.index({ student_id: 1, academicYear_id: 1 }, { unique: true });

const Credit = mongoose.models.Credit || mongoose.model('Credit', creditSchema);

module.exports = Credit;
