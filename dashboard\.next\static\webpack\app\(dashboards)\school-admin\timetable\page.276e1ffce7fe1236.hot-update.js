"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/timetable/page",{

/***/ "(app-pages-browser)/./src/app/(dashboards)/school-admin/timetable/page.tsx":
/*!**************************************************************!*\
  !*** ./src/app/(dashboards)/school-admin/timetable/page.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TimetablePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock4,Edit,Filter,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock-4.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock4,Edit,Filter,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock4,Edit,Filter,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock4,Edit,Filter,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock4,Edit,Filter,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock4,Edit,Filter,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock4,Edit,Filter,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Dashboard/Layouts/SchoolLayout */ \"(app-pages-browser)/./src/components/Dashboard/Layouts/SchoolLayout.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/widgets/CircularLoader */ \"(app-pages-browser)/./src/components/widgets/CircularLoader.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var _components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/utils/ProtectedRoute */ \"(app-pages-browser)/./src/components/utils/ProtectedRoute.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/services/TimetableServices */ \"(app-pages-browser)/./src/app/services/TimetableServices.tsx\");\n/* harmony import */ var _app_services_ClassServices__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/services/ClassServices */ \"(app-pages-browser)/./src/app/services/ClassServices.tsx\");\n/* harmony import */ var _app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/services/SubjectServices */ \"(app-pages-browser)/./src/app/services/SubjectServices.tsx\");\n/* harmony import */ var _app_services_TeacherServices__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/services/TeacherServices */ \"(app-pages-browser)/./src/app/services/TeacherServices.tsx\");\n/* harmony import */ var _components_modals_TimetableModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/modals/TimetableModal */ \"(app-pages-browser)/./src/components/modals/TimetableModal.tsx\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst navigation = {\n    icon: _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    baseHref: \"/school-admin/timetable\",\n    title: \"Time Table\"\n};\nconst DAYS = [\n    'Monday',\n    'Tuesday',\n    'Wednesday',\n    'Thursday',\n    'Friday'\n];\nfunction TimetablePage() {\n    var _user_school_ids, _classes_find;\n    _s();\n    const { logout, user } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    const { toasts, removeToast, showSuccess, showError } = (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_11__.useToast)();\n    // State management\n    const [timetableData, setTimetableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [allClassesTimetable, setAllClassesTimetable] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [periods, setPeriods] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loadingData, setLoadingData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [selectedClass, setSelectedClass] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all_classes');\n    const [selectedWeek, setSelectedWeek] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('current');\n    // Modal states\n    const [isTimetableModalOpen, setIsTimetableModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [scheduleToEdit, setScheduleToEdit] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Additional data for forms\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [teachers, setTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Loading states\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Get school ID from user\n    const schoolId = (user === null || user === void 0 ? void 0 : (_user_school_ids = user.school_ids) === null || _user_school_ids === void 0 ? void 0 : _user_school_ids[0]) || (user === null || user === void 0 ? void 0 : user.school_id);\n    // Fetch timetable data from API\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"TimetablePage.useEffect\": ()=>{\n            const fetchTimetableData = {\n                \"TimetablePage.useEffect.fetchTimetableData\": async ()=>{\n                    if (!schoolId) {\n                        showError(\"Error\", \"No school ID found\");\n                        setLoadingData(false);\n                        return;\n                    }\n                    try {\n                        setLoadingData(true);\n                        if (selectedClass === 'all_classes') {\n                            // Fetch timetables for all classes\n                            const allClassesResponse = await (0,_app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_6__.getOrganizedTimetable)(schoolId, {});\n                            console.log(\"All Classes Timetable Data:\", allClassesResponse.timetable);\n                            // Group timetable data by class\n                            const classGroupedTimetables = {};\n                            // Get all schedule entries and group by class\n                            const scheduleResponse = await (0,_app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_6__.getTimetable)(schoolId, {});\n                            const scheduleEntries = scheduleResponse.schedule_records;\n                            // Group entries by class name\n                            scheduleEntries.forEach({\n                                \"TimetablePage.useEffect.fetchTimetableData\": (entry)=>{\n                                    if (!classGroupedTimetables[entry.class_name]) {\n                                        classGroupedTimetables[entry.class_name] = {};\n                                        DAYS.forEach({\n                                            \"TimetablePage.useEffect.fetchTimetableData\": (day)=>{\n                                                classGroupedTimetables[entry.class_name][day] = {};\n                                            }\n                                        }[\"TimetablePage.useEffect.fetchTimetableData\"]);\n                                    }\n                                    if (!classGroupedTimetables[entry.class_name][entry.day_of_week]) {\n                                        classGroupedTimetables[entry.class_name][entry.day_of_week] = {};\n                                    }\n                                    classGroupedTimetables[entry.class_name][entry.day_of_week][entry.period_number] = entry;\n                                }\n                            }[\"TimetablePage.useEffect.fetchTimetableData\"]);\n                            setAllClassesTimetable(classGroupedTimetables);\n                            setTimetableData(allClassesResponse.timetable);\n                            setPeriods(allClassesResponse.periods);\n                        } else {\n                            // Build filters for specific class\n                            const filters = {};\n                            if (selectedClass !== 'all_classes') filters.class_id = selectedClass;\n                            // Fetch organized timetable for specific class\n                            const response = await (0,_app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_6__.getOrganizedTimetable)(schoolId, filters);\n                            console.log(\"Timetable Data:\", response.timetable);\n                            setTimetableData(response.timetable);\n                            setPeriods(response.periods);\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching timetable data:\", error);\n                        showError(\"Error\", \"Failed to load timetable data\");\n                    } finally{\n                        setLoadingData(false);\n                    }\n                }\n            }[\"TimetablePage.useEffect.fetchTimetableData\"];\n            fetchTimetableData();\n        }\n    }[\"TimetablePage.useEffect\"], [\n        schoolId,\n        selectedClass\n    ]);\n    // Fetch additional data for modals\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"TimetablePage.useEffect\": ()=>{\n            const fetchAdditionalData = {\n                \"TimetablePage.useEffect.fetchAdditionalData\": async ()=>{\n                    if (!schoolId) return;\n                    console.log(\"Fetching additional data for school ID:\", schoolId);\n                    try {\n                        // Fetch data with individual error handling\n                        const results = await Promise.allSettled([\n                            (0,_app_services_ClassServices__WEBPACK_IMPORTED_MODULE_7__.getClassesBySchool)(schoolId),\n                            (0,_app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_8__.getSubjectsBySchoolId)(schoolId),\n                            (0,_app_services_TeacherServices__WEBPACK_IMPORTED_MODULE_9__.getTeachersBySchool)(schoolId)\n                        ]);\n                        // Handle each result individually\n                        if (results[0].status === 'fulfilled') {\n                            setClasses(results[0].value);\n                        } else {\n                            console.error(\"Failed to fetch classes:\", results[0].reason);\n                            setClasses([]);\n                        }\n                        if (results[1].status === 'fulfilled') {\n                            setSubjects(results[1].value);\n                        } else {\n                            console.error(\"Failed to fetch subjects:\", results[1].reason);\n                            setSubjects([]);\n                        }\n                        if (results[2].status === 'fulfilled') {\n                            setTeachers(results[2].value);\n                        } else {\n                            console.error(\"Failed to fetch teachers:\", results[2].reason);\n                            setTeachers([]);\n                        }\n                        // Show warning if any critical data failed to load\n                        const anyDataFailed = results.some({\n                            \"TimetablePage.useEffect.fetchAdditionalData.anyDataFailed\": (result)=>result.status === 'rejected'\n                        }[\"TimetablePage.useEffect.fetchAdditionalData.anyDataFailed\"]);\n                        if (anyDataFailed) {\n                            showError(\"Warning\", \"Some form data could not be loaded. Some features may be limited.\");\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching additional data:\", error);\n                        showError(\"Error\", \"Failed to load form data\");\n                    }\n                }\n            }[\"TimetablePage.useEffect.fetchAdditionalData\"];\n            fetchAdditionalData();\n        }\n    }[\"TimetablePage.useEffect\"], [\n        schoolId\n    ]); // Removed showError from dependencies\n    // CRUD Functions\n    const handleCreateSchedule = ()=>{\n        setScheduleToEdit(null);\n        setIsTimetableModalOpen(true);\n    };\n    const handleCellClick = (day, periodNumber)=>{\n        var _timetableData_day;\n        // Check if there's already a schedule entry for this slot\n        const existingEntry = (_timetableData_day = timetableData[day]) === null || _timetableData_day === void 0 ? void 0 : _timetableData_day[periodNumber];\n        console.log(\"existingEntry\", existingEntry);\n        if (existingEntry) {\n            const period = periods.find((p)=>p.period_number === periodNumber);\n            console.log(\"period\", period);\n            // Edit existing entry\n            setScheduleToEdit({\n                ...existingEntry,\n                period_id: (period === null || period === void 0 ? void 0 : period._id) || \"\",\n                class_id: selectedClass,\n                day_of_week: day,\n                t\n            });\n        } else {\n            // Create new entry with pre-filled day and period\n            const period = periods.find((p)=>p.period_number === periodNumber);\n            setScheduleToEdit({\n                class_id: selectedClass,\n                day_of_week: day,\n                period_id: (period === null || period === void 0 ? void 0 : period._id) || \"\"\n            });\n        }\n        setIsTimetableModalOpen(true);\n    };\n    // Modal submission function\n    const handleScheduleSubmit = async (data)=>{\n        if (!schoolId) {\n            showError(\"Error\", \"No school ID found\");\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            // Create new schedule entry (we don't have update/delete for now)\n            await (0,_app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_6__.createScheduleEntry)(schoolId, data);\n            // Refresh timetable\n            const filters = {};\n            if (selectedClass !== 'all') filters.class_id = selectedClass;\n            const response = await (0,_app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_6__.getOrganizedTimetable)(schoolId, filters);\n            setTimetableData(response.timetable);\n            setPeriods(response.periods);\n            setIsTimetableModalOpen(false);\n            setScheduleToEdit(null);\n            // Show success notification\n            showSuccess(\"Schedule Created\", \"Schedule entry has been created successfully.\");\n        } catch (error) {\n            console.error(\"Error submitting schedule:\", error);\n            showError(\"Error\", \"Failed to save schedule. Please try again.\");\n            throw error;\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const getSubjectColor = (subject)=>{\n        const colors = {\n            Mathematics: 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300',\n            Physics: 'bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900/30 dark:text-purple-300',\n            Chemistry: 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300',\n            Biology: 'bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/30 dark:text-orange-300',\n            English: 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300',\n            History: 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-300'\n        };\n        return colors[subject] || 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300';\n    };\n    if (loadingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                size: 32,\n                color: \"teal\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                lineNumber: 260,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n            lineNumber: 259,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        allowedRoles: [\n            \"school_admin\",\n            \"admin\",\n            \"super\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            navigation: navigation,\n            onLogout: logout,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-teal rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: \"Time Table Management\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-foreground/60\",\n                                                        children: \"Create and manage class schedules and time tables\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.button, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Add Schedule\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 text-foreground/60\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-foreground\",\n                                                children: \"View:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Class:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedClass,\n                                                onChange: (e)=>setSelectedClass(e.target.value),\n                                                className: \"px-3 py-2 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all_classes\",\n                                                        children: \"All Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    classes.map((classItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: classItem._id,\n                                                            children: classItem.name\n                                                        }, classItem._id, false, {\n                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Week:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedWeek,\n                                                onChange: (e)=>setSelectedWeek(e.target.value),\n                                                className: \"px-3 py-2 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"current\",\n                                                        children: \"Current Week\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"next\",\n                                                        children: \"Next Week\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"custom\",\n                                                        children: \"Custom Range\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-foreground\",\n                                            children: [\n                                                \"Weekly Schedule - \",\n                                                selectedClass === 'all_classes' ? 'All Classes' : ((_classes_find = classes.find((c)=>c._id === selectedClass)) === null || _classes_find === void 0 ? void 0 : _classes_find.name) || 'Unknown Class'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            onClick: handleCreateSchedule,\n                                            className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Add Schedule\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this),\n                                selectedClass === 'all_classes' ? // All Classes View\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8\",\n                                    children: Object.entries(allClassesTimetable).map((param)=>{\n                                        let [className, classTimetable] = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border border-stroke rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-foreground mb-4 bg-gray-50 dark:bg-gray-800 p-3 rounded-md\",\n                                                    children: className\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"overflow-x-auto\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                        className: \"w-full border-collapse\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"border border-stroke p-2 bg-gray-50 dark:bg-gray-800 text-left font-semibold text-foreground text-sm\",\n                                                                            children: \"Period\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                            lineNumber: 364,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        DAYS.map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"border border-stroke p-2 bg-gray-50 dark:bg-gray-800 text-center font-semibold text-foreground min-w-[140px] text-sm\",\n                                                                                children: day\n                                                                            }, day, false, {\n                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                lineNumber: 368,\n                                                                                columnNumber: 31\n                                                                            }, this))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                    lineNumber: 363,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                children: periods.map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"border border-stroke p-2 bg-gray-50 dark:bg-gray-800 font-medium text-foreground text-sm\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-center\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"font-semibold\",\n                                                                                            children: [\n                                                                                                \"P\",\n                                                                                                period.period_number\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                            lineNumber: 379,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-xs text-foreground/60\",\n                                                                                            children: [\n                                                                                                period.start_time.slice(0, 5),\n                                                                                                \"-\",\n                                                                                                period.end_time.slice(0, 5)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                            lineNumber: 380,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                    lineNumber: 378,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                lineNumber: 377,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            DAYS.map((day)=>{\n                                                                                var _classTimetable_day;\n                                                                                const scheduleEntry = (_classTimetable_day = classTimetable[day]) === null || _classTimetable_day === void 0 ? void 0 : _classTimetable_day[period.period_number];\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"border border-stroke p-1\",\n                                                                                    children: scheduleEntry ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"p-2 rounded border \".concat(getSubjectColor(scheduleEntry.subject_name), \" text-xs\"),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"font-semibold mb-1\",\n                                                                                                children: scheduleEntry.subject_name\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                                lineNumber: 394,\n                                                                                                columnNumber: 41\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"opacity-80\",\n                                                                                                children: scheduleEntry.teacher_name\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                                lineNumber: 397,\n                                                                                                columnNumber: 41\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                        lineNumber: 393,\n                                                                                        columnNumber: 39\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"p-2 text-center text-foreground/30 text-xs\",\n                                                                                        children: \"Free\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                        lineNumber: 402,\n                                                                                        columnNumber: 39\n                                                                                    }, this)\n                                                                                }, \"\".concat(className, \"-\").concat(day, \"-\").concat(period.period_number), false, {\n                                                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                    lineNumber: 388,\n                                                                                    columnNumber: 35\n                                                                                }, this);\n                                                                            })\n                                                                        ]\n                                                                    }, period.period_number, true, {\n                                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                        lineNumber: 376,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, className, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 15\n                                }, this) : // Single Class View\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full border-collapse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"border border-stroke p-3 bg-gray-50 dark:bg-gray-800 text-left font-semibold text-foreground\",\n                                                            children: \"Period / Day\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        DAYS.map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"border border-stroke p-3 bg-gray-50 dark:bg-gray-800 text-center font-semibold text-foreground min-w-[180px]\",\n                                                                children: day\n                                                            }, day, false, {\n                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: periods.map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"border border-stroke p-3 bg-gray-50 dark:bg-gray-800 font-medium text-foreground\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-semibold\",\n                                                                            children: [\n                                                                                \"P \",\n                                                                                period.period_number\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                            lineNumber: 438,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-foreground/60\",\n                                                                            children: [\n                                                                                period.start_time.slice(0, 5),\n                                                                                \" - \",\n                                                                                period.end_time.slice(0, 5)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                            lineNumber: 439,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                    lineNumber: 437,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                lineNumber: 436,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            DAYS.map((day)=>{\n                                                                var _timetableData_day;\n                                                                const scheduleEntry = (_timetableData_day = timetableData[day]) === null || _timetableData_day === void 0 ? void 0 : _timetableData_day[period.period_number];\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"border border-stroke p-2 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors\",\n                                                                    onClick: ()=>handleCellClick(day, period.period_number),\n                                                                    children: scheduleEntry ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-3 rounded-lg border-2 \".concat(getSubjectColor(scheduleEntry.subject_name), \" relative group\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-semibold text-sm mb-1\",\n                                                                                children: scheduleEntry.subject_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                lineNumber: 454,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs opacity-80 mb-1\",\n                                                                                children: scheduleEntry.teacher_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                lineNumber: 457,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs opacity-70\",\n                                                                                children: scheduleEntry.class_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                lineNumber: 460,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity p-1 bg-white dark:bg-gray-800 rounded shadow-sm\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    className: \"h-3 w-3 text-foreground/60\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                    lineNumber: 466,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                lineNumber: 465,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                        lineNumber: 453,\n                                                                        columnNumber: 33\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-3 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 text-center text-foreground/40 hover:border-teal hover:text-teal transition-colors\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"h-4 w-4 mx-auto mb-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                lineNumber: 471,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs\",\n                                                                                children: \"Add Class\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                lineNumber: 472,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                        lineNumber: 470,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, \"\".concat(day, \"-\").concat(period.period_number), false, {\n                                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                    lineNumber: 447,\n                                                                    columnNumber: 29\n                                                                }, this);\n                                                            })\n                                                        ]\n                                                    }, period.period_number, true, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Total Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: Object.values(timetableData).reduce((total, day)=>total + Object.values(day).filter((entry)=>entry !== null).length, 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Free Periods\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: DAYS.length * periods.length - Object.values(timetableData).reduce((total, day)=>total + Object.values(day).filter((entry)=>entry !== null).length, 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 506,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 515,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 514,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 505,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Teachers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: new Set(Object.values(timetableData).flatMap((day)=>Object.values(day)).filter((entry)=>entry !== null).map((entry)=>entry.teacher_name)).size\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Subjects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: new Set(Object.values(timetableData).flatMap((day)=>Object.values(day)).filter((entry)=>entry !== null).map((entry)=>entry.subject_name)).size\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 552,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                            lineNumber: 487,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_TimetableModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    isOpen: isTimetableModalOpen,\n                    onClose: ()=>{\n                        setIsTimetableModalOpen(false);\n                        setScheduleToEdit(null);\n                    },\n                    onSubmit: handleScheduleSubmit,\n                    schedule: scheduleToEdit,\n                    classes: classes,\n                    subjects: subjects,\n                    teachers: teachers,\n                    periods: periods,\n                    loading: isSubmitting,\n                    preSelectedClass: selectedClass !== 'all_classes' ? selectedClass : undefined,\n                    isClassLocked: selectedClass !== 'all_classes'\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                    lineNumber: 561,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Toast__WEBPACK_IMPORTED_MODULE_11__.ToastContainer, {\n                    toasts: toasts,\n                    onClose: removeToast\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                    lineNumber: 579,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n            lineNumber: 267,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n        lineNumber: 266,\n        columnNumber: 5\n    }, this);\n}\n_s(TimetablePage, \"92L3/DB093ESpwcNTSP/EXrapis=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        _components_ui_Toast__WEBPACK_IMPORTED_MODULE_11__.useToast\n    ];\n});\n_c = TimetablePage;\nvar _c;\n$RefreshReg$(_c, \"TimetablePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboards)/school-admin/timetable/page.tsx\n"));

/***/ })

});