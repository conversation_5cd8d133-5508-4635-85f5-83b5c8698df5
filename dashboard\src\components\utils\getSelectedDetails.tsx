// utils/feeUtils.ts or wherever you define it
import { FeeSchema } from "@/app/models/FeesModel";

export const getSelectedFeesDetails = (
  selectedFeeIds: string[],
  feeList: FeeSchema[]
): FeeSchema[] => {
  const SCOLAFIFY_FEE_ID = "507f1f77bcf86cd799439011";
  const SCOLAFIFY_FEE = 3000;

  const scolafifyFee: FeeSchema = {
    _id: SCOLAFIFY_FEE_ID,
    fee_type: "Scolafify Fee",
    amount: SCOLAFIFY_FEE,
    school_id: "",
  };

  const selected = feeList.filter(fee => selectedFeeIds.includes(fee._id));

  if (selectedFeeIds.includes(SCOLAFIFY_FEE_ID)) {
    selected.push(scolafifyFee);
  }

  return selected;
};
