"use client";

import { useState, useEffect } from "react";
import { X, Save, Loader2 } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface TeacherAssignmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => Promise<void>;
  assignment?: any;
  classes: any[];
  subjects: any[];
  teachers: any[];
  periods: any[];
  loading?: boolean;
}

const DAYS = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
const SCHEDULE_TYPES = ['Normal', 'Exam', 'Event'];

export default function TeacherAssignmentModal({
  isOpen,
  onClose,
  onSubmit,
  assignment,
  classes,
  subjects,
  teachers,
  periods,
  loading = false
}: TeacherAssignmentModalProps) {
  const [formData, setFormData] = useState({
    class_id: '',
    subject_id: '',
    teacher_id: '',
    period_id: '',
    day_of_week: '',
    schedule_type: 'Normal'
  });

  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset form when modal opens/closes or assignment changes
  useEffect(() => {
    if (isOpen) {
      if (assignment) {
        // Edit mode - populate form with assignment data
        // Extract IDs from objects if they exist
        const classId = typeof assignment.class_id === 'object' ? assignment.class_id._id : assignment.class_id;
        const subjectId = typeof assignment.subject_id === 'object' ? assignment.subject_id._id : assignment.subject_id;
        const teacherId = typeof assignment.teacher_id === 'object' ? assignment.teacher_id._id : assignment.teacher_id;
        const periodId = typeof assignment.period_id === 'object' ? assignment.period_id._id : assignment.period_id;

        setFormData({
          class_id: classId || '',
          subject_id: subjectId || '',
          teacher_id: teacherId || '',
          period_id: periodId || '',
          day_of_week: assignment.day_of_week || '',
          schedule_type: assignment.schedule_type || 'Normal'
        });
      } else {
        // Create mode - reset form
        setFormData({
          class_id: '',
          subject_id: '',
          teacher_id: '',
          period_id: '',
          day_of_week: '',
          schedule_type: 'Normal'
        });
      }
      setErrors({});
    }
  }, [isOpen, assignment]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!formData.class_id) newErrors.class_id = 'Class is required';
    if (!formData.subject_id) newErrors.subject_id = 'Subject is required';
    if (!formData.teacher_id) newErrors.teacher_id = 'Teacher is required';
    if (!formData.period_id) newErrors.period_id = 'Period is required';
    if (!formData.day_of_week) newErrors.day_of_week = 'Day of week is required';
    if (!formData.schedule_type) newErrors.schedule_type = 'Schedule type is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      await onSubmit(formData);
      onClose();
    } catch (error) {
      console.error('Error submitting assignment:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting && !loading) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-widget rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-stroke">
            <h2 className="text-xl font-semibold text-foreground">
              {assignment ? 'Edit Teacher Assignment' : 'New Teacher Assignment'}
            </h2>
            <button
              onClick={handleClose}
              disabled={isSubmitting || loading}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors disabled:opacity-50"
            >
              <X className="h-5 w-5 text-foreground" />
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Class Selection */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Class <span className="text-red-500">*</span>
                </label>
                <select
                  value={formData.class_id}
                  onChange={(e) => handleInputChange('class_id', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground ${
                    errors.class_id ? 'border-red-500' : 'border-stroke'
                  }`}
                  disabled={isSubmitting || loading}
                >
                  <option value="">Select a class</option>
                  {classes.map((classItem) => (
                    <option key={classItem._id} value={classItem._id}>
                      {classItem.name}
                    </option>
                  ))}
                </select>
                {errors.class_id && (
                  <p className="text-red-500 text-sm mt-1">{errors.class_id}</p>
                )}
              </div>

              {/* Subject Selection */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Subject <span className="text-red-500">*</span>
                </label>
                <select
                  value={formData.subject_id}
                  onChange={(e) => handleInputChange('subject_id', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground ${
                    errors.subject_id ? 'border-red-500' : 'border-stroke'
                  }`}
                  disabled={isSubmitting || loading}
                >
                  <option value="">Select a subject</option>
                  {subjects.map((subject) => (
                    <option key={subject._id} value={subject._id}>
                      {subject.name}
                    </option>
                  ))}
                </select>
                {errors.subject_id && (
                  <p className="text-red-500 text-sm mt-1">{errors.subject_id}</p>
                )}
              </div>

              {/* Teacher Selection */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Teacher <span className="text-red-500">*</span>
                </label>
                <select
                  value={formData.teacher_id}
                  onChange={(e) => handleInputChange('teacher_id', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground ${
                    errors.teacher_id ? 'border-red-500' : 'border-stroke'
                  }`}
                  disabled={isSubmitting || loading}
                >
                  <option value="">Select a teacher</option>
                  {teachers.map((teacher) => (
                    <option key={teacher._id} value={teacher._id}>
                      {teacher.first_name && teacher.last_name ?
                        `${teacher.first_name} ${teacher.last_name}` :
                        teacher.name || 'Unknown Teacher'}
                    </option>
                  ))}
                </select>
                {errors.teacher_id && (
                  <p className="text-red-500 text-sm mt-1">{errors.teacher_id}</p>
                )}
              </div>

              {/* Period Selection */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Period <span className="text-red-500">*</span>
                </label>
                <select
                  value={formData.period_id}
                  onChange={(e) => handleInputChange('period_id', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground ${
                    errors.period_id ? 'border-red-500' : 'border-stroke'
                  }`}
                  disabled={isSubmitting || loading}
                >
                  <option value="">Select a period</option>
                  {periods.map((period) => (
                    <option key={period._id} value={period._id}>
                      Period {period.period_number} ({period.start_time.slice(0, 5)} - {period.end_time.slice(0, 5)})
                    </option>
                  ))}
                </select>
                {errors.period_id && (
                  <p className="text-red-500 text-sm mt-1">{errors.period_id}</p>
                )}
              </div>

              {/* Day Selection */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Day of Week <span className="text-red-500">*</span>
                </label>
                <select
                  value={formData.day_of_week}
                  onChange={(e) => handleInputChange('day_of_week', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground ${
                    errors.day_of_week ? 'border-red-500' : 'border-stroke'
                  }`}
                  disabled={isSubmitting || loading}
                >
                  <option value="">Select a day</option>
                  {DAYS.map((day) => (
                    <option key={day} value={day}>
                      {day}
                    </option>
                  ))}
                </select>
                {errors.day_of_week && (
                  <p className="text-red-500 text-sm mt-1">{errors.day_of_week}</p>
                )}
              </div>

              {/* Schedule Type Selection */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Schedule Type <span className="text-red-500">*</span>
                </label>
                <select
                  value={formData.schedule_type}
                  onChange={(e) => handleInputChange('schedule_type', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground ${
                    errors.schedule_type ? 'border-red-500' : 'border-stroke'
                  }`}
                  disabled={isSubmitting || loading}
                >
                  {SCHEDULE_TYPES.map((type) => (
                    <option key={type} value={type}>
                      {type}
                    </option>
                  ))}
                </select>
                {errors.schedule_type && (
                  <p className="text-red-500 text-sm mt-1">{errors.schedule_type}</p>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-end space-x-4 pt-6 border-t border-stroke">
              <button
                type="button"
                onClick={handleClose}
                disabled={isSubmitting || loading}
                className="px-4 py-2 text-foreground/70 hover:text-foreground transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting || loading}
                className="flex items-center space-x-2 px-6 py-2 bg-teal text-white rounded-md hover:bg-teal-600 transition-colors disabled:opacity-50"
              >
                {(isSubmitting || loading) && <Loader2 className="h-4 w-4 animate-spin" />}
                <Save className="h-4 w-4" />
                <span>{assignment ? 'Update Assignment' : 'Create Assignment'}</span>
              </button>
            </div>
          </form>
        </motion.div>
      </div>
    </AnimatePresence>
  );
}
