"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/attendance/page",{

/***/ "(app-pages-browser)/./src/app/(dashboards)/school-admin/attendance/page.tsx":
/*!***************************************************************!*\
  !*** ./src/app/(dashboards)/school-admin/attendance/page.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AttendancePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,FileCheck2,Filter,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check-2.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,FileCheck2,Filter,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,FileCheck2,Filter,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,FileCheck2,Filter,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,FileCheck2,Filter,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,FileCheck2,Filter,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,FileCheck2,Filter,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Dashboard/Layouts/SchoolLayout */ \"(app-pages-browser)/./src/components/Dashboard/Layouts/SchoolLayout.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_utils_TableFix__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/utils/TableFix */ \"(app-pages-browser)/./src/components/utils/TableFix.tsx\");\n/* harmony import */ var _components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/widgets/CircularLoader */ \"(app-pages-browser)/./src/components/widgets/CircularLoader.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var _components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/utils/ProtectedRoute */ \"(app-pages-browser)/./src/components/utils/ProtectedRoute.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/services/AttendanceServices */ \"(app-pages-browser)/./src/app/services/AttendanceServices.tsx\");\n/* harmony import */ var _app_services_StudentServices__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/services/StudentServices */ \"(app-pages-browser)/./src/app/services/StudentServices.tsx\");\n/* harmony import */ var _app_services_ClassServices__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/services/ClassServices */ \"(app-pages-browser)/./src/app/services/ClassServices.tsx\");\n/* harmony import */ var _app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/services/SubjectServices */ \"(app-pages-browser)/./src/app/services/SubjectServices.tsx\");\n/* harmony import */ var _app_services_ClassScheduleServices__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/services/ClassScheduleServices */ \"(app-pages-browser)/./src/app/services/ClassScheduleServices.tsx\");\n/* harmony import */ var _components_modals_AttendanceModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/modals/AttendanceModal */ \"(app-pages-browser)/./src/components/modals/AttendanceModal.tsx\");\n/* harmony import */ var _components_modals_DeleteConfirmationModal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/modals/DeleteConfirmationModal */ \"(app-pages-browser)/./src/components/modals/DeleteConfirmationModal.tsx\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst navigation = {\n    icon: _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n    baseHref: \"/school-admin/attendance\",\n    title: \"Attendance\"\n};\nfunction AttendancePage() {\n    var _user_school_ids;\n    _s();\n    const { logout, user } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const { toasts, removeToast, showSuccess, showError } = (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_14__.useToast)();\n    // State management\n    const [attendanceRecords, setAttendanceRecords] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        totalStudents: 0,\n        presentToday: 0,\n        absentToday: 0,\n        lateToday: 0,\n        excusedToday: 0,\n        attendanceRate: 0\n    });\n    const [loadingData, setLoadingData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(new Date().toISOString().split('T')[0]);\n    const [selectedClass, setSelectedClass] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    // Modal states\n    const [isAttendanceModalOpen, setIsAttendanceModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isDeleteModalOpen, setIsDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [attendanceToEdit, setAttendanceToEdit] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [attendanceToDelete, setAttendanceToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [deleteType, setDeleteType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"single\");\n    const [selectedAttendances, setSelectedAttendances] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Additional data for forms\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [schedules, setSchedules] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Loading states\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [clearSelection, setClearSelection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Get school ID from user\n    const schoolId = (user === null || user === void 0 ? void 0 : (_user_school_ids = user.school_ids) === null || _user_school_ids === void 0 ? void 0 : _user_school_ids[0]) || (user === null || user === void 0 ? void 0 : user.school_id);\n    // Fetch attendance data from API\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"AttendancePage.useEffect\": ()=>{\n            const fetchAttendanceData = {\n                \"AttendancePage.useEffect.fetchAttendanceData\": async ()=>{\n                    if (!schoolId) {\n                        showError(\"Error\", \"No school ID found\");\n                        setLoadingData(false);\n                        return;\n                    }\n                    try {\n                        setLoadingData(true);\n                        // Build filters\n                        const filters = {};\n                        if (selectedDate) filters.date = selectedDate;\n                        if (selectedClass !== 'all') filters.class_id = selectedClass;\n                        if (selectedStatus !== 'all') filters.status = selectedStatus;\n                        // Fetch records and stats in parallel\n                        const [recordsResponse, statsResponse] = await Promise.all([\n                            (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.getAttendanceRecords)(schoolId, filters),\n                            (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.getAttendanceStats)(schoolId, selectedDate)\n                        ]);\n                        setAttendanceRecords(recordsResponse.attendance_records);\n                        setStats(statsResponse.stats);\n                    } catch (error) {\n                        console.error(\"Error fetching attendance data:\", error);\n                        showError(\"Error\", \"Failed to load attendance data\");\n                    } finally{\n                        setLoadingData(false);\n                    }\n                }\n            }[\"AttendancePage.useEffect.fetchAttendanceData\"];\n            fetchAttendanceData();\n        }\n    }[\"AttendancePage.useEffect\"], [\n        schoolId,\n        selectedDate,\n        selectedClass,\n        selectedStatus\n    ]);\n    // Fetch additional data for modals\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"AttendancePage.useEffect\": ()=>{\n            const fetchAdditionalData = {\n                \"AttendancePage.useEffect.fetchAdditionalData\": async ()=>{\n                    if (!schoolId) return;\n                    try {\n                        // Fetch data with individual error handling\n                        const results = await Promise.allSettled([\n                            (0,_app_services_StudentServices__WEBPACK_IMPORTED_MODULE_8__.getStudentsBySchool)(schoolId),\n                            (0,_app_services_ClassServices__WEBPACK_IMPORTED_MODULE_9__.getClassesBySchool)(schoolId),\n                            (0,_app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_10__.getAllSubjects)(),\n                            (0,_app_services_ClassScheduleServices__WEBPACK_IMPORTED_MODULE_11__.getClassSchedulesBySchool)(schoolId)\n                        ]);\n                        // Handle each result individually\n                        if (results[0].status === 'fulfilled') {\n                            setStudents(results[0].value);\n                        } else {\n                            console.error(\"Failed to fetch students:\", results[0].reason);\n                            setStudents([]);\n                        }\n                        if (results[1].status === 'fulfilled') {\n                            setClasses(results[1].value);\n                        } else {\n                            console.error(\"Failed to fetch classes:\", results[1].reason);\n                            setClasses([]);\n                        }\n                        if (results[2].status === 'fulfilled') {\n                            setSubjects(results[2].value);\n                        } else {\n                            console.error(\"Failed to fetch subjects:\", results[2].reason);\n                            setSubjects([]);\n                        }\n                        if (results[3].status === 'fulfilled') {\n                            setSchedules(results[3].value);\n                        } else {\n                            console.error(\"Failed to fetch schedules:\", results[3].reason);\n                            setSchedules([]);\n                        // Don't show error for schedules as it's not critical\n                        }\n                        // Only show error if critical data failed to load\n                        const criticalDataFailed = results[0].status === 'rejected' || results[1].status === 'rejected' || results[2].status === 'rejected';\n                        if (criticalDataFailed) {\n                            showError(\"Warning\", \"Some form data could not be loaded. Some features may be limited.\");\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching additional data:\", error);\n                        showError(\"Error\", \"Failed to load form data\");\n                    }\n                }\n            }[\"AttendancePage.useEffect.fetchAdditionalData\"];\n            fetchAdditionalData();\n        }\n    }[\"AttendancePage.useEffect\"], [\n        schoolId\n    ]); // Removed showError from dependencies\n    // CRUD Functions\n    const handleCreateAttendance = ()=>{\n        setAttendanceToEdit(null);\n        setIsAttendanceModalOpen(true);\n    };\n    const handleEditAttendance = (attendance)=>{\n        setAttendanceToEdit(attendance);\n        setIsAttendanceModalOpen(true);\n    };\n    const handleDeleteAttendance = (attendance)=>{\n        setAttendanceToDelete(attendance);\n        setDeleteType(\"single\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleDeleteMultiple = ()=>{\n        setDeleteType(\"multiple\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleSelectionChange = (selectedRows)=>{\n        setSelectedAttendances(selectedRows);\n    };\n    // Modal submission functions\n    const handleAttendanceSubmit = async (data)=>{\n        if (!schoolId) {\n            showError(\"Error\", \"No school ID found\");\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            if (attendanceToEdit) {\n                // Update existing attendance\n                await (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.updateAttendance)(attendanceToEdit._id, data);\n            } else {\n                // Create new attendance\n                await (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.createAttendance)({\n                    ...data,\n                    school_id: schoolId\n                });\n            }\n            // Refresh attendance list\n            const filters = {};\n            if (selectedDate) filters.date = selectedDate;\n            if (selectedClass !== 'all') filters.class_id = selectedClass;\n            if (selectedStatus !== 'all') filters.status = selectedStatus;\n            const [recordsResponse, statsResponse] = await Promise.all([\n                (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.getAttendanceRecords)(schoolId, filters),\n                (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.getAttendanceStats)(schoolId, selectedDate)\n            ]);\n            setAttendanceRecords(recordsResponse.attendance_records);\n            setStats(statsResponse.stats);\n            setIsAttendanceModalOpen(false);\n            setAttendanceToEdit(null);\n            // Show success notification\n            showSuccess(attendanceToEdit ? \"Attendance Updated\" : \"Attendance Marked\", attendanceToEdit ? \"Attendance record has been updated successfully.\" : \"Attendance has been marked successfully.\");\n        } catch (error) {\n            console.error(\"Error submitting attendance:\", error);\n            showError(\"Error\", \"Failed to save attendance. Please try again.\");\n            throw error;\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleDeleteConfirm = async ()=>{\n        if (!schoolId) return;\n        try {\n            if (deleteType === \"single\" && attendanceToDelete) {\n                await (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.deleteAttendance)(attendanceToDelete._id);\n            } else if (deleteType === \"multiple\") {\n                const selectedIds = selectedAttendances.map((a)=>a._id);\n                await (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.deleteMultipleAttendances)(selectedIds);\n                setClearSelection(true);\n            }\n            // Refresh attendance list\n            const filters = {};\n            if (selectedDate) filters.date = selectedDate;\n            if (selectedClass !== 'all') filters.class_id = selectedClass;\n            if (selectedStatus !== 'all') filters.status = selectedStatus;\n            const [recordsResponse, statsResponse] = await Promise.all([\n                (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.getAttendanceRecords)(schoolId, filters),\n                (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.getAttendanceStats)(schoolId, selectedDate)\n            ]);\n            setAttendanceRecords(recordsResponse.attendance_records);\n            setStats(statsResponse.stats);\n            setIsDeleteModalOpen(false);\n            setAttendanceToDelete(null);\n            setSelectedAttendances([]);\n            // Show success notification\n            if (deleteType === \"single\") {\n                showSuccess(\"Attendance Deleted\", \"Attendance record has been deleted successfully.\");\n            } else {\n                showSuccess(\"Attendances Deleted\", \"\".concat(selectedAttendances.length, \" attendance records have been deleted successfully.\"));\n            }\n        } catch (error) {\n            console.error(\"Error deleting attendance(s):\", error);\n            showError(\"Error\", \"Failed to delete attendance record(s). Please try again.\");\n            throw error;\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'Present':\n                return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';\n            case 'Absent':\n                return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';\n            case 'Late':\n                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';\n            case 'Excused':\n                return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';\n            default:\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';\n        }\n    };\n    // Table columns\n    const columns = [\n        {\n            header: \"Student\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-medium text-foreground\",\n                            children: row.student_name\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-foreground/60\",\n                            children: row.student_id\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Class\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-medium\",\n                    children: row.class_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 320,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Subject\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.subject_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 326,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Period\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: [\n                        \"Period \",\n                        row.period_number\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 332,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Teacher\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.teacher_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 338,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Status\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getStatusColor(row.status)),\n                    children: row.status\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 344,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Date\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: new Date(row.date).toLocaleDateString()\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    // Actions for the table\n    const actions = [\n        {\n            label: \"Edit\",\n            onClick: (attendance)=>{\n                handleEditAttendance(attendance);\n            }\n        },\n        {\n            label: \"Delete\",\n            onClick: (attendance)=>{\n                handleDeleteAttendance(attendance);\n            }\n        }\n    ];\n    console.log(\"attendanceRecords\", attendanceRecords);\n    // Filter data based on selections\n    const filteredRecords = attendanceRecords.filter((record)=>{\n        if (selectedClass !== 'all' && record.class_name !== selectedClass) return false;\n        if (selectedStatus !== 'all' && record.status !== selectedStatus) return false;\n        return true;\n    });\n    if (loadingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: 32,\n                color: \"teal\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                lineNumber: 383,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n            lineNumber: 382,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        allowedRoles: [\n            \"school_admin\",\n            \"admin\",\n            \"super\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            navigation: navigation,\n            onLogout: logout,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-teal rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: \"Attendance Management\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-foreground/60\",\n                                                        children: \"Monitor and track student attendance across all classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-foreground\",\n                                                children: [\n                                                    stats.attendanceRate,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-foreground/60\",\n                                                children: \"Today's Rate\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                            lineNumber: 393,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Total Students\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: stats.totalStudents\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Present Today\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-green-600\",\n                                                        children: stats.presentToday\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Absent Today\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-red-600\",\n                                                        children: stats.absentToday\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                    lineNumber: 440,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Late Today\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-yellow-600\",\n                                                        children: stats.lateToday\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-yellow-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                            lineNumber: 415,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-4 w-4 text-foreground/60\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-foreground\",\n                                                children: \"Filters:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Date:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                value: selectedDate,\n                                                onChange: (e)=>setSelectedDate(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Class:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedClass,\n                                                onChange: (e)=>setSelectedClass(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    classes.map((classItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: classItem._id,\n                                                            children: classItem.name\n                                                        }, classItem._id, false, {\n                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Status:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedStatus,\n                                                onChange: (e)=>setSelectedStatus(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Present\",\n                                                        children: \"Present\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Absent\",\n                                                        children: \"Absent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Late\",\n                                                        children: \"Late\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Excused\",\n                                                        children: \"Excused\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 510,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_20__.motion.button, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Export\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 520,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-foreground\",\n                                            children: [\n                                                \"Attendance Records (\",\n                                                filteredRecords.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_20__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            onClick: handleCreateAttendance,\n                                            className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Mark Attendance\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        size: 24,\n                                        color: \"teal\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 543,\n                                        columnNumber: 33\n                                    }, void 0),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_TableFix__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        data: filteredRecords,\n                                        columns: columns,\n                                        actions: actions,\n                                        defaultItemsPerPage: 15,\n                                        onSelectionChange: handleSelectionChange,\n                                        handleDeleteMultiple: handleDeleteMultiple,\n                                        clearSelection: clearSelection,\n                                        onSelectionCleared: ()=>setClearSelection(false)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 544,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                    lineNumber: 543,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                            lineNumber: 526,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 391,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_AttendanceModal__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    isOpen: isAttendanceModalOpen,\n                    onClose: ()=>{\n                        setIsAttendanceModalOpen(false);\n                        setAttendanceToEdit(null);\n                    },\n                    onSubmit: handleAttendanceSubmit,\n                    attendance: attendanceToEdit,\n                    students: students,\n                    classes: classes,\n                    subjects: subjects,\n                    schedules: schedules,\n                    loading: isSubmitting\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 559,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_DeleteConfirmationModal__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    isOpen: isDeleteModalOpen,\n                    onClose: ()=>{\n                        setIsDeleteModalOpen(false);\n                        setAttendanceToDelete(null);\n                    },\n                    onConfirm: handleDeleteConfirm,\n                    title: deleteType === \"single\" ? \"Delete Attendance Record\" : \"Delete Selected Attendance Records\",\n                    message: deleteType === \"single\" ? \"Are you sure you want to delete this attendance record? This action cannot be undone.\" : \"Are you sure you want to delete \".concat(selectedAttendances.length, \" selected attendance records? This action cannot be undone.\"),\n                    itemName: deleteType === \"single\" && attendanceToDelete ? \"\".concat(attendanceToDelete.student_name, \" - \").concat(attendanceToDelete.subject_name, \" (\").concat(new Date(attendanceToDelete.date).toLocaleDateString(), \")\") : undefined,\n                    itemCount: deleteType === \"multiple\" ? selectedAttendances.length : undefined,\n                    type: deleteType\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 575,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Toast__WEBPACK_IMPORTED_MODULE_14__.ToastContainer, {\n                    toasts: toasts,\n                    onClose: removeToast\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 602,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n            lineNumber: 390,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n        lineNumber: 389,\n        columnNumber: 5\n    }, this);\n}\n_s(AttendancePage, \"ANoYHScOC7tz7PVR3x5l6KpvzZA=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        _components_ui_Toast__WEBPACK_IMPORTED_MODULE_14__.useToast\n    ];\n});\n_c = AttendancePage;\nvar _c;\n$RefreshReg$(_c, \"AttendancePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboards)/school-admin/attendance/page.tsx\n"));

/***/ })

});