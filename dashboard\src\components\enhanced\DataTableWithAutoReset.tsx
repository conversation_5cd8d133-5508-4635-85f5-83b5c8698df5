'use client';

import React, { useRef, useImperativeHandle, forwardRef } from 'react';
import DataTableFix from '@/components/utils/TableFix';

interface DataTableWithAutoResetProps<T> {
  columns: any[];
  data: T[];
  actions?: any[];
  defaultItemsPerPage?: number;
  loading?: boolean;
  onLoadingChange?: (loading: boolean) => void;
  onSelectionChange?: (selection: T[]) => void;
  idAccessor?: keyof T;
  hasSearch?: boolean;
  showCheckbox?: boolean;
  enableBulkActions?: boolean;
  handleDeleteMultiple?: (selectedIds: string[]) => void;
  handleDeleteAll?: () => void;
  [key: string]: any; // For other props
}

export interface DataTableRef {
  clearSelection: () => void;
}

const DataTableWithAutoReset = forwardRef<DataTableRef, DataTableWithAutoResetProps<any>>(
  function DataTableWithAutoReset<T>(
    {
      columns,
      data,
      actions = [],
      defaultItemsPerPage = 5,
      loading = false,
      onLoadingChange,
      onSelectionChange,
      idAccessor = '_id' as keyof T,
      hasSearch = true,
      showCheckbox = true,
      enableBulkActions = true,
      handleDeleteMultiple,
      handleDeleteAll,
      ...otherProps
    }: DataTableWithAutoResetProps<T>,
    ref
  ) {
    const clearSelectionTrigger = useRef(false);

    // Expose clearSelection method to parent
    useImperativeHandle(ref, () => ({
      clearSelection: () => {
        clearSelectionTrigger.current = !clearSelectionTrigger.current;
      }
    }));

    return (
      <DataTableFix
        columns={columns}
        data={data}
        actions={actions}
        defaultItemsPerPage={defaultItemsPerPage}
        loading={loading}
        onLoadingChange={onLoadingChange}
        onSelectionChange={onSelectionChange}
        idAccessor={idAccessor}
        hasSearch={hasSearch}
        showCheckbox={showCheckbox}
        enableBulkActions={enableBulkActions}
        handleDeleteMultiple={handleDeleteMultiple}
        handleDeleteAll={handleDeleteAll}
        clearSelection={clearSelectionTrigger.current}
        onSelectionCleared={() => {
          // Reset trigger after clearing
          clearSelectionTrigger.current = false;
        }}
        {...otherProps}
      />
    );
  }
);

export default DataTableWithAutoReset;
