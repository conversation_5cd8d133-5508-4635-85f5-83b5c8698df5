"use client";

import React, { useState, useEffect } from "react";
import { X, Percent, Save } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface GradeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => Promise<void>;
  grade?: any | null;
  students: any[];
  subjects: any[];
  examTypes: any[];
  loading?: boolean;
}

export default function GradeModal({
  isOpen,
  onClose,
  onSubmit,
  grade,
  students,
  subjects,
  examTypes,
  loading = false
}: GradeModalProps) {
  const [formData, setFormData] = useState({
    student_id: "",
    subject_id: "",
    exam_type: "",
    term: "First Term" as "First Term" | "Second Term" | "Third Term",
    academic_year: "2024-2025",
    score: "",
    grade: "",
    comments: ""
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const isEditing = !!grade;

  useEffect(() => {
    if (isOpen) {
      if (grade) {
        setFormData({
          student_id: grade.student_id || "",
          subject_id: grade.subject_id || "",
          exam_type: grade.exam_type || "",
          term: grade.term || "First Term",
          academic_year: grade.academic_year || "2024-2025",
          score: grade.score?.toString() || "",
          grade: grade.grade || "",
          comments: grade.comments || ""
        });
      } else {
        setFormData({
          student_id: "",
          subject_id: "",
          exam_type: "",
          term: "First Term",
          academic_year: "2024-2025",
          score: "",
          grade: "",
          comments: ""
        });
      }
      setErrors({});
    }
  }, [isOpen, grade]);

  // Auto-calculate grade based on score
  useEffect(() => {
    if (formData.score) {
      const score = parseFloat(formData.score);
      let calculatedGrade = "";
      
      if (score >= 90) calculatedGrade = "A+";
      else if (score >= 85) calculatedGrade = "A";
      else if (score >= 80) calculatedGrade = "A-";
      else if (score >= 75) calculatedGrade = "B+";
      else if (score >= 70) calculatedGrade = "B";
      else if (score >= 65) calculatedGrade = "B-";
      else if (score >= 60) calculatedGrade = "C+";
      else if (score >= 55) calculatedGrade = "C";
      else if (score >= 50) calculatedGrade = "C-";
      else if (score >= 45) calculatedGrade = "D+";
      else if (score >= 40) calculatedGrade = "D";
      else calculatedGrade = "F";

      setFormData(prev => ({ ...prev, grade: calculatedGrade }));
    }
  }, [formData.score]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.student_id) {
      newErrors.student_id = "Student is required";
    }
    if (!formData.subject_id) {
      newErrors.subject_id = "Subject is required";
    }
    if (!formData.exam_type) {
      newErrors.exam_type = "Exam type is required";
    }
    if (!formData.score) {
      newErrors.score = "Score is required";
    } else {
      const score = parseFloat(formData.score);
      if (isNaN(score) || score < 0 || score > 100) {
        newErrors.score = "Score must be a number between 0 and 100";
      }
    }
    if (!formData.academic_year) {
      newErrors.academic_year = "Academic year is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      const submitData = {
        ...formData,
        score: parseFloat(formData.score)
      };
      await onSubmit(submitData);
      onClose();
    } catch (error) {
      console.error("Error submitting grade:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={onClose}
          />
          
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-lg mx-4 max-h-[90vh] overflow-y-auto"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                  <Percent className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {isEditing ? "Edit Grade" : "Add New Grade"}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {isEditing ? "Update grade record" : "Create new grade record"}
                  </p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit} className="p-6 space-y-4">
              {/* Student */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Student
                </label>
                <select
                  value={formData.student_id}
                  onChange={(e) => handleInputChange("student_id", e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white ${
                    errors.student_id 
                      ? "border-red-500 dark:border-red-500" 
                      : "border-gray-300 dark:border-gray-600"
                  }`}
                >
                  <option value="">Select student</option>
                  {students.map((student) => (
                    <option key={student._id} value={student._id}>
                      {student.first_name} {student.last_name} ({student.student_id})
                    </option>
                  ))}
                </select>
                {errors.student_id && (
                  <p className="mt-1 text-sm text-red-500">{errors.student_id}</p>
                )}
              </div>

              {/* Subject */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Subject
                </label>
                <select
                  value={formData.subject_id}
                  onChange={(e) => handleInputChange("subject_id", e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white ${
                    errors.subject_id 
                      ? "border-red-500 dark:border-red-500" 
                      : "border-gray-300 dark:border-gray-600"
                  }`}
                >
                  <option value="">Select subject</option>
                  {subjects.map((subject) => (
                    <option key={subject._id} value={subject._id}>
                      {subject.name}
                    </option>
                  ))}
                </select>
                {errors.subject_id && (
                  <p className="mt-1 text-sm text-red-500">{errors.subject_id}</p>
                )}
              </div>

              {/* Exam Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Exam Type
                </label>
                <select
                  value={formData.exam_type}
                  onChange={(e) => handleInputChange("exam_type", e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white ${
                    errors.exam_type 
                      ? "border-red-500 dark:border-red-500" 
                      : "border-gray-300 dark:border-gray-600"
                  }`}
                >
                  <option value="">Select exam type</option>
                  {examTypes.map((examType) => (
                    <option key={examType._id} value={examType._id}>
                      {examType.type}
                    </option>
                  ))}
                </select>
                {errors.exam_type && (
                  <p className="mt-1 text-sm text-red-500">{errors.exam_type}</p>
                )}
              </div>

              {/* Term and Academic Year */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Term
                  </label>
                  <select
                    value={formData.term}
                    onChange={(e) => handleInputChange("term", e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="First Term">First Term</option>
                    <option value="Second Term">Second Term</option>
                    <option value="Third Term">Third Term</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Academic Year
                  </label>
                  <input
                    type="text"
                    value={formData.academic_year}
                    onChange={(e) => handleInputChange("academic_year", e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white ${
                      errors.academic_year 
                        ? "border-red-500 dark:border-red-500" 
                        : "border-gray-300 dark:border-gray-600"
                    }`}
                    placeholder="2024-2025"
                  />
                  {errors.academic_year && (
                    <p className="mt-1 text-sm text-red-500">{errors.academic_year}</p>
                  )}
                </div>
              </div>

              {/* Score and Grade */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Score (0-100)
                  </label>
                  <input
                    type="number"
                    min="0"
                    max="100"
                    step="0.1"
                    value={formData.score}
                    onChange={(e) => handleInputChange("score", e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white ${
                      errors.score 
                        ? "border-red-500 dark:border-red-500" 
                        : "border-gray-300 dark:border-gray-600"
                    }`}
                    placeholder="85.5"
                  />
                  {errors.score && (
                    <p className="mt-1 text-sm text-red-500">{errors.score}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Grade (Auto-calculated)
                  </label>
                  <input
                    type="text"
                    value={formData.grade}
                    onChange={(e) => handleInputChange("grade", e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white"
                    placeholder="A"
                  />
                </div>
              </div>

              {/* Comments */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Comments (Optional)
                </label>
                <textarea
                  value={formData.comments}
                  onChange={(e) => handleInputChange("comments", e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Additional comments about the student's performance..."
                />
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  {isSubmitting ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Save className="h-4 w-4" />
                  )}
                  <span>{isSubmitting ? "Saving..." : (isEditing ? "Update" : "Create")}</span>
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
}
