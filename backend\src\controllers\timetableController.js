const ClassSchedule = require('../models/ClassSchedule');
const Class = require('../models/Class');
const Subject = require('../models/Subject');
const User = require('../models/User');
const Period = require('../models/Periods');
const ActivityLog = require('../models/ActivityLog');

// Get timetable for a school with filters
const getTimetable = async (req, res) => {
  try {
    const { school_id } = req.params;
    const { class_id, teacher_id, day_of_week } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Build filter object
    const filter = { school_id };
    if (class_id) filter.class_id = class_id;
    if (teacher_id) filter.teacher_id = teacher_id;
    if (day_of_week) filter.day_of_week = day_of_week;

    // Get schedule records with populated data
    const scheduleRecords = await ClassSchedule.find(filter)
      .populate('class_id', 'name grade_level')
      .populate('subject_id', 'name')
      .populate('teacher_id', 'first_name last_name')
      .populate('period_id', 'period_number start_time end_time')
      .sort({ day_of_week: 1, 'period_id.period_number': 1 })
      .lean();

    // Format the response
    const formattedRecords = scheduleRecords.map(record => ({
      _id: record._id,
      class_name: record.class_id?.name || 'Unknown Class',
      subject_name: record.subject_id?.name || 'Unknown Subject',
      teacher_name: record.teacher_id ? 
        `${record.teacher_id.first_name} ${record.teacher_id.last_name}` : 'No Teacher Assigned',
      period_number: record.period_id?.period_number || 0,
      start_time: record.period_id?.start_time || '00:00:00',
      end_time: record.period_id?.end_time || '00:00:00',
      day_of_week: record.day_of_week,
      schedule_type: record.schedule_type
    }));

    res.status(200).json({
      schedule_records: formattedRecords,
      message: 'Timetable retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching timetable:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get timetable organized by days and periods
const getOrganizedTimetable = async (req, res) => {
  try {
    const { school_id } = req.params;
    const { class_id, teacher_id } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Build filter object
    const filter = { school_id };
    if (class_id) filter.class_id = class_id;
    if (teacher_id) filter.teacher_id = teacher_id;

    // Get all periods for the school
    const periods = await Period.find({ school_id })
      .sort({ period_number: 1 })
      .lean();

    // Get schedule records
    const scheduleRecords = await ClassSchedule.find(filter)
      .populate('class_id', 'name grade_level')
      .populate('subject_id', 'name')
      .populate('teacher_id', 'name')
      .populate('period_id', 'period_number start_time end_time')
      .lean();

    // Organize by day and period
    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
    const organizedTimetable = {};

    days.forEach(day => {
      organizedTimetable[day] = {};
      periods.forEach(period => {
        organizedTimetable[day][period.period_number] = null;
      });
    });

    // Fill in the schedule data
    scheduleRecords.forEach(record => {
      if (organizedTimetable[record.day_of_week] && record.period_id) {
        organizedTimetable[record.day_of_week][record.period_id.period_number] = {
          _id: record._id,
          class_name: record.class_id?.name || 'Unknown Class',
          subject_name: record.subject_id?.name || 'Unknown Subject',
          teacher_name: record.teacher_id ? 
            `${record.teacher_id.name}` : 'No Teacher Assigned',
          period_number: record.period_id.period_number,
          start_time: record.period_id.start_time,
          end_time: record.period_id.end_time,
          day_of_week: record.day_of_week,
          schedule_type: record.schedule_type
        };
      }
    });

    res.status(200).json({
      timetable: organizedTimetable,
      periods: periods,
      message: 'Organized timetable retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching organized timetable:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get teacher's personal timetable
const getTeacherTimetable = async (req, res) => {
  try {
    const { school_id, teacher_id } = req.params;

    if (!school_id || !teacher_id) {
      return res.status(400).json({ message: 'School ID and Teacher ID are required' });
    }

    // Get all periods for the school
    const periods = await Period.find({ school_id })
      .sort({ period_number: 1 })
      .lean();

    // Get teacher's schedule records
    const scheduleRecords = await ClassSchedule.find({ 
      school_id, 
      teacher_id 
    })
      .populate('class_id', 'name grade_level')
      .populate('subject_id', 'name')
      .populate('period_id', 'period_number start_time end_time')
      .lean();

    // Get student count for each class
    const Student = require('../models/Student');
    const classIds = [...new Set(scheduleRecords.map(record => record.class_id?._id).filter(Boolean))];
    
    const studentCounts = {};
    for (const classId of classIds) {
      const count = await Student.countDocuments({ class_id: classId, school_id });
      studentCounts[classId.toString()] = count;
    }

    // Organize by day and period
    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
    const organizedTimetable = {};

    days.forEach(day => {
      organizedTimetable[day] = {};
      periods.forEach(period => {
        organizedTimetable[day][period.period_number] = null;
      });
    });

    // Fill in the schedule data
    scheduleRecords.forEach(record => {
      if (organizedTimetable[record.day_of_week] && record.period_id) {
        organizedTimetable[record.day_of_week][record.period_id.period_number] = {
          _id: record._id,
          class_name: record.class_id?.name || 'Unknown Class',
          subject_name: record.subject_id?.name || 'Unknown Subject',
          period_number: record.period_id.period_number,
          start_time: record.period_id.start_time,
          end_time: record.period_id.end_time,
          day_of_week: record.day_of_week,
          student_count: studentCounts[record.class_id?._id?.toString()] || 0,
          schedule_type: record.schedule_type
        };
      }
    });

    res.status(200).json({
      timetable: organizedTimetable,
      periods: periods,
      teacher_stats: {
        total_classes: scheduleRecords.length,
        different_classes: new Set(scheduleRecords.map(r => r.class_id?._id)).size,
        total_students: Object.values(studentCounts).reduce((sum, count) => sum + count, 0),
        subjects: [...new Set(scheduleRecords.map(r => r.subject_id?.name).filter(Boolean))]
      },
      message: 'Teacher timetable retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching teacher timetable:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Create a new schedule entry
const createScheduleEntry = async (req, res) => {
  try {
    const { school_id } = req.params;
    const { 
      class_id, 
      subject_id, 
      teacher_id, 
      period_id, 
      day_of_week, 
      schedule_type = 'Normal' 
    } = req.body;

    if (!school_id || !class_id || !subject_id || !period_id || !day_of_week) {
      return res.status(400).json({ 
        message: 'School ID, class ID, subject ID, period ID, and day of week are required' 
      });
    }

    // Check if schedule already exists for this class, period, and day
    const existingSchedule = await ClassSchedule.findOne({
      school_id,
      class_id,
      period_id,
      day_of_week
    });

    if (existingSchedule) {
      return res.status(400).json({ 
        message: 'Schedule already exists for this class, period, and day' 
      });
    }

    // Check if teacher is available at this time (if teacher is assigned)
    if (teacher_id) {
      const teacherConflict = await ClassSchedule.findOne({
        school_id,
        teacher_id,
        period_id,
        day_of_week
      });

      if (teacherConflict) {
        return res.status(400).json({ 
          message: 'Teacher is not available at this time' 
        });
      }
    }

    const newSchedule = new ClassSchedule({
      school_id,
      class_id,
      subject_id,
      teacher_id,
      period_id,
      day_of_week,
      schedule_type
    });

    await newSchedule.save();

    // Populate the created schedule for response
    const populatedSchedule = await ClassSchedule.findById(newSchedule._id)
      .populate('class_id', 'name grade_level')
      .populate('subject_id', 'name')
      .populate('teacher_id', 'first_name last_name')
      .populate('period_id', 'period_number start_time end_time');

    // Log activity
    await ActivityLog.logActivity({
      user_id: req.user.id,
      school_id: school_id,
      action: 'schedule_created',
      target_type: 'class',
      target_id: class_id,
      target_name: populatedSchedule.class_id?.name,
      details: {
        subject: populatedSchedule.subject_id?.name,
        teacher: populatedSchedule.teacher_id ? 
          `${populatedSchedule.teacher_id.first_name} ${populatedSchedule.teacher_id.last_name}` : 'No Teacher',
        period: populatedSchedule.period_id?.period_number,
        day: day_of_week
      },
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    res.status(201).json({
      schedule: populatedSchedule,
      message: 'Schedule entry created successfully'
    });
  } catch (error) {
    console.error('Error creating schedule entry:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get timetable statistics
const getTimetableStats = async (req, res) => {
  try {
    const { school_id } = req.params;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Get total periods
    const totalPeriods = await Period.countDocuments({ school_id });

    // Get total schedule entries
    const totalScheduleEntries = await ClassSchedule.countDocuments({ school_id });

    // Get unique teachers
    const uniqueTeachers = await ClassSchedule.distinct('teacher_id', { school_id });

    // Get unique subjects
    const uniqueSubjects = await ClassSchedule.distinct('subject_id', { school_id });

    // Get unique classes
    const uniqueClasses = await ClassSchedule.distinct('class_id', { school_id });

    // Calculate free periods (total possible slots - used slots)
    const totalPossibleSlots = totalPeriods * 5; // 5 days a week
    const usedSlots = totalScheduleEntries;
    const freeSlots = totalPossibleSlots - usedSlots;

    res.status(200).json({
      stats: {
        total_periods: totalPeriods,
        total_schedule_entries: totalScheduleEntries,
        unique_teachers: uniqueTeachers.filter(id => id).length,
        unique_subjects: uniqueSubjects.length,
        unique_classes: uniqueClasses.length,
        free_slots: freeSlots,
        utilization_rate: totalPossibleSlots > 0 ? 
          Math.round((usedSlots / totalPossibleSlots) * 100) : 0
      },
      message: 'Timetable statistics retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching timetable stats:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

module.exports = {
  getTimetable,
  getOrganizedTimetable,
  getTeacherTimetable,
  createScheduleEntry,
  getTimetableStats
};
