export interface ExamTypeSchema extends Record<string, unknown> {
  _id: string;
  school_id: string;        // MongoDB ObjectId as string
  type: string;             // e.g., "Midterm", "Final", "Mock"
  createdAt?: string;       // Auto-generated timestamp
  updatedAt?: string;       // Auto-generated timestamp
}

export interface ExamTypeCreateSchema extends Record<string, unknown> {
  school_id: string;        // Required
  type: string;             // Required
}

export interface ExamTypeUpdateSchema extends Record<string, unknown> {
  _id: string;              // Required to identify the record
  school_id?: string;       // Optional
  type?: string;            // Optional
}

export interface ExamTypeDeleteSchema extends Record<string, unknown> {
  _id: string;              // Required MongoDB ID to delete
}
