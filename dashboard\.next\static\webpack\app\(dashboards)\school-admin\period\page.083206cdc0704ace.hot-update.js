"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/period/page",{

/***/ "(app-pages-browser)/./src/components/modals/PeriodModal.tsx":
/*!***********************************************!*\
  !*** ./src/components/modals/PeriodModal.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PeriodModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Clock_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction PeriodModal(param) {\n    let { isOpen, onClose, onSubmit, period, existingPeriods, loading = false } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        period_number: \"\",\n        start_time: \"\",\n        end_time: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isEditing = !!period;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PeriodModal.useEffect\": ()=>{\n            if (isOpen) {\n                if (period) {\n                    setFormData({\n                        period_number: period.period_number.toString(),\n                        start_time: period.start_time.slice(0, 5),\n                        end_time: period.end_time.slice(0, 5)\n                    });\n                } else {\n                    // Auto-suggest next period number\n                    const maxPeriod = Math.max(...existingPeriods.map({\n                        \"PeriodModal.useEffect.maxPeriod\": (p)=>p.period_number\n                    }[\"PeriodModal.useEffect.maxPeriod\"]), 0);\n                    setFormData({\n                        period_number: (maxPeriod + 1).toString(),\n                        start_time: \"\",\n                        end_time: \"\"\n                    });\n                }\n                setErrors({});\n            }\n        }\n    }[\"PeriodModal.useEffect\"], [\n        isOpen,\n        period,\n        existingPeriods\n    ]);\n    const validateForm = ()=>{\n        const newErrors = {};\n        // Validate period number\n        const periodNum = parseInt(formData.period_number);\n        if (!periodNum || periodNum < 1) {\n            newErrors.period_number = \"Period number must be a positive number\";\n        } else {\n            // Check for duplicate period number (only when creating or changing number)\n            const isDuplicate = existingPeriods.some((p)=>p.period_number === periodNum && (!period || p._id !== period._id));\n            if (isDuplicate) {\n                newErrors.period_number = \"This period number already exists\";\n            }\n        }\n        // Validate times\n        if (!formData.start_time) {\n            newErrors.start_time = \"Start time is required\";\n        }\n        if (!formData.end_time) {\n            newErrors.end_time = \"End time is required\";\n        }\n        // Validate time order\n        if (formData.start_time && formData.end_time) {\n            const startTime = new Date(\"2000-01-01T\".concat(formData.start_time, \":00\"));\n            const endTime = new Date(\"2000-01-01T\".concat(formData.end_time, \":00\"));\n            if (startTime >= endTime) {\n                newErrors.end_time = \"End time must be after start time\";\n            }\n            // Check for time conflicts with other periods\n            const hasConflict = existingPeriods.some((p)=>{\n                if (period && p._id === period._id) return false; // Skip self when editing\n                const pStart = new Date(\"2000-01-01T\".concat(p.start_time));\n                const pEnd = new Date(\"2000-01-01T\".concat(p.end_time));\n                return startTime >= pStart && startTime < pEnd || endTime > pStart && endTime <= pEnd || startTime <= pStart && endTime >= pEnd;\n            });\n            if (hasConflict) {\n                newErrors.start_time = \"Time conflicts with existing period\";\n                newErrors.end_time = \"Time conflicts with existing period\";\n            }\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) return;\n        setIsSubmitting(true);\n        try {\n            const submitData = {\n                period_number: parseInt(formData.period_number),\n                start_time: \"\".concat(formData.start_time, \":00\"),\n                end_time: \"\".concat(formData.end_time, \":00\")\n            };\n            await onSubmit(submitData);\n            onClose();\n        } catch (error) {\n            console.error(\"Error submitting period:\", error);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Convert French time format (17h) to 24-hour format (17:00)\n    const convertFrenchTime = (value)=>{\n        // Check if it's French format (e.g., \"17h\", \"17h30\", \"9h\", \"9h15\")\n        const frenchTimeRegex = /^(\\d{1,2})h(\\d{0,2})$/;\n        const match = value.match(frenchTimeRegex);\n        if (match) {\n            const hours = match[1].padStart(2, '0');\n            const minutes = match[2] ? match[2].padStart(2, '0') : '00';\n            return \"\".concat(hours, \":\").concat(minutes);\n        }\n        return value; // Return as-is if not French format\n    };\n    const handleInputChange = (field, value)=>{\n        // Convert French time format if applicable\n        if ((field === 'start_time' || field === 'end_time') && value.includes('h')) {\n            value = convertFrenchTime(value);\n        }\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: \"\"\n                }));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n        children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 z-50 flex items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"absolute inset-0 bg-black bg-opacity-50\",\n                    onClick: onClose\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        scale: 0.95,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        scale: 0.95,\n                        y: 20\n                    },\n                    className: \"relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-teal-500 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-5 w-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                    children: isEditing ? \"Edit Period\" : \"Add New Period\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                    children: isEditing ? \"Update period details\" : \"Create a new class period\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"p-6 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Period Number\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            min: \"1\",\n                                            value: formData.period_number,\n                                            onChange: (e)=>handleInputChange(\"period_number\", e.target.value),\n                                            className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 dark:bg-gray-700 dark:text-white \".concat(errors.period_number ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\"),\n                                            placeholder: \"Enter period number\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.period_number && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.period_number\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Start Time\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"time\",\n                                            value: formData.start_time,\n                                            onChange: (e)=>handleInputChange(\"start_time\", e.target.value),\n                                            className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 dark:bg-gray-700 dark:text-white \".concat(errors.start_time ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\"),\n                                            placeholder: \"08:00 or 8h\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-xs text-gray-500 dark:text-gray-400\",\n                                            children: \"Supports French format: 8h, 8h30, 17h, etc.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.start_time && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.start_time\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"End Time\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"time\",\n                                            value: formData.end_time,\n                                            onChange: (e)=>handleInputChange(\"end_time\", e.target.value),\n                                            className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 dark:bg-gray-700 dark:text-white \".concat(errors.end_time ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.end_time && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.end_time\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: onClose,\n                                            className: \"px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                            disabled: isSubmitting,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: isSubmitting,\n                                            className: \"px-4 py-2 bg-teal-500 text-white rounded-md hover:bg-teal-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\",\n                                            children: [\n                                                isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: isSubmitting ? \"Saving...\" : isEditing ? \"Update\" : \"Create\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n            lineNumber: 167,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\PeriodModal.tsx\",\n        lineNumber: 165,\n        columnNumber: 5\n    }, this);\n}\n_s(PeriodModal, \"17jEtga8JvdH/S7RGvKordgNGWI=\");\n_c = PeriodModal;\nvar _c;\n$RefreshReg$(_c, \"PeriodModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/modals/PeriodModal.tsx\n"));

/***/ })

});