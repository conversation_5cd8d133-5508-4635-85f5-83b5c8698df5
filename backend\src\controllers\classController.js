// controllers/classController.js
const Subject =require('../models/Subject')
const Class = require('../models/Class'); // Assuming you have a Class model
const { ensureUniqueId } = require('../utils/generateId'); 

const testClassResponse = (req, res) => {
  res.status(200).json({ message: 'Hi, this is class' });
};

// // Get all classes
const getAllClasses = async (req, res) => {
  try {
    const classes = await Class.find();
    res.json(classes);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

const createClass = async (req, res) => {
  try {
    const classId = await ensureUniqueId(Class, 'class_id', 'CLS');
    const newClass = new Class({ class_id: classId, ...req.body });
    await newClass.save();

    // Sync subjects: Add this new class _id to each subject's class_id array
    const subjectIds = req.body.subject_id; // assuming it's an array of subject ObjectIds

    if (Array.isArray(subjectIds) && subjectIds.length > 0) {
      await Promise.all(subjectIds.map(async (subjectId) => {
        await Subject.findByIdAndUpdate(
          subjectId,
          { $addToSet: { class_id: newClass._id } } // avoid duplicates
        );
      }));
    }

    res.status(201).json(newClass);
  } catch (err) {
    console.error(err);
    res.status(400).json({ message: err.message });
  }
};


// // Get a class by ID
const getClassById = async (req, res) => {
  try {
    const classItem = await Class.findOne({class_id:req.params.id});
    if (!classItem) {
      return res.status(404).json({ message: 'Class not found' });
    }
    res.json(classItem);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

const updateClassById = async (req, res) => {
  try {
    // Find existing class to compare subject references
    const existingClass = await Class.findById(req.params.id);

    if (!existingClass) {
      return res.status(404).json({ message: 'Class not found' });
    }

    // Update the class document
    const updatedClass = await Class.findByIdAndUpdate(req.params.id, req.body, { new: true });

    // Old and new subject IDs (as strings)
    const oldSubjectIds = existingClass.subject_id.map(id => id.toString());
    const newSubjectIds = Array.isArray(req.body.subject_id) 
      ? req.body.subject_id.map(id => id.toString()) 
      : [];

    // Subjects to add this class to
    const subjectsToAdd = newSubjectIds.filter(id => !oldSubjectIds.includes(id));
    // Subjects to remove this class from
    const subjectsToRemove = oldSubjectIds.filter(id => !newSubjectIds.includes(id));

    // Add class reference to new subjects
    await Promise.all(subjectsToAdd.map(async subjectId => {
      await Subject.findByIdAndUpdate(subjectId, { $addToSet: { class_id: updatedClass._id } });
    }));

    // Remove class reference from removed subjects
    await Promise.all(subjectsToRemove.map(async subjectId => {
      await Subject.findByIdAndUpdate(subjectId, { $pull: { class_id: updatedClass._id } });
    }));

    res.json(updatedClass);
  } catch (err) {
    console.error(err);
    res.status(400).json({ message: err.message });
  }
};

const deleteClassById = async (req, res) => {
  try {
    // Find class to get linked subjects
    const classToDelete = await Class.findById(req.params.id);

    if (!classToDelete) {
      return res.status(404).json({ message: 'Class not found' });
    }

    // Remove this class _id from all linked subjects' class_id arrays
    if (classToDelete.subject_id && classToDelete.subject_id.length > 0) {
      await Promise.all(classToDelete.subject_id.map(async subjectId => {
        await Subject.findByIdAndUpdate(subjectId, { $pull: { class_id: classToDelete._id } });
      }));
    }

    // Delete the class
    await Class.findByIdAndDelete(req.params.id);

    res.json({ message: 'Class deleted successfully' });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

const deleteMultipleClasses = async (req, res) => {
  const { ids } = req.body; // array of class _ids

  if (!Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({ message: 'Invalid input: ids should be a non-empty array' });
  }

  try {
    // Find classes to delete to get their subject refs
    const classesToDelete = await Class.find({ _id: { $in: ids } });

    if (classesToDelete.length === 0) {
      return res.status(404).json({ message: 'No class records found for the provided IDs' });
    }

    // Remove class references from linked subjects
    await Promise.all(classesToDelete.map(async classDoc => {
      if (classDoc.subject_id && classDoc.subject_id.length > 0) {
        await Promise.all(classDoc.subject_id.map(async subjectId => {
          await Subject.findByIdAndUpdate(subjectId, { $pull: { class_id: classDoc._id } });
        }));
      }
    }));

    // Delete the classes
    const result = await Class.deleteMany({ _id: { $in: ids } });

    res.json({ message: `${result.deletedCount} class records deleted successfully` });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

// Delete ALL class records
const deleteAllClasses = async (req, res) => {
  try {
    const classCount = await Class.countDocuments();

    if (classCount === 0) {
      return res.status(404).json({ message: 'No classes found to delete' });
    }

    // Find all classes to collect subject refs
    const allClasses = await Class.find({}, '_id subject_id');

    // Collect all subject IDs referenced by classes
    const subjectIdSet = new Set();
    allClasses.forEach(cls => {
      if (cls.subject_id && cls.subject_id.length > 0) {
        cls.subject_id.forEach(sid => subjectIdSet.add(sid.toString()));
      }
    });
    const allSubjectIds = Array.from(subjectIdSet);

    // Remove all class references from these subjects
    if (allSubjectIds.length > 0) {
      await Promise.all(allSubjectIds.map(async subjectId => {
        await Subject.findByIdAndUpdate(subjectId, { $set: { class_id: [] } });
      }));
    }

    // Delete all classes
    const result = await Class.deleteMany({});

    res.json({
      message: `All ${result.deletedCount} class records deleted successfully`,
      deletedCount: result.deletedCount
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

module.exports = {
  testClassResponse,
  getAllClasses,
  createClass,
  getClassById,
  updateClassById,
  deleteClassById,
  deleteMultipleClasses,
  deleteAllClasses,
};
