const express = require('express');
const router = express.Router();
const staffController = require('../controllers/staffController');
const { authenticate, authorize, checkSubscription } = require('../middleware/middleware');

// Test routes
router.get('/test', staffController.testStaffResponse);
router.get('/test-firebase', staffController.testFirebaseIntegration);

// Utility route for syncing existing staff with Firebase
router.post('/sync-firebase/:school_id',
  authenticate,
  checkSubscription,
  authorize(['admin', 'super']),
  staffController.syncStaffWithFirebase
);

// GET staff member by ID
router.get('/get-staff/:staff_id',
  authenticate,
  checkSubscription,
  authorize(['admin', 'school_admin', 'super']),
  staffController.getStaffById
);

// GET all staff for a specific school
router.get('/get-staff-by-school/:school_id',
  authenticate,
  checkSubscription,
  authorize(['admin', 'school_admin', 'super']),
  staffController.getStaffBySchool
);

// GET search teachers
router.get('/search-teachers',
  authenticate, 
  checkSubscription, 
  authorize(['admin', 'school_admin', 'super']), 
  staffController.searchTeachers
);

// POST create new staff member
router.post('/create-staff', 
  authenticate, 
  checkSubscription, 
  authorize(['admin', 'school_admin', 'super']), 
  staffController.createStaff
);

// PUT update staff member
router.put('/update-staff/:staff_id', 
  authenticate, 
  checkSubscription, 
  authorize(['admin', 'school_admin', 'super']), 
  staffController.updateStaff
);

// DELETE staff member (remove from school)
router.delete('/delete-staff/:staff_id', 
  authenticate, 
  checkSubscription, 
  authorize(['admin', 'school_admin', 'super']), 
  staffController.deleteStaff
);

// POST reset staff password
router.post('/reset-password/:staff_id', 
  authenticate, 
  checkSubscription, 
  authorize(['admin', 'school_admin', 'super']), 
  staffController.resetStaffPassword
);

// POST generate access code for teacher
router.post('/generate-access-code', 
  authenticate, 
  checkSubscription, 
  authorize(['admin', 'school_admin', 'super']), 
  staffController.generateAccessCode
);

module.exports = router;
