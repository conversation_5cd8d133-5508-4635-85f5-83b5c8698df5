const express = require('express');
const gradeController = require('../controllers/gradeController'); // Updated controller import
const { authenticate, authorize, checkSubscription } = require('../middleware/middleware');
const { checkTeacherSchoolAccess, checkTeacherClassSubjectAccess } = require('../middleware/teacherMiddleware');


const router = express.Router();
// router.get('/test', gradeController.testGradeResponse); // Updated route to match grade

// GET /grades to fetch all grade records
router.get('/get-grades' , authenticate, checkSubscription, authorize(['admin', 'super', 'parent', 'teacher']) , gradeController.getAllGrades);

// GET /grade by id
router.get('/get-grade/:id' , authenticate, checkSubscription, authorize(['admin', 'super', 'parent', 'teacher']) , gradeController.getGradeById);

// POST /grades to create a new grade record
router.post('/create-grade' , authenticate, authorize(['admin', 'super', 'teacher']), checkTeacherClassSubjectAccess, gradeController.createGrade);

// PUT /grades/:id to update a specific grade record
router.put('/update-grade/:id' , authenticate, authorize(['admin', 'super', 'teacher']) , gradeController.updateGradeById);

// DELETE /grades/:id to delete a specific grade record
router.delete('/delete-grade/:id' , authenticate, authorize(['admin', 'super', 'teacher']) , gradeController.deleteGradeById);

//DELETE multiple grades
router.delete('/delete-grades', authenticate, authorize(['admin', 'super', 'teacher']), gradeController.deleteMultipleGrades);

// New routes for school-specific grades with teacher access control
router.get('/school/:school_id', authenticate, checkSubscription, authorize(['admin', 'super', 'school_admin', 'teacher']), checkTeacherSchoolAccess, gradeController.getGradeRecords);
router.get('/school/:school_id/stats', authenticate, checkSubscription, authorize(['admin', 'super', 'school_admin', 'teacher']), checkTeacherSchoolAccess, gradeController.getGradeStats);

module.exports = router;
