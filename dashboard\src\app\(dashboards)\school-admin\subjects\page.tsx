"use client";

import { Presentation, NotebookPen } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import CircularLoader from "@/components/widgets/CircularLoader";
import React, { Suspense, useEffect, useState } from "react";
import useAuth from "@/app/hooks/useAuth";
import { SubjectSchema } from "@/app/models/Subject";
import { getStudentById } from "@/app/services/StudentServices";
import { getSubjectsBySchoolId } from "@/app/services/SubjectServices";
import DataTableFix from '@/components/utils/TableFix';
import { SchoolSchema } from "@/app/models/SchoolModel";
import { ClassSchema } from "@/app/models/ClassModel";
import { getClassesBySchool } from "@/app/services/ClassServices";
import { getSchoolBy_id } from "@/app/services/SchoolServices";
import { getEntityNames } from "@/components/utils/getEntityNames";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import NotificationCard from "@/components/NotificationCard";
import CreateSubjectModal from "./component/CreateSubjectModal";

const BASE_URL = "/school-admin";

function Subject() {
  const router = useRouter();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [subjects, setSubjects] = useState<SubjectSchema[]>([]);
  const [school, setSchool] = useState<SchoolSchema>({} as SchoolSchema);
  const [classes, setClasses] = useState<ClassSchema[]>([]);
  const [selectedClass, setSelectedClass] = useState<string>("all");
  const [subjectToDelete, setSubjectToDelete] = useState<SubjectSchema | null>(null);
  const [loadingData, setLoadingData] = useState(false);
  const [isNotificationCard, setIsNotificationCard] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState("");
  const [notificationType, setNotificationType] = useState<"success" | "error" | "info" | "warning">("success");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<"success" | "failure" | null>(null);
  const { user } = useAuth();
  const schoolId = user?.school_ids?.[0] ?? null;

  const fetchAllData = async () => {
    try {
      const [subjectsResult, schoolResult, classesResult] = await Promise.all([
        getSubjectsBySchoolId(schoolId as string),
        getSchoolBy_id(schoolId as string),
        getClassesBySchool(schoolId as string),
      ]);

      setSubjects(subjectsResult);
      setSchool(schoolResult);
      setClasses(classesResult);
    } catch (error) {
      console.error("Failed to fetch resources:", error);
    }
  };

  useEffect(() => {
    fetchAllData();
  }, [schoolId]);

  // Filter logic
  const filteredSubjects = selectedClass === "all"
    ? subjects
    : subjects.filter(subject => subject.class_id === selectedClass);

  const columns = [
    { header: "Subject Code", accessor: (row: SubjectSchema) => row.subject_code },
    { header: "Name", accessor: (row: SubjectSchema) => row.name || "N/A" },
    {
      header: "Class",
      accessor: (row: SubjectSchema) =>
        getEntityNames([row.class_id], classes, "ClassName")
    },
    { header: "Coefficient", accessor: (row: SubjectSchema) => row.coefficient || "N/A" },
    {
      header: "Compulsory",
      accessor: (row: SubjectSchema) => (row.compulsory ? "Yes" : "No"),
    },
  ];

  const actions = [
    {
      label: "View",
      onClick: (sub: SubjectSchema) => {
        router.push(`${BASE_URL}/subject/view?id=${sub._id}`);
      },
    },
    {
      label: "Delete",
      onClick: (sub: SubjectSchema) => {
        setSubjectToDelete(sub);
      },
    },
  ];

  const handleCreateSubject = async (subjectData: SubjectSchema) => {

  }
  return (
    <div>
      {isNotificationCard && (
        <NotificationCard
          title="Notification"
          icon={
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z" stroke="#15803d" strokeWidth="1.5" />
              <path d="M7.75 11.9999L10.58 14.8299L16.25 9.16992" stroke="#15803d" strokeWidth="1.5" />
            </svg>
          }
          message={notificationMessage}
          onClose={() => setIsNotificationCard(false)}
          type={notificationType}
          isVisible={isNotificationCard}
          isFixed={true}
        />
      )}
      <div className="flex justify-between items-center mb-4">
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          transition={{ type: 'spring', stiffness: 300 }}
          onClick={() => setIsModalOpen(true)}
          className="px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600"
        >
          Add Subject
        </motion.button>

        {/* Class Filter Dropdown */}
        <select
          value={selectedClass}
          onChange={(e) => setSelectedClass(e.target.value)}
          className="px-3 py-2 border rounded-md text-sm bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-teal "
        >
          <option value="all">All Classes</option>
          {classes.map((cls) => (
            <option key={cls._id} value={cls._id}>
              {String(cls.name)}
            </option>
          ))}
        </select>
      </div>

      <DataTableFix
        columns={columns}
        data={filteredSubjects}
        actions={actions}
        defaultItemsPerPage={5}
        loading={loadingData}
        onLoadingChange={setLoadingData}
        showCheckbox={true}
      />

      {isModalOpen && (
        <CreateSubjectModal
          onClose={() => { setIsModalOpen(false); setSubmitStatus(null); }}
          onSave={handleCreateSubject}
          submitStatus={submitStatus}
          isSubmitting={isSubmitting}
          SchoolId={schoolId as string}
        />
      )}
    </div>
  );
}

export default function Page() {
  const navigation = {
    icon: NotebookPen,
    baseHref: `${BASE_URL}/subjects`,
    title: "Subjects"
  };
  const { logout } = useAuth();

  return (
    <Suspense fallback={
      <div>
        <div className="flex justify-center items-center h-screen absolute top-0 left-0 z-50">
          <CircularLoader size={32} color="teal" />
        </div>
      </div>
    }>
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        <Subject />
      </SchoolLayout>
    </Suspense>
  );
}
