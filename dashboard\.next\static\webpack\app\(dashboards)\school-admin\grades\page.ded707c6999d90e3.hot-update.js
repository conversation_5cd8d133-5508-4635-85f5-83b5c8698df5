"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/grades/page",{

/***/ "(app-pages-browser)/./src/app/(dashboards)/school-admin/grades/page.tsx":
/*!***********************************************************!*\
  !*** ./src/app/(dashboards)/school-admin/grades/page.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GradesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/percent.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Dashboard/Layouts/SchoolLayout */ \"(app-pages-browser)/./src/components/Dashboard/Layouts/SchoolLayout.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_utils_TableFix__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/utils/TableFix */ \"(app-pages-browser)/./src/components/utils/TableFix.tsx\");\n/* harmony import */ var _components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/widgets/CircularLoader */ \"(app-pages-browser)/./src/components/widgets/CircularLoader.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var _components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/utils/ProtectedRoute */ \"(app-pages-browser)/./src/components/utils/ProtectedRoute.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/services/GradeServices */ \"(app-pages-browser)/./src/app/services/GradeServices.tsx\");\n/* harmony import */ var _app_services_StudentServices__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/services/StudentServices */ \"(app-pages-browser)/./src/app/services/StudentServices.tsx\");\n/* harmony import */ var _app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/services/SubjectServices */ \"(app-pages-browser)/./src/app/services/SubjectServices.tsx\");\n/* harmony import */ var _app_services_ExamTypeServices__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/services/ExamTypeServices */ \"(app-pages-browser)/./src/app/services/ExamTypeServices.tsx\");\n/* harmony import */ var _components_modals_GradeModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/modals/GradeModal */ \"(app-pages-browser)/./src/components/modals/GradeModal.tsx\");\n/* harmony import */ var _components_modals_DeleteConfirmationModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/modals/DeleteConfirmationModal */ \"(app-pages-browser)/./src/components/modals/DeleteConfirmationModal.tsx\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst navigation = {\n    icon: _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n    baseHref: \"/school-admin/grades\",\n    title: \"Grades\"\n};\nfunction GradesPage() {\n    var _user_school_ids;\n    _s();\n    const { logout, user } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const { toasts, removeToast, showSuccess, showError } = (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_13__.useToast)();\n    // State management\n    const [gradeRecords, setGradeRecords] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        totalGrades: 0,\n        averageScore: 0,\n        highestScore: 0,\n        lowestScore: 0,\n        passRate: 0\n    });\n    const [loadingData, setLoadingData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [selectedClass, setSelectedClass] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedSubject, setSelectedSubject] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedTerm, setSelectedTerm] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedExamType, setSelectedExamType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    // Modal states\n    const [isGradeModalOpen, setIsGradeModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isDeleteModalOpen, setIsDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [gradeToEdit, setGradeToEdit] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [gradeToDelete, setGradeToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [deleteType, setDeleteType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"single\");\n    const [selectedGrades, setSelectedGrades] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Additional data for forms\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [examTypes, setExamTypes] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Loading states\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [clearSelection, setClearSelection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Get school ID from user\n    const schoolId = (user === null || user === void 0 ? void 0 : (_user_school_ids = user.school_ids) === null || _user_school_ids === void 0 ? void 0 : _user_school_ids[0]) || (user === null || user === void 0 ? void 0 : user.school_id);\n    // Fetch grade data from API\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"GradesPage.useEffect\": ()=>{\n            const fetchGradeData = {\n                \"GradesPage.useEffect.fetchGradeData\": async ()=>{\n                    if (!schoolId) {\n                        showError(\"Error\", \"No school ID found\");\n                        setLoadingData(false);\n                        return;\n                    }\n                    try {\n                        setLoadingData(true);\n                        // Build filters\n                        const filters = {};\n                        if (selectedClass !== 'all') filters.class_id = selectedClass;\n                        if (selectedSubject !== 'all') filters.subject_id = selectedSubject;\n                        if (selectedTerm !== 'all') filters.term = selectedTerm;\n                        if (selectedExamType !== 'all') filters.exam_type_id = selectedExamType;\n                        // Fetch records and stats in parallel\n                        const [recordsResponse, statsResponse] = await Promise.all([\n                            (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeRecords)(schoolId, filters),\n                            (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeStats)(schoolId, filters)\n                        ]);\n                        setGradeRecords(recordsResponse.grade_records);\n                        setStats(statsResponse.stats);\n                    } catch (error) {\n                        console.error(\"Error fetching grade data:\", error);\n                        showError(\"Error\", \"Failed to load grade data\");\n                    } finally{\n                        setLoadingData(false);\n                    }\n                }\n            }[\"GradesPage.useEffect.fetchGradeData\"];\n            fetchGradeData();\n        }\n    }[\"GradesPage.useEffect\"], [\n        schoolId,\n        selectedClass,\n        selectedSubject,\n        selectedTerm,\n        selectedExamType\n    ]);\n    // Fetch additional data for modals\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"GradesPage.useEffect\": ()=>{\n            const fetchAdditionalData = {\n                \"GradesPage.useEffect.fetchAdditionalData\": async ()=>{\n                    if (!schoolId) return;\n                    try {\n                        // Fetch data with individual error handling\n                        const results = await Promise.allSettled([\n                            (0,_app_services_StudentServices__WEBPACK_IMPORTED_MODULE_8__.getStudentsBySchool)(schoolId),\n                            (0,_app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_9__.getAllSubjects)(),\n                            (0,_app_services_ExamTypeServices__WEBPACK_IMPORTED_MODULE_10__.getExamTypes)()\n                        ]);\n                        // Handle each result individually\n                        if (results[0].status === 'fulfilled') {\n                            setStudents(results[0].value);\n                        } else {\n                            console.error(\"Failed to fetch students:\", results[0].reason);\n                            setStudents([]);\n                        }\n                        if (results[1].status === 'fulfilled') {\n                            setSubjects(results[1].value);\n                        } else {\n                            console.error(\"Failed to fetch subjects:\", results[1].reason);\n                            setSubjects([]);\n                        }\n                        if (results[2].status === 'fulfilled') {\n                            setExamTypes(results[2].value);\n                        } else {\n                            console.error(\"Failed to fetch exam types:\", results[2].reason);\n                            setExamTypes([]);\n                        }\n                        // Show warning if any critical data failed to load\n                        const anyDataFailed = results.some({\n                            \"GradesPage.useEffect.fetchAdditionalData.anyDataFailed\": (result)=>result.status === 'rejected'\n                        }[\"GradesPage.useEffect.fetchAdditionalData.anyDataFailed\"]);\n                        if (anyDataFailed) {\n                            showError(\"Warning\", \"Some form data could not be loaded. Some features may be limited.\");\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching additional data:\", error);\n                        showError(\"Error\", \"Failed to load form data\");\n                    }\n                }\n            }[\"GradesPage.useEffect.fetchAdditionalData\"];\n            fetchAdditionalData();\n        }\n    }[\"GradesPage.useEffect\"], [\n        schoolId\n    ]); // Removed showError from dependencies\n    // CRUD Functions\n    const handleCreateGrade = ()=>{\n        setGradeToEdit(null);\n        setIsGradeModalOpen(true);\n    };\n    const handleEditGrade = (grade)=>{\n        setGradeToEdit(grade);\n        setIsGradeModalOpen(true);\n    };\n    const handleDeleteGrade = (grade)=>{\n        setGradeToDelete(grade);\n        setDeleteType(\"single\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleDeleteMultiple = ()=>{\n        setDeleteType(\"multiple\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleSelectionChange = (selectedRows)=>{\n        setSelectedGrades(selectedRows);\n    };\n    // Modal submission functions\n    const handleGradeSubmit = async (data)=>{\n        if (!schoolId) {\n            showError(\"Error\", \"No school ID found\");\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            if (gradeToEdit) {\n                // Update existing grade\n                await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.updateGrade)(gradeToEdit._id, data);\n            } else {\n                // Create new grade\n                await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.createGrade)({\n                    ...data,\n                    school_id: schoolId\n                });\n            }\n            // Refresh grades list\n            const filters = {};\n            if (selectedClass !== 'all') filters.class_id = selectedClass;\n            if (selectedSubject !== 'all') filters.subject_id = selectedSubject;\n            if (selectedTerm !== 'all') filters.term = selectedTerm;\n            if (selectedExamType !== 'all') filters.exam_type_id = selectedExamType;\n            const [recordsResponse, statsResponse] = await Promise.all([\n                (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeRecords)(schoolId, filters),\n                (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeStats)(schoolId, filters)\n            ]);\n            setGradeRecords(recordsResponse.grade_records);\n            setStats(statsResponse.stats);\n            setIsGradeModalOpen(false);\n            setGradeToEdit(null);\n            // Show success notification\n            showSuccess(gradeToEdit ? \"Grade Updated\" : \"Grade Added\", gradeToEdit ? \"Grade has been updated successfully.\" : \"New grade has been added successfully.\");\n        } catch (error) {\n            console.error(\"Error submitting grade:\", error);\n            showError(\"Error\", \"Failed to save grade. Please try again.\");\n            throw error;\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleDeleteConfirm = async ()=>{\n        if (!schoolId) return;\n        try {\n            if (deleteType === \"single\" && gradeToDelete) {\n                await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.deleteGrade)(gradeToDelete._id);\n            } else if (deleteType === \"multiple\") {\n                const selectedIds = selectedGrades.map((g)=>g._id);\n                await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.deleteMultipleGrades)(selectedIds);\n                setClearSelection(true);\n            }\n            // Refresh grades list\n            const filters = {};\n            if (selectedClass !== 'all') filters.class_id = selectedClass;\n            if (selectedSubject !== 'all') filters.subject_id = selectedSubject;\n            if (selectedTerm !== 'all') filters.term = selectedTerm;\n            if (selectedExamType !== 'all') filters.exam_type_id = selectedExamType;\n            const [recordsResponse, statsResponse] = await Promise.all([\n                (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeRecords)(schoolId, filters),\n                (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeStats)(schoolId, filters)\n            ]);\n            setGradeRecords(recordsResponse.grade_records);\n            setStats(statsResponse.stats);\n            setIsDeleteModalOpen(false);\n            setGradeToDelete(null);\n            setSelectedGrades([]);\n            // Show success notification\n            if (deleteType === \"single\") {\n                showSuccess(\"Grade Deleted\", \"Grade record has been deleted successfully.\");\n            } else {\n                showSuccess(\"Grades Deleted\", \"\".concat(selectedGrades.length, \" grade records have been deleted successfully.\"));\n            }\n        } catch (error) {\n            console.error(\"Error deleting grade(s):\", error);\n            showError(\"Error\", \"Failed to delete grade record(s). Please try again.\");\n            throw error;\n        }\n    };\n    const getGradeColor = (grade)=>{\n        switch(grade){\n            case 'A+':\n            case 'A':\n                return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';\n            case 'B+':\n            case 'B':\n                return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';\n            case 'C+':\n            case 'C':\n                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';\n            case 'D':\n            case 'F':\n                return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';\n            default:\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';\n        }\n    };\n    const getScoreColor = (score)=>{\n        if (score >= 90) return 'text-green-600 font-semibold';\n        if (score >= 80) return 'text-blue-600 font-semibold';\n        if (score >= 70) return 'text-yellow-600 font-semibold';\n        if (score >= 60) return 'text-orange-600 font-semibold';\n        return 'text-red-600 font-semibold';\n    };\n    // Table columns\n    const columns = [\n        {\n            header: \"Student\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-medium text-foreground\",\n                            children: row.student_name\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-foreground/60\",\n                            children: row.student_id\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Class\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-medium\",\n                    children: row.class_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Subject\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.subject_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 327,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Exam Type\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.exam_type\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 333,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Score\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-lg font-bold \".concat(getScoreColor(row.score)),\n                    children: [\n                        row.score,\n                        \"%\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 339,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Grade\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"px-2 py-1 rounded-full text-sm font-bold \".concat(getGradeColor(row.grade)),\n                    children: row.grade\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 347,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Term\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.term\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 355,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Teacher\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.teacher_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 361,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    // Actions for the table\n    const actions = [\n        {\n            label: \"Edit\",\n            onClick: (grade)=>{\n                handleEditGrade(grade);\n            }\n        },\n        {\n            label: \"Delete\",\n            onClick: (grade)=>{\n                handleDeleteGrade(grade);\n            }\n        }\n    ];\n    // Filter data based on selections\n    const filteredRecords = gradeRecords.filter((record)=>{\n        if (selectedClass !== 'all' && record.class_name !== selectedClass) return false;\n        if (selectedSubject !== 'all' && record.subject_name !== selectedSubject) return false;\n        if (selectedTerm !== 'all' && record.term !== selectedTerm) return false;\n        if (selectedExamType !== 'all' && record.exam_type !== selectedExamType) return false;\n        return true;\n    });\n    if (loadingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: 32,\n                color: \"teal\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                lineNumber: 394,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n            lineNumber: 393,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        allowedRoles: [\n            \"school_admin\",\n            \"admin\",\n            \"super\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            navigation: navigation,\n            onLogout: logout,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-teal rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: \"Grades Management\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-foreground/60\",\n                                                        children: \"Monitor and manage student grades across all subjects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-foreground\",\n                                                children: [\n                                                    stats.averageScore,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-foreground/60\",\n                                                children: \"Average Score\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-5 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Total Grades\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: stats.totalGrades\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Average Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: [\n                                                            stats.averageScore,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-teal rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Highest Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-green-600\",\n                                                        children: [\n                                                            stats.highestScore,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Lowest Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-red-600\",\n                                                        children: [\n                                                            stats.lowestScore,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 463,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Pass Rate\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: [\n                                                            stats.passRate,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 426,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4 text-foreground/60\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-foreground\",\n                                                children: \"Filters:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Class:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedClass,\n                                                onChange: (e)=>setSelectedClass(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Grade 10A\",\n                                                        children: \"Grade 10A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Grade 10B\",\n                                                        children: \"Grade 10B\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Grade 11A\",\n                                                        children: \"Grade 11A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Grade 11B\",\n                                                        children: \"Grade 11B\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Subject:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 512,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedSubject,\n                                                onChange: (e)=>setSelectedSubject(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Subjects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Mathematics\",\n                                                        children: \"Mathematics\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Physics\",\n                                                        children: \"Physics\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 520,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Chemistry\",\n                                                        children: \"Chemistry\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 521,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Biology\",\n                                                        children: \"Biology\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"English\",\n                                                        children: \"English\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 511,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Term:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedTerm,\n                                                onChange: (e)=>setSelectedTerm(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Terms\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"First Term\",\n                                                        children: \"First Term\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Second Term\",\n                                                        children: \"Second Term\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 536,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Third Term\",\n                                                        children: \"Third Term\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Exam Type:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedExamType,\n                                                onChange: (e)=>setSelectedExamType(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Types\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Mid-term Exam\",\n                                                        children: \"Mid-term Exam\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Final Exam\",\n                                                        children: \"Final Exam\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 550,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Quiz\",\n                                                        children: \"Quiz\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Lab Test\",\n                                                        children: \"Lab Test\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 552,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Assignment\",\n                                                        children: \"Assignment\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 553,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_19__.motion.button, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 562,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Export\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 563,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 557,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 489,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-foreground\",\n                                            children: [\n                                                \"Grade Records (\",\n                                                filteredRecords.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                            lineNumber: 571,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_19__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            onClick: handleCreateGrade,\n                                            className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Add Grade\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 582,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                            lineNumber: 575,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 570,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        size: 24,\n                                        color: \"teal\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 586,\n                                        columnNumber: 33\n                                    }, void 0),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_TableFix__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        data: filteredRecords,\n                                        columns: columns,\n                                        actions: actions,\n                                        defaultItemsPerPage: 15,\n                                        onSelectionChange: handleSelectionChange,\n                                        handleDeleteMultiple: handleDeleteMultiple,\n                                        clearSelection: clearSelection,\n                                        onSelectionCleared: ()=>setClearSelection(false)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 587,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 586,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 569,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 402,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_GradeModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    isOpen: isGradeModalOpen,\n                    onClose: ()=>{\n                        setIsGradeModalOpen(false);\n                        setGradeToEdit(null);\n                    },\n                    onSubmit: handleGradeSubmit,\n                    grade: gradeToEdit,\n                    students: students,\n                    subjects: subjects,\n                    examTypes: examTypes,\n                    loading: isSubmitting\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 602,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_DeleteConfirmationModal__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    isOpen: isDeleteModalOpen,\n                    onClose: ()=>{\n                        setIsDeleteModalOpen(false);\n                        setGradeToDelete(null);\n                    },\n                    onConfirm: handleDeleteConfirm,\n                    title: deleteType === \"single\" ? \"Delete Grade Record\" : \"Delete Selected Grade Records\",\n                    message: deleteType === \"single\" ? \"Are you sure you want to delete this grade record? This action cannot be undone.\" : \"Are you sure you want to delete \".concat(selectedGrades.length, \" selected grade records? This action cannot be undone.\"),\n                    itemName: deleteType === \"single\" && gradeToDelete ? \"\".concat(gradeToDelete.student_name, \" - \").concat(gradeToDelete.subject_name, \" (\").concat(gradeToDelete.exam_type, \")\") : undefined,\n                    itemCount: deleteType === \"multiple\" ? selectedGrades.length : undefined,\n                    type: deleteType\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 617,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Toast__WEBPACK_IMPORTED_MODULE_13__.ToastContainer, {\n                    toasts: toasts,\n                    onClose: removeToast\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 644,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n            lineNumber: 401,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n        lineNumber: 400,\n        columnNumber: 5\n    }, this);\n}\n_s(GradesPage, \"RdTpmZWYWpSbOXdTiw9G3BxuXW8=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        _components_ui_Toast__WEBPACK_IMPORTED_MODULE_13__.useToast\n    ];\n});\n_c = GradesPage;\nvar _c;\n$RefreshReg$(_c, \"GradesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboards)/school-admin/grades/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/services/SubjectServices.tsx":
/*!**********************************************!*\
  !*** ./src/app/services/SubjectServices.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSubject: () => (/* binding */ createSubject),\n/* harmony export */   deleteAllSubjects: () => (/* binding */ deleteAllSubjects),\n/* harmony export */   deleteMultipleSubjects: () => (/* binding */ deleteMultipleSubjects),\n/* harmony export */   deleteSubject: () => (/* binding */ deleteSubject),\n/* harmony export */   getAllSubjects: () => (/* binding */ getAllSubjects),\n/* harmony export */   getSubjectById: () => (/* binding */ getSubjectById),\n/* harmony export */   getSubjectsByClassId: () => (/* binding */ getSubjectsByClassId),\n/* harmony export */   getSubjectsBySchoolId: () => (/* binding */ getSubjectsBySchoolId),\n/* harmony export */   updateSubject: () => (/* binding */ updateSubject)\n/* harmony export */ });\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./AuthContext */ \"(app-pages-browser)/./src/app/services/AuthContext.tsx\");\n/* harmony import */ var _UserServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./UserServices */ \"(app-pages-browser)/./src/app/services/UserServices.tsx\");\n\n\n// Get all subjects\nasync function getAllSubjects() {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const res = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/subject/get-subjects\"), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        }\n    });\n    if (!res.ok) throw new Error(\"Failed to fetch subjects\");\n    const data = await res.json();\n    return data.map((subject)=>({\n            _id: subject._id,\n            subject_id: subject.subject_id,\n            subject_code: subject.subject_code,\n            compulsory: subject.compulsory,\n            school_id: subject.school_id,\n            class_id: subject.class_id,\n            name: subject.name,\n            description: subject.description,\n            coefficient: subject.coefficient,\n            department: subject.department || \"\",\n            createdAt: subject.createdAt,\n            updatedAt: subject.updatedAt\n        }));\n}\n// Get subject by ID\nasync function getSubjectById(id) {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const res = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/subject/get-subject/\").concat(id), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        }\n    });\n    if (!res.ok) throw new Error(\"Failed to fetch subject\");\n    const subject = await res.json();\n    return {\n        _id: subject._id,\n        subject_id: subject.subject_id,\n        subject_code: subject.subject_code,\n        compulsory: subject.compulsory,\n        school_id: subject.school_id,\n        class_id: subject.class_id,\n        name: subject.name,\n        description: subject.description,\n        coefficient: subject.coefficient,\n        department: subject.department || \"\",\n        createdAt: subject.createdAt,\n        updatedAt: subject.updatedAt\n    };\n}\n// Create a new subject\nasync function createSubject(data) {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const res = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/subject/create-subject\"), {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        },\n        body: JSON.stringify(data)\n    });\n    if (!res.ok) {\n        let msg = \"Failed to create subject\";\n        try {\n            const err = await res.json();\n            msg = err.message || msg;\n        } catch (e) {}\n        throw new Error(msg);\n    }\n    return await res.json();\n}\n// Update subject by ID\nasync function updateSubject(id, data) {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const res = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/subject/update-subject/\").concat(id), {\n        method: \"PUT\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        },\n        body: JSON.stringify(data)\n    });\n    if (!res.ok) {\n        let msg = \"Failed to update subject\";\n        try {\n            const err = await res.json();\n            msg = err.message || msg;\n        } catch (e) {}\n        throw new Error(msg);\n    }\n    return await res.json();\n}\n// Delete subject by ID\nasync function deleteSubject(id) {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const res = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/subject/delete-subject/\").concat(id), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        }\n    });\n    if (!res.ok) {\n        let msg = \"Failed to delete subject\";\n        try {\n            const err = await res.json();\n            msg = err.message || msg;\n        } catch (e) {}\n        throw new Error(msg);\n    }\n    return await res.json();\n}\n// Delete multiple subjects by IDs\nasync function deleteMultipleSubjects(ids) {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const res = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/subject/delete-subjects\"), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        },\n        body: JSON.stringify({\n            ids\n        })\n    });\n    if (!res.ok) {\n        let msg = \"Failed to delete subjects\";\n        try {\n            const err = await res.json();\n            msg = err.message || msg;\n        } catch (e) {}\n        throw new Error(msg);\n    }\n    return await res.json();\n}\n// Delete all subjects\nasync function deleteAllSubjects() {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const res = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/subject/delete-all-subjects\"), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        }\n    });\n    if (!res.ok) {\n        throw new Error(\"Failed to delete all subjects\");\n    }\n    return await res.json();\n}\n// Get subjects by school ID\nasync function getSubjectsBySchoolId(schoolId) {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const res = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/subject/get-subject-by-school/\").concat(schoolId), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        }\n    });\n    if (!res.ok) throw new Error(\"Failed to fetch subjects by school ID\");\n    const data = await res.json();\n    return data.map((subject)=>({\n            _id: subject._id,\n            subject_id: subject.subject_id,\n            subject_code: subject.subject_code,\n            compulsory: subject.compulsory,\n            school_id: subject.school_id,\n            class_id: subject.class_id,\n            name: subject.name,\n            description: subject.description,\n            coefficient: subject.coefficient,\n            department: subject.department || \"\",\n            createdAt: subject.createdAt,\n            updatedAt: subject.updatedAt\n        }));\n}\n// Get subjects by class ID\nasync function getSubjectsByClassId(classId) {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const res = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/subject/get-subject-by-class/\").concat(classId), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        }\n    });\n    if (!res.ok) throw new Error(\"Failed to fetch subjects by class ID\");\n    const data = await res.json();\n    return data.map((subject)=>({\n            _id: subject._id,\n            subject_id: subject.subject_id,\n            subject_code: subject.subject_code,\n            compulsory: subject.compulsory,\n            school_id: subject.school_id,\n            class_id: subject.class_id,\n            name: subject.name,\n            description: subject.description,\n            coefficient: subject.coefficient,\n            department: subject.department || \"\",\n            createdAt: subject.createdAt,\n            updatedAt: subject.updatedAt\n        }));\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/services/SubjectServices.tsx\n"));

/***/ })

});