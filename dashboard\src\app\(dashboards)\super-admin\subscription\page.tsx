"use client";

import SuperLayout from '@/components/Dashboard/Layouts/SuperLayout';
import CircularLoader from '@/components/widgets/CircularLoader';
import React, { Suspense, useEffect, useState } from 'react';
import { Wallet } from 'lucide-react';
import DataTableFix from '@/components/utils/TableFix';
import NotificationCard, { NotificationType } from '@/components/NotificationCard';
import { createSuccessNotification, createErrorNotification, NotificationState } from '@/app/types/notification';
import useAuth from '@/app/hooks/useAuth';
import { getUserBy_id, verifyPassword } from '@/app/services/UserServices';
import CreateSubscriptionModal from '../subscription/components/CreateSubscriptionModal';
import DeleteUserModal from '../users/components/DeleteUserModal';
import UpdateSubscriptionModal from './components/UpdateSubscriptionModal';
import { getSubscriptions, deleteMultipleSubscriptions, deleteAllSubscriptions } from '@/app/services/SubscriptionsServices';
import { SubscriptionSchema } from '@/app/models/SubscriptionModel';
import { getStudentById } from '@/app/services/StudentServices';
import BulkDeleteModal from '@/components/modals/BulkDeleteModal';
import { PayementSchema } from '@/app/models/PayementModel';
import { initiateTransaction } from '@/app/services/transactionServices';
import ProtectedRoute from '@/components/utils/ProtectedRoute';

// Interface pour une souscription
interface Parent {
  id: string;
  name: string;
  email?: string;
}

interface Child {
  id: string;
  name: string;
  parentId: string;
  schoolInfo?: string;
}

interface SubscriptionNewFormSchema extends Record<string, unknown> {
  parent: Parent;
  children: Child[];
  status: boolean;
  startDate: string;
  endDate: string;
  // For backward compatibility with existing code
  id?: string;
  parentName?: string;
  childNames?: string[];
  parentId?: string;
  childIds?: string[];
}

interface SubscriptionCreateSchema {
  parentId: string;
  childIds: string[];
}

// We now fetch parents and children data from the API

export default function Page() {
  const { logout } = useAuth();

  const BASE_URL = "/super-admin";

  const navigation = {
    icon: Wallet, // Tu peux remplacer l'icône par une icône plus appropriée pour les souscriptions
    baseHref: `${BASE_URL}/subscription`,
    title: "Subscriptions",
  };

  function SubscriptionContent() {
    const [subscriptions, setSubscriptions] = useState<SubscriptionNewFormSchema[]>([]);
    const [selectedSubscriptions, setSelectedSubscriptions] = useState<SubscriptionNewFormSchema[]>([]);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [subscriptionToDelete, setSubscriptionToDelete] = useState<SubscriptionNewFormSchema | null>(null);
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false);
    const [subscriptionToUpdate, setSubscriptionToUpdate] = useState<SubscriptionNewFormSchema | null>(null);
    const [loadingData, setLoadingData] = useState(false);
    const [notification, setNotification] = useState<NotificationState>({
      title: '',
      message: '',
      type: 'info',
      isVisible: false
    });
    const { user } = useAuth();

    // Bulk delete modal states
    const [isBulkDeleteModalOpen, setIsBulkDeleteModalOpen] = useState(false);
    const [bulkDeleteType, setBulkDeleteType] = useState<"selected" | "all">("selected");
    const [selectedSubscriptionIds, setSelectedSubscriptionIds] = useState<string[]>([]);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitStatus, setSubmitStatus] = useState<"success" | "failure" | null>(null);

    // Key to force DataTable re-render and clear selection
    const [tableKey, setTableKey] = useState(0);

    // Diagnostic function to check data integrity
    async function diagnoseDatabaseIssues() {
      try {
        console.log("🔍 Starting database diagnosis...");

        const fetchedSubscriptions = await getSubscriptions();
        console.log("📊 Total subscriptions found:", fetchedSubscriptions.length);

        for (const subscription of fetchedSubscriptions) {
          console.log("🔍 Subscription:", subscription._id);
          console.log("   Guardian ID:", subscription.guardian_id);
          console.log("   Student IDs:", subscription.student_id);
          console.log("   Status:", subscription.status);
          console.log("   Amount:", subscription.amount);
        }

        // Check if we can fetch students
        const { getStudents } = await import('@/app/services/StudentServices');
        const allStudents = await getStudents();
        console.log("👥 Total students in database:", allStudents.length);
        console.log("📋 Sample student IDs:", allStudents.slice(0, 5).map(s => s.student_id));

      } catch (error) {
        console.error("❌ Diagnosis failed:", error);
      }
    }

    async function getAllSubscriptions() {
      try {
        setLoadingData(true);
        const fetchedSubscriptions = await getSubscriptions();

        console.log("🚀 Starting subscription processing...");
        console.log("📊 Fetched subscriptions:", fetchedSubscriptions.length);

        // Group subscriptions by guardian_id (parent)
        const subscriptionsByParent: Record<string, {
          parent: { id: string, name: string, email: string },
          children: { id: string, name: string, parentId: string }[],
          subscriptions: SubscriptionSchema[]
        }> = {};

        // Process each subscription
        await Promise.all(
          fetchedSubscriptions.map(async (subscription: SubscriptionSchema) => {
            // Get parent information
            const parentData = await getUserBy_id(subscription.guardian_id);

            if (parentData && parentData.role === "parent") {
              // Initialize parent entry if it doesn't exist
              if (!subscriptionsByParent[parentData._id]) {
                subscriptionsByParent[parentData._id] = {
                  parent: {
                    id: parentData._id,
                    name: parentData.name,
                    email: parentData.email
                  },
                  children: [],
                  subscriptions: []
                };
              }

              // Add subscription to parent's subscriptions
              subscriptionsByParent[parentData._id].subscriptions.push(subscription);

              // Get student information for each student_id in the subscription
              if (subscription.student_id && subscription.student_id.length > 0) {
                console.log("🔍 Processing subscription:", subscription._id);
                console.log("📋 Student IDs in subscription:", subscription.student_id);

                const studentPromises = subscription.student_id.map(async (studentId) => {
                  try {
                    console.log("🔎 Fetching student with ID:", studentId);

                    // Try to get student by ID with multiple approaches
                    let studentData = null;

                    // First, try direct fetch by student_id
                    try {
                      studentData = await getStudentById(studentId);
                      console.log("✅ Direct fetch successful");
                    } catch (fetchError) {
                      console.log("⚠️ Direct fetch failed, trying alternative approaches...");

                      // Get all students and try to match
                      const { getStudents } = await import('@/app/services/StudentServices');
                      const allStudents = await getStudents();
                      console.log("🔍 Searching in", allStudents.length, "students");

                      // Try multiple matching strategies
                      studentData = allStudents.find(student => {
                        const matches = [
                          student.student_id === studentId,           // Exact student_id match
                          student._id === studentId,                  // MongoDB _id match
                          student.student_id?.includes(studentId),    // Partial match
                          studentId.includes(student.student_id || '') // Reverse partial match
                        ];

                        if (matches.some(match => match)) {
                          console.log("🎯 Found match:", student.name, "using strategy:", matches.findIndex(m => m));
                          return true;
                        }
                        return false;
                      });

                      if (!studentData) {
                        console.log("❌ No match found for ID:", studentId);
                        console.log("📋 Available student IDs:", allStudents.map(s => s.student_id));
                      }
                    }

                    if (studentData) {
                      console.log("✅ Found student:", studentData.name, "with ID:", studentData.student_id);

                      // Check if this child is already in the list
                      const existingChildIndex = subscriptionsByParent[parentData._id].children
                        .findIndex(child => child.id === studentData.student_id);

                      if (existingChildIndex === -1) {
                        // Add child if not already in the list
                        subscriptionsByParent[parentData._id].children.push({
                          id: studentData.student_id,
                          name: studentData.name,
                          parentId: parentData._id
                        });
                        console.log("➕ Added child to parent:", studentData.name);
                      } else {
                        console.log("⚠️ Child already exists in list:", studentData.name);
                      }
                    } else {
                      console.log("❌ No student data found for ID:", studentId);
                      // Add placeholder for missing students
                      subscriptionsByParent[parentData._id].children.push({
                        id: studentId,
                        name: `Student ${studentId} (Not Found)`,
                        parentId: parentData._id
                      });
                    }
                    return studentData;
                  } catch (error) {
                    console.error(`❌ Error processing student with ID ${studentId}:`, error);
                    // Add placeholder for error cases
                    subscriptionsByParent[parentData._id].children.push({
                      id: studentId,
                      name: `Student ${studentId} (Error)`,
                      parentId: parentData._id
                    });
                    return null;
                  }
                });

                await Promise.all(studentPromises);
                console.log("📊 Final children for parent", parentData.name, ":", subscriptionsByParent[parentData._id].children);
              } else {
                console.log("⚠️ No student_id found in subscription:", subscription._id);
              }
            }
          })
        );

        // Convert the grouped data to the format expected by the component
        const formattedSubscriptions = Object.values(subscriptionsByParent).map(group => {
          return {
            parent: group.parent,
            children: group.children,
            status: group.subscriptions.some(sub => sub.status),
            startDate: group.subscriptions[0]?.createdAt ? new Date(group.subscriptions[0].createdAt).toLocaleDateString() : "",
            endDate: group.subscriptions[0]?.expiryDate ? new Date(group.subscriptions[0].expiryDate).toLocaleDateString() : ""
          } as SubscriptionNewFormSchema;
        });

        setSubscriptions(formattedSubscriptions);
      } catch (error) {
        console.error("Error fetching subscriptions:", error);
      } finally {
        setLoadingData(false);
      }
    }
    useEffect(() => {
      getAllSubscriptions();
    }, []);



    const columns = [
      { header: "Parent Name", accessor: (row: SubscriptionNewFormSchema) => row.parent?.name || row.parentName || "Unknown" },
      {
        header: "Child Name(s)", accessor: (row: SubscriptionNewFormSchema) => {
          if (row.children && row.children.length > 0) {
            return row.children.map(child => child.name).join(", ");
          } else if (row.childNames && row.childNames.length > 0) {
            return row.childNames.join(", ");
          }
          return "No children";
        }
      },
      {
        header: "Status",
        accessor: (row: SubscriptionNewFormSchema) => {
          const statusText = typeof row.status === 'boolean' ? (row.status ? "Active" : "Inactive") : row.status;
          return (
            <span
              className={`px-2 py-1 rounded-full text-sm ${statusText === "Active" || row.status === true
                  ? "bg-green-100 text-green-800"
                  : new Date(row.endDate) > new Date()
                    ? "bg-yellow-100 text-yellow-800"
                    : "bg-red-100 text-red-800"
                }`}
            >
              {statusText}
            </span>
          );
        },
      },
      { header: "Start Date", accessor: (row: SubscriptionNewFormSchema) => row.startDate || "N/A" },
      { header: "End Date", accessor: (row: SubscriptionNewFormSchema) => row.endDate || "N/A" },
    ];



    const actions = [
      {
        label: "edit",
        onClick: (subscription: SubscriptionNewFormSchema) => {
          setSubscriptionToUpdate(subscription);
          setIsUpdateModalOpen(true);
        },
        // Désactiver le bouton d'édition si la souscription est active
        disabled: (subscription: SubscriptionNewFormSchema) => subscription.status === true,
        disabledTooltip: "Cannot edit an active subscription",
      },
      {
        label: "Delete",
        onClick: (subscription: SubscriptionNewFormSchema) => {
          setSubscriptionToDelete(subscription);
          setIsDeleteModalOpen(true);
        },
      },
    ];

    const handleUpdateSubscription = async (updatedSubscription: any) => {
      setLoadingData(true);
      try {
        // Simuler la mise à jour de la souscription (remplace par un appel API plus tard)
        setSubscriptions((prev) =>
          prev.map((sub) =>
            sub.id === updatedSubscription.id ? {
              ...sub,
              childIds: updatedSubscription.childIds,
              children: sub.children.filter(child =>
                updatedSubscription.childIds.includes(child.id)
              )
            } : sub
          )
        );
        setNotification(createSuccessNotification("Subscription renewed successfully!"));
      } catch (error) {
        console.error("Error renewing subscription:", error);
        const errorMessage = error instanceof Error ? error.message : "An unknown error occurred while renewing the subscription.";
        setNotification(createErrorNotification(errorMessage));
      } finally {
        setLoadingData(false);
      }
    };

    const handleSaveSubscription = async (subscriptionData: SubscriptionCreateSchema) => {
      setLoadingData(true);
      try {
        // // Get parent and children data from the API
        const parentData = await getUserBy_id(subscriptionData.parentId);

        const childrenPromises = subscriptionData.childIds.map(childId => getStudentById(childId));
        const childrenData = await Promise.all(childrenPromises);


        const totalAmount = childrenData.length * 19900; // Price per student

        // Concaténer les IDs des enfants séparés par des underscores pour former l'externalId
        const childrenIds = childrenData.map(child => child.student_id).join('_');

        const newTransaction: PayementSchema = {
          userId: parentData._id,
          amount: totalAmount,
          email: parentData.email,
          name: parentData.name,
          externalId: childrenIds,
          redirectUrl: window.location.href
        };

        // Initiate the transaction
        const transactionResponse:any = await initiateTransaction(newTransaction);

        if (transactionResponse && transactionResponse.link) {
          window.location.href = transactionResponse.link;
        }


      } catch (error) {
        console.error("Error creating subscription:", error);
        const errorMessage = error instanceof Error ? error.message : "An unknown error occurred while creating the subscription.";
        setNotification(createErrorNotification(errorMessage));
      } finally {
        setLoadingData(false);
      }
    };

    const handleDelete = async (password: string) => {
      const passwordVerified = user ? await verifyPassword(password, user.email) : false;
      if (!passwordVerified) {
        setNotification(createErrorNotification("Invalid Password!"));
        return;
      }

      if (subscriptionToDelete) {
        try {
          // Simuler la suppression (remplace par un appel API plus tard)
          setSubscriptions(subscriptions.filter((s) => s.id !== subscriptionToDelete.id));
          setNotification(createSuccessNotification("Subscription deleted successfully!"));
          setSubscriptionToDelete(null);
          setIsDeleteModalOpen(false);
        } catch (error) {
          console.error("Error deleting subscription:", error);
          const errorMessage = error instanceof Error ? error.message : "Error deleting subscription.";
          setNotification(createErrorNotification(errorMessage));
        }
      }
    };

    // Handle opening bulk delete modal for selected items
    const handleDeleteMultiple = async (selectedIds: string[]) => {
      if (selectedIds.length === 0) {
        setNotification(createErrorNotification("Please select at least one subscription to delete."));
        return;
      }
      setSelectedSubscriptionIds(selectedIds);
      setBulkDeleteType("selected");
      setIsBulkDeleteModalOpen(true);
    };

    // Handle opening bulk delete modal for all items
    const handleDeleteAll = async () => {
      if (subscriptions.length === 0) {
        setNotification(createErrorNotification("No subscriptions to delete."));
        return;
      }
      setBulkDeleteType("all");
      setIsBulkDeleteModalOpen(true);
    };

    // Handle the actual bulk deletion with password confirmation
    const handleBulkDeleteConfirm = async (password: string) => {
      setIsSubmitting(true);
      setSubmitStatus(null);

      // Verify password
      const passwordVerified = user ? await verifyPassword(password, user.email) : false;
      if (!passwordVerified) {
        setNotification(createErrorNotification("Invalid Password!"));
        setIsSubmitting(false);
        setSubmitStatus("failure");
        return;
      }

      try {
        if (bulkDeleteType === "all") {
          await deleteAllSubscriptions();
          setNotification(createSuccessNotification("All subscriptions deleted successfully!"));
          setSubscriptions([]);
        } else {
          await deleteMultipleSubscriptions(selectedSubscriptionIds);
          setNotification(createSuccessNotification(`${selectedSubscriptionIds.length} subscription(s) deleted successfully!`));
          setSubscriptions(prev => prev.filter(sub => !selectedSubscriptionIds.includes(sub.id || '')));
        }

        setSubmitStatus("success");
        setSelectedSubscriptions([]); // Clear selection
        setTableKey(prev => prev + 1); // Force table re-render to clear selection

        // Close modal after success
        setTimeout(() => {
          setIsBulkDeleteModalOpen(false);
          setSubmitStatus(null);
        }, 2000);

      } catch (error) {
        console.error("Error in bulk deletion:", error);
        const errorMessage = error instanceof Error ? error.message : "Failed to delete subscriptions";
        setNotification(createErrorNotification(errorMessage));
        setSubmitStatus("failure");
      } finally {
        setIsSubmitting(false);
      }
    };



    return (
      <div className="md:p-6">
        <NotificationCard
          title={notification.title}
          message={notification.message}
          type={notification.type}
          isVisible={notification.isVisible}
          onClose={() => setNotification(prev => ({ ...prev, isVisible: false }))}
          isFixed={true}
          autoClose={true}
          duration={5000}
        />

        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4 gap-4">
          <div className="flex gap-2">
            <button
              onClick={() => setIsModalOpen(true)}
              className="px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600"
            >
              Create New Subscription
            </button>

            {/* Temporary diagnostic button */}
            <button
              onClick={diagnoseDatabaseIssues}
              className="px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600"
            >
              🔍 Debug Data
            </button>
          </div>

          {isModalOpen && (
            <CreateSubscriptionModal
              onClose={() => setIsModalOpen(false)}
              onSave={handleSaveSubscription}
              pricePerStudent={19900}
            />
          )}
          {isUpdateModalOpen && subscriptionToUpdate && (
            <UpdateSubscriptionModal
              onClose={() => {
                setIsUpdateModalOpen(false);
                setSubscriptionToUpdate(null);
              }}
              onSave={handleUpdateSubscription}
              subscription={{
                id: subscriptionToUpdate.id || '',
                parentId: subscriptionToUpdate.parentId || subscriptionToUpdate.parent?.id || '',
                childIds: subscriptionToUpdate.childIds || subscriptionToUpdate.children?.map(c => c.id) || [],
                expirationDate: subscriptionToUpdate.endDate || ''
              } as any}
              parent={subscriptionToUpdate.parent as any}
              children={subscriptionToUpdate.children as any}
              pricePerStudent={19900}
            />
          )}
          {isDeleteModalOpen && subscriptionToDelete && (
            <DeleteUserModal
              userName={subscriptionToDelete.parentName || subscriptionToDelete.parent?.name || 'Unknown'}
              onClose={() => {
                setIsDeleteModalOpen(false);
                setSubscriptionToDelete(null);
              }}
              onDelete={handleDelete}
            />
          )}
        </div>

        <DataTableFix<SubscriptionNewFormSchema>
          key={tableKey} // Force re-render to clear selection
          columns={columns}
          data={subscriptions}
          actions={actions as any}
          defaultItemsPerPage={5}
          loading={loadingData}
          onLoadingChange={setLoadingData}
          onSelectionChange={setSelectedSubscriptions}
          idAccessor="id"
          enableBulkActions={true}
          handleDeleteMultiple={handleDeleteMultiple}
          handleDeleteAll={handleDeleteAll}
          showCheckbox={true}
        />

        {/* Bulk Delete Modal */}
        {isBulkDeleteModalOpen && (
          <BulkDeleteModal
            isOpen={isBulkDeleteModalOpen}
            onClose={() => {
              setIsBulkDeleteModalOpen(false);
              setSubmitStatus(null);
            }}
            onConfirm={handleBulkDeleteConfirm}
            title={bulkDeleteType === "all" ? "Delete All Subscriptions" : "Delete Selected Subscriptions"}
            message={
              bulkDeleteType === "all"
                ? `Are you sure you want to delete ALL ${subscriptions.length} subscriptions? This action cannot be undone.`
                : `Are you sure you want to delete ${selectedSubscriptionIds.length} selected subscription(s)? This action cannot be undone.`
            }
            itemCount={bulkDeleteType === "all" ? subscriptions.length : selectedSubscriptionIds.length}
            itemType="subscriptions"
            isDeleteAll={bulkDeleteType === "all"}
            isSubmitting={isSubmitting}
            submitStatus={submitStatus}
            requirePassword={true}
          />
        )}
      </div>
    );
  }

  return (
    <Suspense
      fallback={
        <div className="flex justify-center items-center h-screen absolute top-0 left-0 z-50">
          <CircularLoader size={32} color="teal" />
        </div>
      }
    >
      <ProtectedRoute>
        <SuperLayout
          navigation={navigation}
          showGoPro={true}
          onLogout={() => logout()}
        >
          <SubscriptionContent />
        </SuperLayout>
      </ProtectedRoute>
    </Suspense>
  );
}