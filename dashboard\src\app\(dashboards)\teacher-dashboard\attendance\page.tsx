"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { ClipboardList, Users, Calendar, Check, X, Clock, Save } from "lucide-react";
import TeacherLayout from "@/components/Dashboard/Layouts/TeacherLayout";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import CircularLoader from "@/components/widgets/CircularLoader";
import { motion } from "framer-motion";

interface SelectedSchool {
  school_id: string;
  school_name: string;
  access_granted_at: string;
}

interface Student {
  student_id: string;
  name: string;
  roll_number: string;
  avatar?: string;
}

interface AttendanceRecord {
  student_id: string;
  status: 'present' | 'absent' | 'late';
  remarks?: string;
}

interface ClassInfo {
  class_id: string;
  class_name: string;
  subject: string;
  students: Student[];
}

const navigation = {
  icon: ClipboardList,
  baseHref: "/teacher-dashboard/attendance",
  title: "Attendance"
};

export default function TeacherAttendancePage() {
  const { user, logout } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const classId = searchParams.get("class");
  
  const [selectedSchool, setSelectedSchool] = useState<SelectedSchool | null>(null);
  const [classInfo, setClassInfo] = useState<ClassInfo | null>(null);
  const [attendance, setAttendance] = useState<AttendanceRecord[]>([]);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    // Check if user is a teacher
    if (user && user.role !== "teacher") {
      router.push("/dashboard");
      return;
    }

    // Get selected school from localStorage
    const storedSchool = localStorage.getItem("teacher_selected_school");
    if (storedSchool) {
      try {
        const school = JSON.parse(storedSchool);
        setSelectedSchool(school);
        
        if (classId) {
          fetchClassInfo(classId);
        } else {
          // If no class selected, redirect to classes page
          router.push("/teacher-dashboard/classes");
        }
      } catch (error) {
        console.error("Error parsing stored school:", error);
        router.push("/teacher-dashboard");
      }
    } else {
      router.push("/teacher-dashboard");
    }
    
    setLoading(false);
  }, [user, router, classId]);

  const fetchClassInfo = async (classId: string) => {
    try {
      setLoading(true);

      // Get teacher's students for this class
      const { getTeacherStudents } = await import("@/app/services/TeacherAssignmentServices");
      const { getTeacherPermissions } = await import("@/app/services/TeacherPermissionServices");

      if (!selectedSchool) return;

      const [studentsData, teacherData] = await Promise.all([
        getTeacherStudents(selectedSchool.school_id),
        getTeacherPermissions(selectedSchool.school_id)
      ]);

      // Filter students for this specific class
      const classStudents = studentsData.filter(student => student.class_id === classId);

      // Find class info from teacher's assigned classes
      const assignedClass = teacherData.assigned_classes.find(cls => cls._id === classId);

      if (!assignedClass) {
        throw new Error("Class not found in teacher's assignments");
      }

      const classInfo: ClassInfo = {
        class_id: classId,
        class_name: assignedClass.name,
        subject: teacherData.assigned_subjects[0]?.name || "General", // Use first assigned subject
        students: classStudents.map(student => ({
          student_id: student._id,
          name: `${student.first_name} ${student.last_name}`,
          roll_number: (student as any).roll_number || "N/A"
        }))
      };

      setClassInfo(classInfo);

      // Initialize attendance records
      const initialAttendance = classInfo.students.map(student => ({
        student_id: student.student_id,
        status: 'present' as const,
        remarks: ''
      }));

      setAttendance(initialAttendance);

    } catch (error) {
      console.error("Error fetching class info:", error);
      // Set empty class info on error
      setClassInfo({
        class_id: classId,
        class_name: "Unknown Class",
        subject: "Unknown Subject",
        students: []
      });
      setAttendance([]);
    } finally {
      setLoading(false);
    }
  };

  const handleSchoolChange = () => {
    localStorage.removeItem("teacher_selected_school");
    router.push("/teacher-dashboard");
  };

  const updateAttendance = (studentId: string, status: 'present' | 'absent' | 'late') => {
    setAttendance(prev => 
      prev.map(record => 
        record.student_id === studentId 
          ? { ...record, status }
          : record
      )
    );
  };

  const updateRemarks = (studentId: string, remarks: string) => {
    setAttendance(prev => 
      prev.map(record => 
        record.student_id === studentId 
          ? { ...record, remarks }
          : record
      )
    );
  };

  const saveAttendance = async () => {
    setSaving(true);
    try {
      // TODO: Implement actual attendance API service
      // For now, we'll simulate the API call with proper structure
      const attendanceData = {
        school_id: selectedSchool?.school_id,
        class_id: classId,
        date: selectedDate,
        attendance_records: attendance.map(record => ({
          student_id: record.student_id,
          status: record.status,
          remarks: record.remarks,
          recorded_by: user?.uid,
          recorded_at: new Date().toISOString()
        }))
      };

      console.log('Saving attendance:', attendanceData);

      // Simulate API call - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      alert('Attendance saved successfully!');
    } catch (error) {
      console.error('Error saving attendance:', error);
      alert('Failed to save attendance. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'present':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'absent':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
      case 'late':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  const getAttendanceStats = () => {
    const present = attendance.filter(a => a.status === 'present').length;
    const absent = attendance.filter(a => a.status === 'absent').length;
    const late = attendance.filter(a => a.status === 'late').length;
    const total = attendance.length;
    
    return { present, absent, late, total };
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <CircularLoader size={32} color="teal" />
      </div>
    );
  }

  if (!classInfo) {
    return (
      <ProtectedRoute allowedRoles={["teacher"]}>
        <TeacherLayout
          navigation={navigation}
          selectedSchool={selectedSchool ? {
            _id: selectedSchool.school_id,
            name: selectedSchool.school_name
          } : null}
          onSchoolChange={handleSchoolChange}
          onLogout={logout}
        >
          <div className="text-center py-12">
            <ClipboardList className="h-16 w-16 text-foreground/30 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">Class Not Found</h3>
            <p className="text-foreground/60 mb-6">
              The requested class could not be found.
            </p>
            <button
              onClick={() => router.push("/teacher-dashboard/classes")}
              className="px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 transition-colors"
            >
              Back to Classes
            </button>
          </div>
        </TeacherLayout>
      </ProtectedRoute>
    );
  }

  const stats = getAttendanceStats();

  return (
    <ProtectedRoute allowedRoles={["teacher"]}>
      <TeacherLayout
        navigation={navigation}
        selectedSchool={selectedSchool ? {
          _id: selectedSchool.school_id,
          name: selectedSchool.school_name
        } : null}
        onSchoolChange={handleSchoolChange}
        onLogout={logout}
      >
        <div className="space-y-6">
          {/* Header */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-teal rounded-full flex items-center justify-center">
                  <ClipboardList className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-foreground">Take Attendance</h1>
                  <p className="text-foreground/60">{classInfo.class_name} • {classInfo.subject}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <div>
                  <label className="block text-sm font-medium text-foreground mb-1">Date</label>
                  <input
                    type="date"
                    value={selectedDate}
                    onChange={(e) => setSelectedDate(e.target.value)}
                    className="px-3 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent bg-widget text-foreground"
                  />
                </div>
                
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={saveAttendance}
                  disabled={saving}
                  className="flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {saving ? <CircularLoader size={16} color="white" /> : <Save size={16} />}
                  <span>{saving ? 'Saving...' : 'Save Attendance'}</span>
                </motion.button>
              </div>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-4 gap-4">
              <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <p className="text-2xl font-bold text-foreground">{stats.total}</p>
                <p className="text-sm text-foreground/60">Total Students</p>
              </div>
              <div className="text-center p-3 bg-green-50 dark:bg-green-900/30 rounded-lg">
                <p className="text-2xl font-bold text-green-600">{stats.present}</p>
                <p className="text-sm text-green-600">Present</p>
              </div>
              <div className="text-center p-3 bg-red-50 dark:bg-red-900/30 rounded-lg">
                <p className="text-2xl font-bold text-red-600">{stats.absent}</p>
                <p className="text-sm text-red-600">Absent</p>
              </div>
              <div className="text-center p-3 bg-yellow-50 dark:bg-yellow-900/30 rounded-lg">
                <p className="text-2xl font-bold text-yellow-600">{stats.late}</p>
                <p className="text-sm text-yellow-600">Late</p>
              </div>
            </div>
          </div>

          {/* Attendance List */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <h2 className="text-lg font-semibold text-foreground mb-4">Student Attendance</h2>
            
            <div className="space-y-3">
              {classInfo.students.map((student, index) => {
                const studentAttendance = attendance.find(a => a.student_id === student.student_id);
                
                return (
                  <motion.div
                    key={student.student_id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="flex items-center justify-between p-4 border border-stroke rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-teal rounded-full flex items-center justify-center text-white font-medium">
                        {student.roll_number}
                      </div>
                      <div>
                        <p className="font-medium text-foreground">{student.name}</p>
                        <p className="text-sm text-foreground/60">Roll No: {student.roll_number}</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4">
                      {/* Status Buttons */}
                      <div className="flex space-x-2">
                        <button
                          onClick={() => updateAttendance(student.student_id, 'present')}
                          className={`flex items-center space-x-1 px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                            studentAttendance?.status === 'present'
                              ? 'bg-green-500 text-white'
                              : 'bg-gray-100 text-gray-600 hover:bg-green-100 hover:text-green-600'
                          }`}
                        >
                          <Check size={14} />
                          <span>Present</span>
                        </button>
                        
                        <button
                          onClick={() => updateAttendance(student.student_id, 'late')}
                          className={`flex items-center space-x-1 px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                            studentAttendance?.status === 'late'
                              ? 'bg-yellow-500 text-white'
                              : 'bg-gray-100 text-gray-600 hover:bg-yellow-100 hover:text-yellow-600'
                          }`}
                        >
                          <Clock size={14} />
                          <span>Late</span>
                        </button>
                        
                        <button
                          onClick={() => updateAttendance(student.student_id, 'absent')}
                          className={`flex items-center space-x-1 px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                            studentAttendance?.status === 'absent'
                              ? 'bg-red-500 text-white'
                              : 'bg-gray-100 text-gray-600 hover:bg-red-100 hover:text-red-600'
                          }`}
                        >
                          <X size={14} />
                          <span>Absent</span>
                        </button>
                      </div>

                      {/* Remarks Input */}
                      <input
                        type="text"
                        placeholder="Remarks (optional)"
                        value={studentAttendance?.remarks || ''}
                        onChange={(e) => updateRemarks(student.student_id, e.target.value)}
                        className="w-32 px-2 py-1 text-sm border border-stroke rounded-md focus:outline-none focus:ring-1 focus:ring-teal bg-widget text-foreground"
                      />
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="flex justify-between items-center">
            <button
              onClick={() => router.push("/teacher-dashboard/classes")}
              className="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-foreground rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            >
              Back to Classes
            </button>
            
            <div className="flex space-x-3">
              <button
                onClick={() => {
                  // Mark all as present
                  setAttendance(prev => prev.map(record => ({ ...record, status: 'present' as const })));
                }}
                className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
              >
                Mark All Present
              </button>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={saveAttendance}
                disabled={saving}
                className="flex items-center space-x-2 px-6 py-2 bg-teal text-white rounded-md hover:bg-teal-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {saving ? <CircularLoader size={16} color="white" /> : <Save size={16} />}
                <span>{saving ? 'Saving...' : 'Save Attendance'}</span>
              </motion.button>
            </div>
          </div>
        </div>
      </TeacherLayout>
    </ProtectedRoute>
  );
}
