"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/period/page",{

/***/ "(app-pages-browser)/./src/app/(dashboards)/school-admin/period/page.tsx":
/*!***********************************************************!*\
  !*** ./src/app/(dashboards)/school-admin/period/page.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PeriodsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ChartNoAxesGantt_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChartNoAxesGantt,Clock,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-no-axes-gantt.js\");\n/* harmony import */ var _barrel_optimize_names_ChartNoAxesGantt_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChartNoAxesGantt,Clock,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ChartNoAxesGantt_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChartNoAxesGantt,Clock,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Dashboard/Layouts/SchoolLayout */ \"(app-pages-browser)/./src/components/Dashboard/Layouts/SchoolLayout.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_utils_TableFix__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/utils/TableFix */ \"(app-pages-browser)/./src/components/utils/TableFix.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/widgets/CircularLoader */ \"(app-pages-browser)/./src/components/widgets/CircularLoader.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var _components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/utils/ProtectedRoute */ \"(app-pages-browser)/./src/components/utils/ProtectedRoute.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_PeriodServices__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/services/PeriodServices */ \"(app-pages-browser)/./src/app/services/PeriodServices.tsx\");\n/* harmony import */ var _components_modals_PeriodModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/modals/PeriodModal */ \"(app-pages-browser)/./src/components/modals/PeriodModal.tsx\");\n/* harmony import */ var _components_modals_DeleteConfirmationModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/modals/DeleteConfirmationModal */ \"(app-pages-browser)/./src/components/modals/DeleteConfirmationModal.tsx\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst navigation = {\n    icon: _barrel_optimize_names_ChartNoAxesGantt_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    baseHref: \"/school-admin/period\",\n    title: \"Periods\"\n};\nfunction PeriodsPage() {\n    var _user_school_ids;\n    _s();\n    const { logout, user } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { toasts, removeToast, showSuccess, showError } = (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_11__.useToast)();\n    // State management\n    const [periods, setPeriods] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loadingData, setLoadingData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Modal states\n    const [isPeriodModalOpen, setIsPeriodModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isDeleteModalOpen, setIsDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [periodToEdit, setPeriodToEdit] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [periodToDelete, setPeriodToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [deleteType, setDeleteType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"single\");\n    const [selectedPeriods, setSelectedPeriods] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Loading states\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [clearSelection, setClearSelection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Get school ID from user\n    const schoolId = (user === null || user === void 0 ? void 0 : (_user_school_ids = user.school_ids) === null || _user_school_ids === void 0 ? void 0 : _user_school_ids[0]) || (user === null || user === void 0 ? void 0 : user.school_id) || \"\";\n    // Fetch periods from API\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"PeriodsPage.useEffect\": ()=>{\n            const fetchPeriods = {\n                \"PeriodsPage.useEffect.fetchPeriods\": async ()=>{\n                    if (!schoolId) {\n                        setError(\"No school ID found\");\n                        setLoadingData(false);\n                        return;\n                    }\n                    try {\n                        setLoadingData(true);\n                        setError(null);\n                        const response = await (0,_app_services_PeriodServices__WEBPACK_IMPORTED_MODULE_8__.getPeriods)(schoolId);\n                        setPeriods(response.periods);\n                    } catch (error) {\n                        console.error(\"Error fetching periods:\", error);\n                        setError(\"Failed to load periods\");\n                    } finally{\n                        setLoadingData(false);\n                    }\n                }\n            }[\"PeriodsPage.useEffect.fetchPeriods\"];\n            fetchPeriods();\n        }\n    }[\"PeriodsPage.useEffect\"], [\n        schoolId\n    ]);\n    // Format time for display\n    const formatTime = (timeString)=>{\n        const [hours, minutes] = timeString.split(':');\n        const hour = parseInt(hours);\n        const ampm = hour >= 12 ? 'PM' : 'AM';\n        const displayHour = hour % 12 || 12;\n        return \"\".concat(displayHour, \":\").concat(minutes, \" \").concat(ampm);\n    };\n    // Calculate duration\n    const calculateDuration = (startTime, endTime)=>{\n        const start = new Date(\"2000-01-01T\".concat(startTime));\n        const end = new Date(\"2000-01-01T\".concat(endTime));\n        const diffMs = end.getTime() - start.getTime();\n        const diffMins = Math.floor(diffMs / 60000);\n        if (diffMins >= 60) {\n            const hours = Math.floor(diffMins / 60);\n            const remainingMins = diffMins % 60;\n            if (remainingMins === 0) {\n                return \"\".concat(hours, \"h\");\n            } else {\n                return \"\".concat(hours, \"h \").concat(remainingMins, \"min\");\n            }\n        } else {\n            return \"\".concat(diffMins, \"min\");\n        }\n    };\n    // Table columns\n    const columns = [\n        {\n            header: \"Period\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-teal rounded-full flex items-center justify-center text-white font-semibold text-sm\",\n                            children: row.period_number\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium\",\n                            children: [\n                                \"Period \",\n                                row.period_number\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Start Time\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"font-mono text-sm\",\n                    children: formatTime(row.start_time)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"End Time\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"font-mono text-sm\",\n                    children: formatTime(row.end_time)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Duration\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm text-foreground/70\",\n                    children: calculateDuration(row.start_time, row.end_time)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Time Range\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2 text-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartNoAxesGantt_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"h-4 w-4 text-foreground/50\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                formatTime(row.start_time),\n                                \" - \",\n                                formatTime(row.end_time)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    // Table actions\n    const actions = [\n        {\n            label: \"Edit\",\n            onClick: (period)=>{\n                setPeriodToEdit(period);\n                setIsPeriodModalOpen(true);\n            }\n        },\n        {\n            label: \"Delete\",\n            onClick: (period)=>{\n                handleDeletePeriod(period);\n            }\n        }\n    ];\n    // CRUD Functions\n    const handleCreatePeriod = ()=>{\n        setPeriodToEdit(null);\n        setIsPeriodModalOpen(true);\n    };\n    const handleEditPeriod = (period)=>{\n        setPeriodToEdit(period);\n        setIsPeriodModalOpen(true);\n    };\n    const handleDeletePeriod = (period)=>{\n        setPeriodToDelete(period);\n        setDeleteType(\"single\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleDeleteMultiple = (selectedIds)=>{\n        setDeleteType(\"multiple\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleDeleteAll = ()=>{\n        setDeleteType(\"all\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleSelectionChange = (selectedRows)=>{\n        setSelectedPeriods(selectedRows);\n    };\n    // Modal submission functions\n    const handlePeriodSubmit = async (data)=>{\n        if (!schoolId) {\n            setError(\"No school ID found\");\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            if (periodToEdit) {\n                // Update existing period\n                await (0,_app_services_PeriodServices__WEBPACK_IMPORTED_MODULE_8__.updatePeriod)(schoolId, periodToEdit._id, data);\n            } else {\n                // Create new period\n                await (0,_app_services_PeriodServices__WEBPACK_IMPORTED_MODULE_8__.createPeriod)(schoolId, data);\n            }\n            // Refresh periods list\n            const response = await (0,_app_services_PeriodServices__WEBPACK_IMPORTED_MODULE_8__.getPeriods)(schoolId);\n            setPeriods(response.periods);\n            setIsPeriodModalOpen(false);\n            setPeriodToEdit(null);\n            // Show success notification\n            showSuccess(periodToEdit ? \"Period Updated\" : \"Period Created\", periodToEdit ? \"Period has been updated successfully.\" : \"New period has been created successfully.\");\n        } catch (error) {\n            console.error(\"Error submitting period:\", error);\n            showError(\"Error\", \"Failed to save period. Please try again.\");\n            throw error;\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleDeleteConfirm = async ()=>{\n        if (!schoolId) return;\n        try {\n            if (deleteType === \"single\" && periodToDelete) {\n                await (0,_app_services_PeriodServices__WEBPACK_IMPORTED_MODULE_8__.deletePeriod)(schoolId, periodToDelete._id);\n            } else if (deleteType === \"multiple\") {\n                const selectedIds = selectedPeriods.map((p)=>p._id);\n                await (0,_app_services_PeriodServices__WEBPACK_IMPORTED_MODULE_8__.deleteMultiplePeriods)(selectedIds);\n                setClearSelection(true);\n            } else if (deleteType === \"all\") {\n                await (0,_app_services_PeriodServices__WEBPACK_IMPORTED_MODULE_8__.deleteAllPeriods)();\n                setClearSelection(true);\n            }\n            // Refresh periods list\n            const response = await (0,_app_services_PeriodServices__WEBPACK_IMPORTED_MODULE_8__.getPeriods)(schoolId);\n            setPeriods(response.periods);\n            setIsDeleteModalOpen(false);\n            setPeriodToDelete(null);\n            setSelectedPeriods([]);\n            // Show success notification\n            if (deleteType === \"single\") {\n                showSuccess(\"Period Deleted\", \"Period has been deleted successfully.\");\n            } else if (deleteType === \"multiple\") {\n                showSuccess(\"Periods Deleted\", \"\".concat(selectedPeriods.length, \" periods have been deleted successfully.\"));\n            } else {\n                showSuccess(\"All Periods Deleted\", \"All periods have been deleted successfully.\");\n            }\n        } catch (error) {\n            console.error(\"Error deleting period(s):\", error);\n            showError(\"Error\", \"Failed to delete period(s). Please try again.\");\n            throw error;\n        }\n    };\n    if (loadingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                size: 32,\n                color: \"teal\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                lineNumber: 282,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n            lineNumber: 281,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            allowedRoles: [\n                \"school_admin\",\n                \"admin\",\n                \"super\"\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                navigation: navigation,\n                onLogout: logout,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen bg-background flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-red-500 text-lg font-semibold mb-2\",\n                                children: \"Error\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-foreground/60\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.location.reload(),\n                                className: \"mt-4 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600\",\n                                children: \"Retry\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                lineNumber: 290,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n            lineNumber: 289,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        allowedRoles: [\n            \"school_admin\",\n            \"admin\",\n            \"super\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            navigation: navigation,\n            onLogout: logout,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-teal rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartNoAxesGantt_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: \"School Periods\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-foreground/60\",\n                                                        children: \"Manage class periods and time slots for your school\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-foreground\",\n                                                children: periods.length\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-foreground/60\",\n                                                children: \"Total Periods\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Total Periods\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: periods.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartNoAxesGantt_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"First Period\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-bold text-foreground\",\n                                                        children: periods.length > 0 ? formatTime(periods[0].start_time) : 'N/A'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartNoAxesGantt_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Last Period\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-bold text-foreground\",\n                                                        children: periods.length > 0 ? formatTime(periods[periods.length - 1].end_time) : 'N/A'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartNoAxesGantt_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Avg Duration\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-bold text-foreground\",\n                                                        children: periods.length > 0 ? '45 min' : 'N/A'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartNoAxesGantt_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-foreground\",\n                                            children: \"All Periods\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            transition: {\n                                                type: 'spring',\n                                                stiffness: 300\n                                            },\n                                            onClick: handleCreatePeriod,\n                                            className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartNoAxesGantt_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Add Period\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        size: 24,\n                                        color: \"teal\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 33\n                                    }, void 0),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_TableFix__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        data: periods,\n                                        columns: columns,\n                                        actions: actions,\n                                        defaultItemsPerPage: 10,\n                                        onSelectionChange: handleSelectionChange,\n                                        handleDeleteMultiple: handleDeleteMultiple,\n                                        handleDeleteAll: handleDeleteAll,\n                                        clearSelection: clearSelection,\n                                        onSelectionCleared: ()=>setClearSelection(false)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_PeriodModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    isOpen: isPeriodModalOpen,\n                    onClose: ()=>{\n                        setIsPeriodModalOpen(false);\n                        setPeriodToEdit(null);\n                    },\n                    onSubmit: handlePeriodSubmit,\n                    period: periodToEdit,\n                    existingPeriods: periods,\n                    loading: isSubmitting\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                    lineNumber: 425,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_DeleteConfirmationModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    isOpen: isDeleteModalOpen,\n                    onClose: ()=>{\n                        setIsDeleteModalOpen(false);\n                        setPeriodToDelete(null);\n                    },\n                    onConfirm: handleDeleteConfirm,\n                    title: deleteType === \"single\" ? \"Delete Period\" : deleteType === \"multiple\" ? \"Delete Selected Periods\" : \"Delete All Periods\",\n                    message: deleteType === \"single\" ? \"Are you sure you want to delete this period? This action cannot be undone.\" : deleteType === \"multiple\" ? \"Are you sure you want to delete \".concat(selectedPeriods.length, \" selected periods? This action cannot be undone.\") : \"Are you sure you want to delete ALL periods? This action cannot be undone and will remove all period data.\",\n                    itemName: deleteType === \"single\" && periodToDelete ? \"Period \".concat(periodToDelete.period_number, \" (\").concat(periodToDelete.start_time, \" - \").concat(periodToDelete.end_time, \")\") : undefined,\n                    itemCount: deleteType === \"multiple\" ? selectedPeriods.length : undefined,\n                    type: deleteType\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                    lineNumber: 438,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Toast__WEBPACK_IMPORTED_MODULE_11__.ToastContainer, {\n                    toasts: toasts,\n                    onClose: removeToast\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n                    lineNumber: 469,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n            lineNumber: 310,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\period\\\\page.tsx\",\n        lineNumber: 309,\n        columnNumber: 5\n    }, this);\n}\n_s(PeriodsPage, \"rMMPj3QnZqbDOAWWAk3vSZNXQoQ=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        _components_ui_Toast__WEBPACK_IMPORTED_MODULE_11__.useToast\n    ];\n});\n_c = PeriodsPage;\nvar _c;\n$RefreshReg$(_c, \"PeriodsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboards)/school-admin/period/page.tsx\n"));

/***/ })

});