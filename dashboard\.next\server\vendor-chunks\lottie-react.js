"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/lottie-react";
exports.ids = ["vendor-chunks/lottie-react"];
exports.modules = {

/***/ "(ssr)/./node_modules/lottie-react/build/index.es.js":
/*!*****************************************************!*\
  !*** ./node_modules/lottie-react/build/index.es.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LottiePlayer: () => (/* reexport default from dynamic */ lottie_web__WEBPACK_IMPORTED_MODULE_0___default.a),\n/* harmony export */   \"default\": () => (/* binding */ Lottie),\n/* harmony export */   useLottie: () => (/* binding */ useLottie),\n/* harmony export */   useLottieInteractivity: () => (/* binding */ useLottieInteractivity)\n/* harmony export */ });\n/* harmony import */ var lottie_web__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lottie-web */ \"(ssr)/./node_modules/lottie-web/build/player/lottie.js\");\n/* harmony import */ var lottie_web__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(lottie_web__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\n\nfunction _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\nfunction _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var s = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < s.length; r++) o = s[r], t.includes(o) || {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (e.includes(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nfunction _slicedToArray(r, e) {\n  return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest();\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return _arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;\n  }\n}\n\nvar _excluded$1 = [\"animationData\", \"loop\", \"autoplay\", \"initialSegment\", \"onComplete\", \"onLoopComplete\", \"onEnterFrame\", \"onSegmentStart\", \"onConfigReady\", \"onDataReady\", \"onDataFailed\", \"onLoadedImages\", \"onDOMLoaded\", \"onDestroy\", \"lottieRef\", \"renderer\", \"name\", \"assetsPath\", \"rendererSettings\"];\nvar useLottie = function useLottie(props, style) {\n  var animationData = props.animationData,\n    loop = props.loop,\n    autoplay = props.autoplay,\n    initialSegment = props.initialSegment,\n    onComplete = props.onComplete,\n    onLoopComplete = props.onLoopComplete,\n    onEnterFrame = props.onEnterFrame,\n    onSegmentStart = props.onSegmentStart,\n    onConfigReady = props.onConfigReady,\n    onDataReady = props.onDataReady,\n    onDataFailed = props.onDataFailed,\n    onLoadedImages = props.onLoadedImages,\n    onDOMLoaded = props.onDOMLoaded,\n    onDestroy = props.onDestroy;\n    props.lottieRef;\n    props.renderer;\n    props.name;\n    props.assetsPath;\n    props.rendererSettings;\n    var rest = _objectWithoutProperties(props, _excluded$1);\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    animationLoaded = _useState2[0],\n    setAnimationLoaded = _useState2[1];\n  var animationInstanceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n  var animationContainer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n  /*\n        ======================================\n            INTERACTION METHODS\n        ======================================\n     */\n  /**\n   * Play\n   */\n  var play = function play() {\n    var _a;\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.play();\n  };\n  /**\n   * Stop\n   */\n  var stop = function stop() {\n    var _a;\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.stop();\n  };\n  /**\n   * Pause\n   */\n  var pause = function pause() {\n    var _a;\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.pause();\n  };\n  /**\n   * Set animation speed\n   * @param speed\n   */\n  var setSpeed = function setSpeed(speed) {\n    var _a;\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.setSpeed(speed);\n  };\n  /**\n   * Got to frame and play\n   * @param value\n   * @param isFrame\n   */\n  var goToAndPlay = function goToAndPlay(value, isFrame) {\n    var _a;\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.goToAndPlay(value, isFrame);\n  };\n  /**\n   * Got to frame and stop\n   * @param value\n   * @param isFrame\n   */\n  var goToAndStop = function goToAndStop(value, isFrame) {\n    var _a;\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.goToAndStop(value, isFrame);\n  };\n  /**\n   * Set animation direction\n   * @param direction\n   */\n  var setDirection = function setDirection(direction) {\n    var _a;\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.setDirection(direction);\n  };\n  /**\n   * Play animation segments\n   * @param segments\n   * @param forceFlag\n   */\n  var playSegments = function playSegments(segments, forceFlag) {\n    var _a;\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.playSegments(segments, forceFlag);\n  };\n  /**\n   * Set sub frames\n   * @param useSubFrames\n   */\n  var setSubframe = function setSubframe(useSubFrames) {\n    var _a;\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.setSubframe(useSubFrames);\n  };\n  /**\n   * Get animation duration\n   * @param inFrames\n   */\n  var getDuration = function getDuration(inFrames) {\n    var _a;\n    return (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.getDuration(inFrames);\n  };\n  /**\n   * Destroy animation\n   */\n  var destroy = function destroy() {\n    var _a;\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.destroy();\n    // Removing the reference to the animation so separate cleanups are skipped.\n    // Without it the internal `lottie-react` instance throws exceptions as it already cleared itself on destroy.\n    animationInstanceRef.current = undefined;\n  };\n  /*\n        ======================================\n            LOTTIE\n        ======================================\n     */\n  /**\n   * Load a new animation, and if it's the case, destroy the previous one\n   * @param {Object} forcedConfigs\n   */\n  var loadAnimation = function loadAnimation() {\n    var forcedConfigs = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var _a;\n    // Return if the container ref is null\n    if (!animationContainer.current) {\n      return;\n    }\n    // Destroy any previous instance\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.destroy();\n    // Build the animation configuration\n    var config = _objectSpread2(_objectSpread2(_objectSpread2({}, props), forcedConfigs), {}, {\n      container: animationContainer.current\n    });\n    // Save the animation instance\n    animationInstanceRef.current = lottie_web__WEBPACK_IMPORTED_MODULE_0___default().loadAnimation(config);\n    setAnimationLoaded(!!animationInstanceRef.current);\n    // Return a function that will clean up\n    return function () {\n      var _a;\n      (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.destroy();\n      animationInstanceRef.current = undefined;\n    };\n  };\n  /**\n   * (Re)Initialize when animation data changed\n   */\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    var onUnmount = loadAnimation();\n    // Clean up on unmount\n    return function () {\n      return onUnmount === null || onUnmount === void 0 ? void 0 : onUnmount();\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [animationData, loop]);\n  // Update the autoplay state\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    if (!animationInstanceRef.current) {\n      return;\n    }\n    animationInstanceRef.current.autoplay = !!autoplay;\n  }, [autoplay]);\n  // Update the initial segment state\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    if (!animationInstanceRef.current) {\n      return;\n    }\n    // When null should reset to default animation length\n    if (!initialSegment) {\n      animationInstanceRef.current.resetSegments(true);\n      return;\n    }\n    // If it's not a valid segment, do nothing\n    if (!Array.isArray(initialSegment) || !initialSegment.length) {\n      return;\n    }\n    // If the current position it's not in the new segment\n    // set the current position to start\n    if (animationInstanceRef.current.currentRawFrame < initialSegment[0] || animationInstanceRef.current.currentRawFrame > initialSegment[1]) {\n      animationInstanceRef.current.currentRawFrame = initialSegment[0];\n    }\n    // Update the segment\n    animationInstanceRef.current.setSegment(initialSegment[0], initialSegment[1]);\n  }, [initialSegment]);\n  /*\n        ======================================\n            EVENTS\n        ======================================\n     */\n  /**\n   * Reinitialize listener on change\n   */\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    var partialListeners = [{\n      name: \"complete\",\n      handler: onComplete\n    }, {\n      name: \"loopComplete\",\n      handler: onLoopComplete\n    }, {\n      name: \"enterFrame\",\n      handler: onEnterFrame\n    }, {\n      name: \"segmentStart\",\n      handler: onSegmentStart\n    }, {\n      name: \"config_ready\",\n      handler: onConfigReady\n    }, {\n      name: \"data_ready\",\n      handler: onDataReady\n    }, {\n      name: \"data_failed\",\n      handler: onDataFailed\n    }, {\n      name: \"loaded_images\",\n      handler: onLoadedImages\n    }, {\n      name: \"DOMLoaded\",\n      handler: onDOMLoaded\n    }, {\n      name: \"destroy\",\n      handler: onDestroy\n    }];\n    var listeners = partialListeners.filter(function (listener) {\n      return listener.handler != null;\n    });\n    if (!listeners.length) {\n      return;\n    }\n    var deregisterList = listeners.map(\n    /**\n     * Handle the process of adding an event listener\n     * @param {Listener} listener\n     * @return {Function} Function that deregister the listener\n     */\n    function (listener) {\n      var _a;\n      (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.addEventListener(listener.name, listener.handler);\n      // Return a function to deregister this listener\n      return function () {\n        var _a;\n        (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.removeEventListener(listener.name, listener.handler);\n      };\n    });\n    // Deregister listeners on unmount\n    return function () {\n      deregisterList.forEach(function (deregister) {\n        return deregister();\n      });\n    };\n  }, [onComplete, onLoopComplete, onEnterFrame, onSegmentStart, onConfigReady, onDataReady, onDataFailed, onLoadedImages, onDOMLoaded, onDestroy]);\n  /**\n   * Build the animation view\n   */\n  var View = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", _objectSpread2({\n    style: style,\n    ref: animationContainer\n  }, rest));\n  return {\n    View: View,\n    play: play,\n    stop: stop,\n    pause: pause,\n    setSpeed: setSpeed,\n    goToAndStop: goToAndStop,\n    goToAndPlay: goToAndPlay,\n    setDirection: setDirection,\n    playSegments: playSegments,\n    setSubframe: setSubframe,\n    getDuration: getDuration,\n    destroy: destroy,\n    animationContainerRef: animationContainer,\n    animationLoaded: animationLoaded,\n    animationItem: animationInstanceRef.current\n  };\n};\n\n// helpers\nfunction getContainerVisibility(container) {\n  var _container$getBoundin = container.getBoundingClientRect(),\n    top = _container$getBoundin.top,\n    height = _container$getBoundin.height;\n  var current = window.innerHeight - top;\n  var max = window.innerHeight + height;\n  return current / max;\n}\nfunction getContainerCursorPosition(container, cursorX, cursorY) {\n  var _container$getBoundin2 = container.getBoundingClientRect(),\n    top = _container$getBoundin2.top,\n    left = _container$getBoundin2.left,\n    width = _container$getBoundin2.width,\n    height = _container$getBoundin2.height;\n  var x = (cursorX - left) / width;\n  var y = (cursorY - top) / height;\n  return {\n    x: x,\n    y: y\n  };\n}\nvar useInitInteractivity = function useInitInteractivity(_ref) {\n  var wrapperRef = _ref.wrapperRef,\n    animationItem = _ref.animationItem,\n    mode = _ref.mode,\n    actions = _ref.actions;\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    var wrapper = wrapperRef.current;\n    if (!wrapper || !animationItem || !actions.length) {\n      return;\n    }\n    animationItem.stop();\n    var scrollModeHandler = function scrollModeHandler() {\n      var assignedSegment = null;\n      var scrollHandler = function scrollHandler() {\n        var currentPercent = getContainerVisibility(wrapper);\n        // Find the first action that satisfies the current position conditions\n        var action = actions.find(function (_ref2) {\n          var visibility = _ref2.visibility;\n          return visibility && currentPercent >= visibility[0] && currentPercent <= visibility[1];\n        });\n        // Skip if no matching action was found!\n        if (!action) {\n          return;\n        }\n        if (action.type === \"seek\" && action.visibility && action.frames.length === 2) {\n          // Seek: Go to a frame based on player scroll position action\n          var frameToGo = action.frames[0] + Math.ceil((currentPercent - action.visibility[0]) / (action.visibility[1] - action.visibility[0]) * action.frames[1]);\n          //! goToAndStop must be relative to the start of the current segment\n          animationItem.goToAndStop(frameToGo - animationItem.firstFrame - 1, true);\n        }\n        if (action.type === \"loop\") {\n          // Loop: Loop a given frames\n          if (assignedSegment === null) {\n            // if not playing any segments currently. play those segments and save to state\n            animationItem.playSegments(action.frames, true);\n            assignedSegment = action.frames;\n          } else {\n            // if playing any segments currently.\n            //check if segments in state are equal to the frames selected by action\n            if (assignedSegment !== action.frames) {\n              // if they are not equal. new segments are to be loaded\n              animationItem.playSegments(action.frames, true);\n              assignedSegment = action.frames;\n            } else if (animationItem.isPaused) {\n              // if they are equal the play method must be called only if lottie is paused\n              animationItem.playSegments(action.frames, true);\n              assignedSegment = action.frames;\n            }\n          }\n        }\n        if (action.type === \"play\" && animationItem.isPaused) {\n          // Play: Reset segments and continue playing full animation from current position\n          animationItem.resetSegments(true);\n          animationItem.play();\n        }\n        if (action.type === \"stop\") {\n          // Stop: Stop playback\n          animationItem.goToAndStop(action.frames[0] - animationItem.firstFrame - 1, true);\n        }\n      };\n      document.addEventListener(\"scroll\", scrollHandler);\n      return function () {\n        document.removeEventListener(\"scroll\", scrollHandler);\n      };\n    };\n    var cursorModeHandler = function cursorModeHandler() {\n      var handleCursor = function handleCursor(_x, _y) {\n        var x = _x;\n        var y = _y;\n        // Resolve cursor position if cursor is inside container\n        if (x !== -1 && y !== -1) {\n          // Get container cursor position\n          var pos = getContainerCursorPosition(wrapper, x, y);\n          // Use the resolved position\n          x = pos.x;\n          y = pos.y;\n        }\n        // Find the first action that satisfies the current position conditions\n        var action = actions.find(function (_ref3) {\n          var position = _ref3.position;\n          if (position && Array.isArray(position.x) && Array.isArray(position.y)) {\n            return x >= position.x[0] && x <= position.x[1] && y >= position.y[0] && y <= position.y[1];\n          }\n          if (position && !Number.isNaN(position.x) && !Number.isNaN(position.y)) {\n            return x === position.x && y === position.y;\n          }\n          return false;\n        });\n        // Skip if no matching action was found!\n        if (!action) {\n          return;\n        }\n        // Process action types:\n        if (action.type === \"seek\" && action.position && Array.isArray(action.position.x) && Array.isArray(action.position.y) && action.frames.length === 2) {\n          // Seek: Go to a frame based on player scroll position action\n          var xPercent = (x - action.position.x[0]) / (action.position.x[1] - action.position.x[0]);\n          var yPercent = (y - action.position.y[0]) / (action.position.y[1] - action.position.y[0]);\n          animationItem.playSegments(action.frames, true);\n          animationItem.goToAndStop(Math.ceil((xPercent + yPercent) / 2 * (action.frames[1] - action.frames[0])), true);\n        }\n        if (action.type === \"loop\") {\n          animationItem.playSegments(action.frames, true);\n        }\n        if (action.type === \"play\") {\n          // Play: Reset segments and continue playing full animation from current position\n          if (animationItem.isPaused) {\n            animationItem.resetSegments(false);\n          }\n          animationItem.playSegments(action.frames);\n        }\n        if (action.type === \"stop\") {\n          animationItem.goToAndStop(action.frames[0], true);\n        }\n      };\n      var mouseMoveHandler = function mouseMoveHandler(ev) {\n        handleCursor(ev.clientX, ev.clientY);\n      };\n      var mouseOutHandler = function mouseOutHandler() {\n        handleCursor(-1, -1);\n      };\n      wrapper.addEventListener(\"mousemove\", mouseMoveHandler);\n      wrapper.addEventListener(\"mouseout\", mouseOutHandler);\n      return function () {\n        wrapper.removeEventListener(\"mousemove\", mouseMoveHandler);\n        wrapper.removeEventListener(\"mouseout\", mouseOutHandler);\n      };\n    };\n    switch (mode) {\n      case \"scroll\":\n        return scrollModeHandler();\n      case \"cursor\":\n        return cursorModeHandler();\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [mode, animationItem]);\n};\nvar useLottieInteractivity = function useLottieInteractivity(_ref4) {\n  var actions = _ref4.actions,\n    mode = _ref4.mode,\n    lottieObj = _ref4.lottieObj;\n  var animationItem = lottieObj.animationItem,\n    View = lottieObj.View,\n    animationContainerRef = lottieObj.animationContainerRef;\n  useInitInteractivity({\n    actions: actions,\n    animationItem: animationItem,\n    mode: mode,\n    wrapperRef: animationContainerRef\n  });\n  return View;\n};\n\nvar _excluded = [\"style\", \"interactivity\"];\nvar Lottie = function Lottie(props) {\n  var _a, _b, _c;\n  var style = props.style,\n    interactivity = props.interactivity,\n    lottieProps = _objectWithoutProperties(props, _excluded);\n  /**\n   * Initialize the 'useLottie' hook\n   */\n  var _useLottie = useLottie(lottieProps, style),\n    View = _useLottie.View,\n    play = _useLottie.play,\n    stop = _useLottie.stop,\n    pause = _useLottie.pause,\n    setSpeed = _useLottie.setSpeed,\n    goToAndStop = _useLottie.goToAndStop,\n    goToAndPlay = _useLottie.goToAndPlay,\n    setDirection = _useLottie.setDirection,\n    playSegments = _useLottie.playSegments,\n    setSubframe = _useLottie.setSubframe,\n    getDuration = _useLottie.getDuration,\n    destroy = _useLottie.destroy,\n    animationContainerRef = _useLottie.animationContainerRef,\n    animationLoaded = _useLottie.animationLoaded,\n    animationItem = _useLottie.animationItem;\n  /**\n   * Make the hook variables/methods available through the provided 'lottieRef'\n   */\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    if (props.lottieRef) {\n      props.lottieRef.current = {\n        play: play,\n        stop: stop,\n        pause: pause,\n        setSpeed: setSpeed,\n        goToAndPlay: goToAndPlay,\n        goToAndStop: goToAndStop,\n        setDirection: setDirection,\n        playSegments: playSegments,\n        setSubframe: setSubframe,\n        getDuration: getDuration,\n        destroy: destroy,\n        animationContainerRef: animationContainerRef,\n        animationLoaded: animationLoaded,\n        animationItem: animationItem\n      };\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [(_a = props.lottieRef) === null || _a === void 0 ? void 0 : _a.current]);\n  return useLottieInteractivity({\n    lottieObj: {\n      View: View,\n      play: play,\n      stop: stop,\n      pause: pause,\n      setSpeed: setSpeed,\n      goToAndStop: goToAndStop,\n      goToAndPlay: goToAndPlay,\n      setDirection: setDirection,\n      playSegments: playSegments,\n      setSubframe: setSubframe,\n      getDuration: getDuration,\n      destroy: destroy,\n      animationContainerRef: animationContainerRef,\n      animationLoaded: animationLoaded,\n      animationItem: animationItem\n    },\n    actions: (_b = interactivity === null || interactivity === void 0 ? void 0 : interactivity.actions) !== null && _b !== void 0 ? _b : [],\n    mode: (_c = interactivity === null || interactivity === void 0 ? void 0 : interactivity.mode) !== null && _c !== void 0 ? _c : \"scroll\"\n  });\n};\n\n\n//# sourceMappingURL=index.es.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lottie-react/build/index.es.js\n");

/***/ })

};
;