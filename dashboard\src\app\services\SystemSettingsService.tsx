import Cookies from "js-cookie";
import { BASE_API_URL } from "./AuthContext";

export function getTokenFromCookie(name: string) {
    const token = Cookies.get(name);
    return token;
}

// System Settings Interfaces
export interface SystemSettings {
    platformName: string;
    platformDescription: string;
    supportEmail: string;
    maintenanceMode: boolean;
    allowNewRegistrations: boolean;
    maxSchoolsPerSubscription: number;
    defaultSubscriptionDuration: number;
    emailNotifications: boolean;
    smsNotifications: boolean;
    systemBackupFrequency: string;
    dataRetentionPeriod: number;
}

export interface SecuritySettings {
    passwordMinLength: number;
    requireSpecialCharacters: boolean;
    sessionTimeout: number;
    maxLoginAttempts: number;
    twoFactorRequired: boolean;
    ipWhitelist: string[];
}

// Get system settings
export async function getSystemSettings(): Promise<SystemSettings> {
    try {
        const token = getTokenFromCookie("idToken");
        const response = await fetch(`${BASE_API_URL}/system/settings`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
            },
        });

        if (!response.ok) {
            console.error(
                "Error fetching system settings:",
                response.statusText
            );
            throw new Error("Failed to fetch system settings");
        }

        const settings = await response.json();
        return settings;
    } catch (error) {
        console.error("Error fetching system settings:", error);
        // Return default settings if API call fails
        return {
            platformName: "Scholarify",
            platformDescription: "Comprehensive School Management System",
            supportEmail: "<EMAIL>",
            maintenanceMode: false,
            allowNewRegistrations: true,
            maxSchoolsPerSubscription: 5,
            defaultSubscriptionDuration: 12,
            emailNotifications: true,
            smsNotifications: true,
            systemBackupFrequency: "daily",
            dataRetentionPeriod: 365,
        };
    }
}

// Update system settings
export async function updateSystemSettings(
    settings: SystemSettings
): Promise<SystemSettings> {
    try {
        const token = getTokenFromCookie("idToken");
        const response = await fetch(`${BASE_API_URL}/system/settings`, {
            method: "PUT",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify(settings),
        });

        if (!response.ok) {
            let errorMessage = "Failed to update system settings";

            try {
                const errorBody = await response.json();
                errorMessage = errorBody?.message || errorMessage;
            } catch (parseError) {
                console.warn("Could not parse error response:", parseError);
            }

            console.error("Error updating system settings:", errorMessage);
            throw new Error(errorMessage);
        }

        const updatedSettings = await response.json();
        return updatedSettings;
    } catch (error) {
        console.error("Error updating system settings:", error);
        throw new Error(
            error instanceof Error
                ? error.message
                : "Failed to update system settings"
        );
    }
}

// Get security settings
export async function getSecuritySettings(): Promise<SecuritySettings> {
    try {
        const token = getTokenFromCookie("idToken");
        const response = await fetch(
            `${BASE_API_URL}/system/security-settings`,
            {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token}`,
                },
            }
        );

        if (!response.ok) {
            console.error(
                "Error fetching security settings:",
                response.statusText
            );
            throw new Error("Failed to fetch security settings");
        }

        const settings = await response.json();
        return settings;
    } catch (error) {
        console.error("Error fetching security settings:", error);
        // Return default settings if API call fails
        return {
            passwordMinLength: 8,
            requireSpecialCharacters: true,
            sessionTimeout: 30,
            maxLoginAttempts: 5,
            twoFactorRequired: false,
            ipWhitelist: [],
        };
    }
}

// Update security settings
export async function updateSecuritySettings(
    settings: SecuritySettings
): Promise<SecuritySettings> {
    try {
        const token = getTokenFromCookie("idToken");
        const response = await fetch(
            `${BASE_API_URL}/system/security-settings`,
            {
                method: "PUT",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token}`,
                },
                body: JSON.stringify(settings),
            }
        );

        if (!response.ok) {
            let errorMessage = "Failed to update security settings";

            try {
                const errorBody = await response.json();
                errorMessage = errorBody?.message || errorMessage;
            } catch (parseError) {
                console.warn("Could not parse error response:", parseError);
            }

            console.error("Error updating security settings:", errorMessage);
            throw new Error(errorMessage);
        }

        const updatedSettings = await response.json();
        return updatedSettings;
    } catch (error) {
        console.error("Error updating security settings:", error);
        throw new Error(
            error instanceof Error
                ? error.message
                : "Failed to update security settings"
        );
    }
}

// Get notification preferences
export async function getNotificationSettings(): Promise<{
    emailNotifications: boolean;
    smsNotifications: boolean;
}> {
    try {
        const token = getTokenFromCookie("idToken");
        const response = await fetch(
            `${BASE_API_URL}/system/notification-settings`,
            {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token}`,
                },
            }
        );

        if (!response.ok) {
            console.error(
                "Error fetching notification settings:",
                response.statusText
            );
            throw new Error("Failed to fetch notification settings");
        }

        const settings = await response.json();
        return settings;
    } catch (error) {
        console.error("Error fetching notification settings:", error);
        // Return default settings if API call fails
        return {
            emailNotifications: true,
            smsNotifications: true,
        };
    }
}

// Update notification preferences
export async function updateNotificationSettings(settings: {
    emailNotifications: boolean;
    smsNotifications: boolean;
}): Promise<{ emailNotifications: boolean; smsNotifications: boolean }> {
    try {
        const token = getTokenFromCookie("idToken");
        const response = await fetch(
            `${BASE_API_URL}/system/notification-settings`,
            {
                method: "PUT",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token}`,
                },
                body: JSON.stringify(settings),
            }
        );

        if (!response.ok) {
            let errorMessage = "Failed to update notification settings";

            try {
                const errorBody = await response.json();
                errorMessage = errorBody?.message || errorMessage;
            } catch (parseError) {
                console.warn("Could not parse error response:", parseError);
            }

            console.error(
                "Error updating notification settings:",
                errorMessage
            );
            throw new Error(errorMessage);
        }

        const updatedSettings = await response.json();
        return updatedSettings;
    } catch (error) {
        console.error("Error updating notification settings:", error);
        throw new Error(
            error instanceof Error
                ? error.message
                : "Failed to update notification settings"
        );
    }
}
