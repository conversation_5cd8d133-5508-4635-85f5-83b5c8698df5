"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(auth)/login/page",{

/***/ "(app-pages-browser)/./src/app/(auth)/login/page.tsx":
/*!***************************************!*\
  !*** ./src/app/(auth)/login/page.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Login)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_AppLogo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/AppLogo */ \"(app-pages-browser)/./src/components/AppLogo.tsx\");\n/* harmony import */ var _components_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/input */ \"(app-pages-browser)/./src/components/input.tsx\");\n/* harmony import */ var _components_FormHeading__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/FormHeading */ \"(app-pages-browser)/./src/components/FormHeading.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_NotificationCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/NotificationCard */ \"(app-pages-browser)/./src/components/NotificationCard.tsx\");\n/* harmony import */ var _styles_formStyle_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/styles/formStyle.css */ \"(app-pages-browser)/./src/styles/formStyle.css\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/widgets/CircularLoader */ \"(app-pages-browser)/./src/components/widgets/CircularLoader.tsx\");\n/* harmony import */ var _app_services_UserServices__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/services/UserServices */ \"(app-pages-browser)/./src/app/services/UserServices.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction Login() {\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isForgotPasswordLoading, setIsForgotPasswordLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { login, isAuthenticated, loading, redirectAfterLogin } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_7__[\"default\"])();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    // State pour les champs de formulaire\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [rememberMe, setRememberMe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errorMessage, setErrorMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Login.useEffect\": ()=>{\n            if (!loading && isAuthenticated) {\n                const redirectTo = redirectAfterLogin || '/super-admin/dashboard';\n                router.push(redirectTo);\n            }\n        }\n    }[\"Login.useEffect\"], [\n        isAuthenticated,\n        loading,\n        redirectAfterLogin,\n        router\n    ]);\n    if (loading || isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-screen w-full absolute top-0 left-0 z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                size: 40,\n                color: \"teal-500\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                lineNumber: 37,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n            lineNumber: 36,\n            columnNumber: 13\n        }, this);\n    }\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        try {\n            await login(email, password, rememberMe);\n            setErrorMessage('');\n            setHasError(false);\n            setEmail('');\n            setPassword('');\n            setRememberMe(false);\n            let user = await (0,_app_services_UserServices__WEBPACK_IMPORTED_MODULE_10__.getCurrentUser)();\n            if (user && user.role === 'super') {\n                router.push('/super-admin/dashboard');\n            } else if (user && user.role === 'admin') {\n                router.push('/school-admin/dashboard');\n            } else if (user && user.role === 'teacher') {\n                router.push('/school-admin/dashboard');\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"The email or password you entered doesn't match our records. Please double-check and try again.\";\n            setErrorMessage(errorMessage);\n            setHasError(true);\n        } finally{\n            setIsLoading(false);\n            setPassword('');\n        }\n    };\n    // Fonction pour gérer le clic sur le lien \"Forgot Password?\"\n    const handleForgotPasswordClick = (e)=>{\n        e.preventDefault();\n        setIsForgotPasswordLoading(true);\n        // Rediriger vers la page de récupération de mot de passe après un court délai\n        setTimeout(()=>{\n            router.push('/forgot-password');\n        }, 800); // Délai de 800ms pour montrer l'animation\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex bg-white dark:bg-gray-900 dark:text-white  rounded-lg shadow-lg h-screen px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"asideLogo w-[50%] h-screen py-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"asideImage w-full h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"/assets/images/asideImage2.png\",\n                            className: \"h-full w-full rounded-[25px]\",\n                            alt: \"Aside Image\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 17\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 13\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"asideForm  bg-white dark:bg-gray-900 flex flex-col justify-evenly items-center m-auto  \",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" flex flex-col justify-evenly items-center m-auto w-full max-w-[500px]  dark:text-white py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AppLogo__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                logoSrc: \"/assets/logo.png\",\n                                logoAlt: \"Logo\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mx-auto p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FormHeading__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        title: \"Nice to see you!\",\n                                        subtitle: \"Sign in to access your Dashboard\",\n                                        formIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"50\",\n                                            height: \"50\",\n                                            viewBox: \"0 0 50 50\",\n                                            fill: \"none\",\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                    x: \"1.25\",\n                                                    y: \"1.25\",\n                                                    width: \"47.5\",\n                                                    height: \"47.5\",\n                                                    rx: \"13.75\",\n                                                    stroke: \"#17B890\",\n                                                    strokeWidth: \"2.5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 37\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M27.167 17.832H30.5003C30.9424 17.832 31.3663 18.0076 31.6788 18.3202C31.9914 18.6327 32.167 19.0567 32.167 19.4987V31.1654C32.167 31.6074 31.9914 32.0313 31.6788 32.3439C31.3663 32.6564 30.9424 32.832 30.5003 32.832H27.167\",\n                                                    stroke: \"#17B890\",\n                                                    strokeWidth: \"2.5\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 37\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M23 29.4974L27.1667 25.3307L23 21.1641\",\n                                                    stroke: \"#17B890\",\n                                                    strokeWidth: \"2.5\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 37\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M27.167 25.332H17.167\",\n                                                    stroke: \"#17B890\",\n                                                    strokeWidth: \"2.5\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 37\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 33\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NotificationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        title: \"Error\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"20\",\n                                            height: \"20\",\n                                            viewBox: \"0 0 20 20\",\n                                            fill: \"none\",\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M8.4375 6.5625C8.4375 6.31527 8.51081 6.0736 8.64817 5.86804C8.78552 5.66248 8.98074 5.50226 9.20915 5.40765C9.43756 5.31304 9.68889 5.28829 9.93137 5.33652C10.1738 5.38475 10.3966 5.5038 10.5714 5.67862C10.7462 5.85343 10.8653 6.07616 10.9135 6.31864C10.9617 6.56111 10.937 6.81245 10.8424 7.04085C10.7477 7.26926 10.5875 7.46448 10.382 7.60184C10.1764 7.73919 9.93473 7.8125 9.6875 7.8125C9.35598 7.8125 9.03804 7.6808 8.80362 7.44638C8.5692 7.21196 8.4375 6.89402 8.4375 6.5625ZM18.4375 10C18.4375 11.6688 17.9427 13.3001 17.0155 14.6876C16.0884 16.0752 14.7706 17.1566 13.2289 17.7952C11.6871 18.4338 9.99064 18.6009 8.35393 18.2754C6.71721 17.9498 5.2138 17.1462 4.03379 15.9662C2.85378 14.7862 2.05019 13.2828 1.72463 11.6461C1.39907 10.0094 1.56616 8.31286 2.20477 6.77111C2.84338 5.22936 3.92484 3.9116 5.31238 2.98448C6.69992 2.05735 8.33122 1.5625 10 1.5625C12.237 1.56498 14.3817 2.45473 15.9635 4.03653C17.5453 5.61833 18.435 7.763 18.4375 10ZM16.5625 10C16.5625 8.70206 16.1776 7.43327 15.4565 6.35407C14.7354 5.27487 13.7105 4.43374 12.5114 3.93704C11.3122 3.44034 9.99272 3.31038 8.71972 3.5636C7.44672 3.81681 6.2774 4.44183 5.35962 5.35961C4.44183 6.27739 3.81682 7.44672 3.5636 8.71972C3.31038 9.99272 3.44034 11.3122 3.93704 12.5114C4.43374 13.7105 5.27488 14.7354 6.35407 15.4565C7.43327 16.1776 8.70206 16.5625 10 16.5625C11.7399 16.5606 13.408 15.8686 14.6383 14.6383C15.8686 13.408 16.5606 11.7399 16.5625 10ZM10.9375 12.8656V10.3125C10.9375 9.8981 10.7729 9.50067 10.4799 9.20764C10.1868 8.91462 9.7894 8.75 9.375 8.75C9.1536 8.74967 8.93923 8.82771 8.76986 8.97029C8.60048 9.11287 8.48703 9.31079 8.4496 9.52901C8.41217 9.74722 8.45318 9.97164 8.56536 10.1625C8.67754 10.3534 8.85365 10.4984 9.0625 10.5719V13.125C9.0625 13.5394 9.22712 13.9368 9.52015 14.2299C9.81317 14.5229 10.2106 14.6875 10.625 14.6875C10.8464 14.6878 11.0608 14.6098 11.2301 14.4672C11.3995 14.3246 11.513 14.1267 11.5504 13.9085C11.5878 13.6903 11.5468 13.4659 11.4346 13.275C11.3225 13.0841 11.1464 12.9391 10.9375 12.8656Z\",\n                                                fill: \"#F43F5E\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 37\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 33\n                                        }, void 0),\n                                        message: errorMessage,\n                                        type: \"error\",\n                                        isVisible: hasError,\n                                        onClose: ()=>{\n                                            setErrorMessage('');\n                                            setHasError(false);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSubmit,\n                                        className: \"flex flex-col\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_input__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    label: \"Email\",\n                                                    type: \"email\",\n                                                    id: \"email\",\n                                                    prefixIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"20\",\n                                                        height: \"20\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        fill: \"none\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M17.5 3.4375H2.5C2.25136 3.4375 2.0129 3.53627 1.83709 3.71209C1.66127 3.8879 1.5625 4.12636 1.5625 4.375V15C1.5625 15.4144 1.72712 15.8118 2.02015 16.1049C2.31317 16.3979 2.7106 16.5625 3.125 16.5625H16.875C17.2894 16.5625 17.6868 16.3979 17.9799 16.1049C18.2729 15.8118 18.4375 15.4144 18.4375 15V4.375C18.4375 4.12636 18.3387 3.8879 18.1629 3.71209C17.9871 3.53627 17.7486 3.4375 17.5 3.4375ZM15.0898 5.3125L10 9.97813L4.91016 5.3125H15.0898ZM3.4375 14.6875V6.50625L9.36641 11.9414C9.53932 12.1 9.7654 12.1879 10 12.1879C10.2346 12.1879 10.4607 12.1 10.6336 11.9414L16.5625 6.50625V14.6875H3.4375Z\",\n                                                            fill: \"#575D5E\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                            lineNumber: 132,\n                                                            columnNumber: 45\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 41\n                                                    }, void 0),\n                                                    value: email,\n                                                    onChange: (e)=>setEmail(e.target.value),\n                                                    className: \"w-full pl-10 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 33\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_input__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    label: \"Password\",\n                                                    type: \"password\",\n                                                    id: \"password\",\n                                                    prefixIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"20\",\n                                                        height: \"20\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        fill: \"none\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M16.25 5.9375H14.0625V4.375C14.0625 3.29756 13.6345 2.26425 12.8726 1.50238C12.1108 0.740512 11.0774 0.3125 10 0.3125C8.92256 0.3125 7.88925 0.740512 7.12738 1.50238C6.36551 2.26425 5.9375 3.29756 5.9375 4.375V5.9375H3.75C3.3356 5.9375 2.93817 6.10212 2.64515 6.39515C2.35212 6.68817 2.1875 7.0856 2.1875 7.5V16.25C2.1875 16.6644 2.35212 17.0618 2.64515 17.3549C2.93817 17.6479 3.3356 17.8125 3.75 17.8125H16.25C16.6644 17.8125 17.0618 17.6479 17.3549 17.3549C17.6479 17.0618 17.8125 16.6644 17.8125 16.25V7.5C17.8125 7.0856 17.6479 6.68817 17.3549 6.39515C17.0618 6.10212 16.6644 5.9375 16.25 5.9375ZM7.8125 4.375C7.8125 3.79484 8.04297 3.23844 8.4532 2.8282C8.86344 2.41797 9.41984 2.1875 10 2.1875C10.5802 2.1875 11.1366 2.41797 11.5468 2.8282C11.957 3.23844 12.1875 3.79484 12.1875 4.375V5.9375H7.8125V4.375ZM15.9375 15.9375H4.0625V7.8125H15.9375V15.9375Z\",\n                                                            fill: \"#575D5E\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 45\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 41\n                                                    }, void 0),\n                                                    value: password,\n                                                    onChange: (e)=>setPassword(e.target.value),\n                                                    className: \"w-full pl-10 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 33\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: rememberMe,\n                                                                onChange: (e)=>setRememberMe(e.target.checked),\n                                                                className: \"mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                                lineNumber: 161,\n                                                                columnNumber: 37\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-800 dark:text-gray-200\",\n                                                                children: \"Remember for 30 days\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                                lineNumber: 167,\n                                                                columnNumber: 37\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/forgot-password\",\n                                                        onClick: handleForgotPasswordClick,\n                                                        className: \"text-sm text-blue-600 hover:underline flex items-center\",\n                                                        children: isForgotPasswordLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-3 w-3 animate-spin mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 176,\n                                                                    columnNumber: 45\n                                                                }, this),\n                                                                \"Redirecting...\"\n                                                            ]\n                                                        }, void 0, true) : \"Forgot Password?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                className: \"w-full flex justify-center gap-2 items-center bg-[#17B890] text-white py-2 rounded-full hover:bg-[#17b890c4] transition duration-200\",\n                                                children: [\n                                                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 47\n                                                    }, this),\n                                                    \"Sign In\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"21\",\n                                                        height: \"20\",\n                                                        viewBox: \"0 0 21 20\",\n                                                        fill: \"none\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M10.1875 16.875C10.1875 17.1236 10.0887 17.3621 9.91291 17.5379C9.7371 17.7137 9.49864 17.8125 9.25 17.8125H4.25C4.00136 17.8125 3.7629 17.7137 3.58709 17.5379C3.41127 17.3621 3.3125 17.1236 3.3125 16.875V3.125C3.3125 2.87636 3.41127 2.6379 3.58709 2.46209C3.7629 2.28627 4.00136 2.1875 4.25 2.1875H9.25C9.49864 2.1875 9.7371 2.28627 9.91291 2.46209C10.0887 2.6379 10.1875 2.87636 10.1875 3.125C10.1875 3.37364 10.0887 3.6121 9.91291 3.78791C9.7371 3.96373 9.49864 4.0625 9.25 4.0625H5.1875V15.9375H9.25C9.49864 15.9375 9.7371 16.0363 9.91291 16.2121C10.0887 16.3879 10.1875 16.6264 10.1875 16.875ZM18.6633 9.33672L15.5383 6.21172C15.3622 6.0356 15.1233 5.93665 14.8742 5.93665C14.6251 5.93665 14.3863 6.0356 14.2102 6.21172C14.034 6.38784 13.9351 6.62671 13.9351 6.87578C13.9351 7.12485 14.034 7.36372 14.2102 7.53984L15.7344 9.0625H9.25C9.00136 9.0625 8.7629 9.16127 8.58709 9.33709C8.41127 9.5129 8.3125 9.75136 8.3125 10C8.3125 10.2486 8.41127 10.4871 8.58709 10.6629C8.7629 10.8387 9.00136 10.9375 9.25 10.9375H15.7344L14.2094 12.4617C14.0333 12.6378 13.9343 12.8767 13.9343 13.1258C13.9343 13.3749 14.0333 13.6137 14.2094 13.7898C14.3855 13.966 14.6244 14.0649 14.8734 14.0649C15.1225 14.0649 15.3614 13.966 15.5375 13.7898L18.6625 10.6648C18.7499 10.5778 18.8194 10.4743 18.8667 10.3604C18.9141 10.2465 18.9386 10.1243 18.9386 10.0009C18.9387 9.87755 18.9144 9.75537 18.8672 9.64138C18.8199 9.5274 18.7506 9.42387 18.6633 9.33672Z\",\n                                                            fill: \"#F5F6FA\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"flex justify-center items-center gap-5 py-4 text-gray-800 dark:text-gray-200 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"hover:text-[#17B890]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                children: \"Licence\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 66\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"hover:text-[#17B890]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                children: \"Terms of Use\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 66\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"hover:text-[#17B890]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                children: \"Blog\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 66\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 17\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 13\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n            lineNumber: 85,\n            columnNumber: 9\n        }, this)\n    }, void 0, false);\n}\n_s(Login, \"PCgw0vL19ldjmbvG/ktOa1VDU54=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter\n    ];\n});\n_c = Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvKGF1dGgpL2xvZ2luL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDdUQ7QUFDWjtBQUNKO0FBQ1k7QUFDWjtBQUNzQjtBQUM3QjtBQUNVO0FBQ1k7QUFDVztBQUNKO0FBRTlDLFNBQVNXOztJQUNwQixNQUFNLENBQUNDLFdBQVdDLGFBQWEsR0FBR1osK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDYSx5QkFBeUJDLDJCQUEyQixHQUFHZCwrQ0FBUUEsQ0FBQztJQUN2RSxNQUFNLEVBQUVlLEtBQUssRUFBRUMsZUFBZSxFQUFFQyxPQUFPLEVBQUVDLGtCQUFrQixFQUFFLEdBQUdaLDhEQUFPQTtJQUN2RSxNQUFNYSxTQUFTWiwwREFBU0E7SUFDeEIsc0NBQXNDO0lBQ3RDLE1BQU0sQ0FBQ2EsT0FBT0MsU0FBUyxHQUFHckIsK0NBQVFBLENBQUM7SUFDbkMsTUFBTSxDQUFDc0IsVUFBVUMsWUFBWSxHQUFHdkIsK0NBQVFBLENBQUM7SUFDekMsTUFBTSxDQUFDd0IsWUFBWUMsY0FBYyxHQUFHekIsK0NBQVFBLENBQUM7SUFFN0MsTUFBTSxDQUFDMEIsY0FBY0MsZ0JBQWdCLEdBQUczQiwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUM0QixVQUFVQyxZQUFZLEdBQUc3QiwrQ0FBUUEsQ0FBQztJQUV6Q0QsZ0RBQVNBOzJCQUFDO1lBQ04sSUFBSSxDQUFDa0IsV0FBV0QsaUJBQWlCO2dCQUM3QixNQUFNYyxhQUFhWixzQkFBc0I7Z0JBQ3pDQyxPQUFPWSxJQUFJLENBQUNEO1lBQ2hCO1FBQ0o7MEJBQUc7UUFBQ2Q7UUFBaUJDO1FBQVNDO1FBQW9CQztLQUFPO0lBRXpELElBQUlGLFdBQVdELGlCQUFpQjtRQUM1QixxQkFDSSw4REFBQ2dCO1lBQUlDLFdBQVU7c0JBQ1gsNEVBQUN6QiwwRUFBY0E7Z0JBQUMwQixNQUFNO2dCQUFJQyxPQUFNOzs7Ozs7Ozs7OztJQUc1QztJQUNBLE1BQU1DLGVBQWUsT0FBT0M7UUFDeEJBLEVBQUVDLGNBQWM7UUFDaEIxQixhQUFhO1FBQ2IsSUFBSTtZQUNBLE1BQU1HLE1BQU1LLE9BQU9FLFVBQVVFO1lBQzdCRyxnQkFBZ0I7WUFDaEJFLFlBQVk7WUFDWlIsU0FBUztZQUNURSxZQUFZO1lBQ1pFLGNBQWM7WUFDZCxJQUFJYyxPQUFPLE1BQU05QiwyRUFBY0E7WUFDL0IsSUFBSThCLFFBQVFBLEtBQUtDLElBQUksS0FBSyxTQUFTO2dCQUMvQnJCLE9BQU9ZLElBQUksQ0FBQztZQUNoQixPQUFPLElBQUlRLFFBQVFBLEtBQUtDLElBQUksS0FBSyxTQUFTO2dCQUN0Q3JCLE9BQU9ZLElBQUksQ0FBQztZQUNoQixPQUFPLElBQUlRLFFBQVFBLEtBQUtDLElBQUksS0FBSyxXQUFXO2dCQUN4Q3JCLE9BQU9ZLElBQUksQ0FBQztZQUNoQjtRQUNKLEVBQUUsT0FBT1UsT0FBTztZQUNaLE1BQU1mLGVBQ0ZlLGlCQUFpQkMsUUFDWEQsTUFBTUUsT0FBTyxHQUNiO1lBRVZoQixnQkFBZ0JEO1lBQ2hCRyxZQUFZO1FBQ2hCLFNBQVU7WUFDTmpCLGFBQWE7WUFDYlcsWUFBWTtRQUNoQjtJQUNKO0lBRUEsNkRBQTZEO0lBQzdELE1BQU1xQiw0QkFBNEIsQ0FBQ1A7UUFDL0JBLEVBQUVDLGNBQWM7UUFDaEJ4QiwyQkFBMkI7UUFFM0IsOEVBQThFO1FBQzlFK0IsV0FBVztZQUNQMUIsT0FBT1ksSUFBSSxDQUFDO1FBQ2hCLEdBQUcsTUFBTSwwQ0FBMEM7SUFDdkQ7SUFFQSxxQkFBTztrQkFDSCw0RUFBQ0M7WUFBSUMsV0FBVTs7OEJBRVgsOERBQUNEO29CQUFJQyxXQUFVOzhCQUNYLDRFQUFDRDt3QkFBSUMsV0FBVTtrQ0FDWCw0RUFBQ2E7NEJBQUlDLEtBQUk7NEJBQWlDZCxXQUFVOzRCQUErQmUsS0FBSTs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFHL0YsOERBQUNoQjtvQkFBSUMsV0FBVTs4QkFDWCw0RUFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNYLDhEQUFDaEMsMkRBQU9BO2dDQUFDZ0QsU0FBUTtnQ0FBbUJDLFNBQVE7Ozs7OzswQ0FDNUMsOERBQUNsQjtnQ0FBSUMsV0FBVTs7a0RBQ1gsOERBQUM5QiwrREFBV0E7d0NBQ1JnRCxPQUFNO3dDQUNOQyxVQUFTO3dDQUNUQyx3QkFDSSw4REFBQ0M7NENBQUlDLE9BQU07NENBQUtDLFFBQU87NENBQUtDLFNBQVE7NENBQVlDLE1BQUs7NENBQU9DLE9BQU07OzhEQUM5RCw4REFBQ0M7b0RBQUtDLEdBQUU7b0RBQU9DLEdBQUU7b0RBQU9QLE9BQU07b0RBQU9DLFFBQU87b0RBQU9PLElBQUc7b0RBQVFDLFFBQU87b0RBQVVDLGFBQVk7Ozs7Ozs4REFDM0YsOERBQUNDO29EQUFLQyxHQUFFO29EQUFrT0gsUUFBTztvREFBVUMsYUFBWTtvREFBTUcsZUFBYztvREFBUUMsZ0JBQWU7Ozs7Ozs4REFDbFQsOERBQUNIO29EQUFLQyxHQUFFO29EQUF5Q0gsUUFBTztvREFBVUMsYUFBWTtvREFBTUcsZUFBYztvREFBUUMsZ0JBQWU7Ozs7Ozs4REFDekgsOERBQUNIO29EQUFLQyxHQUFFO29EQUF3QkgsUUFBTztvREFBVUMsYUFBWTtvREFBTUcsZUFBYztvREFBUUMsZ0JBQWU7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUlwSCw4REFBQ2hFLG9FQUFnQkE7d0NBQ2I4QyxPQUFNO3dDQUNObUIsb0JBQ0ksOERBQUNoQjs0Q0FBSUMsT0FBTTs0Q0FBS0MsUUFBTzs0Q0FBS0MsU0FBUTs0Q0FBWUMsTUFBSzs0Q0FBT0MsT0FBTTtzREFDOUQsNEVBQUNPO2dEQUFLQyxHQUFFO2dEQUEwK0RULE1BQUs7Ozs7Ozs7Ozs7O3dDQUcvL0RmLFNBQVNqQjt3Q0FDVDZDLE1BQUs7d0NBQ0xDLFdBQVc1Qzt3Q0FDWDZDLFNBQVM7NENBQ0w5QyxnQkFBZ0I7NENBQ2hCRSxZQUFZO3dDQUNoQjs7Ozs7O2tEQUdKLDhEQUFDNkM7d0NBQUtDLFVBQVV2Qzt3Q0FBY0gsV0FBVTs7MERBQ3BDLDhEQUFDRDtnREFBSUMsV0FBVTswREFDWCw0RUFBQy9CLHlEQUFLQTtvREFDRjBFLE9BQU07b0RBQ05MLE1BQUs7b0RBQ0xNLElBQUc7b0RBQ0hDLDBCQUNJLDhEQUFDeEI7d0RBQUlDLE9BQU07d0RBQUtDLFFBQU87d0RBQUtDLFNBQVE7d0RBQVlDLE1BQUs7d0RBQU9DLE9BQU07a0VBQzlELDRFQUFDTzs0REFBS0MsR0FBRTs0REFBMmxCVCxNQUFLOzs7Ozs7Ozs7OztvREFHaG5CcUIsT0FBTzNEO29EQUNQNEQsVUFBVSxDQUFDM0MsSUFBTWhCLFNBQVNnQixFQUFFNEMsTUFBTSxDQUFDRixLQUFLO29EQUN4QzlDLFdBQVU7b0RBQ1ZpRCxRQUFROzs7Ozs7Ozs7OzswREFJaEIsOERBQUNsRDtnREFBSUMsV0FBVTswREFDWCw0RUFBQy9CLHlEQUFLQTtvREFDRjBFLE9BQU07b0RBQ05MLE1BQUs7b0RBQ0xNLElBQUc7b0RBQ0hDLDBCQUNJLDhEQUFDeEI7d0RBQUlDLE9BQU07d0RBQUtDLFFBQU87d0RBQUtDLFNBQVE7d0RBQVlDLE1BQUs7d0RBQU9DLE9BQU07a0VBQzlELDRFQUFDTzs0REFBS0MsR0FBRTs0REFBODFCVCxNQUFLOzs7Ozs7Ozs7OztvREFHbjNCcUIsT0FBT3pEO29EQUNQMEQsVUFBVSxDQUFDM0MsSUFBTWQsWUFBWWMsRUFBRTRDLE1BQU0sQ0FBQ0YsS0FBSztvREFDM0M5QyxXQUFVO29EQUNWaUQsUUFBUTs7Ozs7Ozs7Ozs7MERBSWhCLDhEQUFDbEQ7Z0RBQUlDLFdBQVU7O2tFQUNYLDhEQUFDMkM7d0RBQU0zQyxXQUFVOzswRUFDYiw4REFBQ2tEO2dFQUNHWixNQUFLO2dFQUNMYSxTQUFTNUQ7Z0VBQ1R3RCxVQUFVLENBQUMzQyxJQUFNWixjQUFjWSxFQUFFNEMsTUFBTSxDQUFDRyxPQUFPO2dFQUMvQ25ELFdBQVU7Ozs7OzswRUFFZCw4REFBQ29EO2dFQUFLcEQsV0FBVTswRUFBMkM7Ozs7Ozs7Ozs7OztrRUFFL0QsOERBQUNxRDt3REFDR0MsTUFBSzt3REFDTEMsU0FBUzVDO3dEQUNUWCxXQUFVO2tFQUVUcEIsd0NBQ0c7OzhFQUNJLDhEQUFDVCxvRkFBT0E7b0VBQUM2QixXQUFVOzs7Ozs7Z0VBQThCOzsyRUFJckQ7Ozs7Ozs7Ozs7OzswREFLWiw4REFBQ3dEO2dEQUNHbEIsTUFBSztnREFDTHRDLFdBQVU7O29EQUVUdEIsMkJBQWEsOERBQUNQLG9GQUFPQTt3REFBQzZCLFdBQVU7Ozs7OztvREFBMEI7a0VBRTNELDhEQUFDcUI7d0RBQUlDLE9BQU07d0RBQUtDLFFBQU87d0RBQUtDLFNBQVE7d0RBQVlDLE1BQUs7d0RBQU9DLE9BQU07a0VBQzlELDRFQUFDTzs0REFBS0MsR0FBRTs0REFBZzZDVCxNQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FNNzdDLDhEQUFDMUI7MENBQ0csNEVBQUMwRDtvQ0FBR3pELFdBQVU7O3NEQUNWLDhEQUFDMEQ7NENBQUcxRCxXQUFVO3NEQUF1Qiw0RUFBQ3FEO2dEQUFFQyxNQUFLOzBEQUFJOzs7Ozs7Ozs7OztzREFDakQsOERBQUNJOzRDQUFHMUQsV0FBVTtzREFBdUIsNEVBQUNxRDtnREFBRUMsTUFBSzswREFBSTs7Ozs7Ozs7Ozs7c0RBQ2pELDhEQUFDSTs0Q0FBRzFELFdBQVU7c0RBQXVCLDRFQUFDcUQ7Z0RBQUVDLE1BQUs7MERBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPN0U7R0FuTXdCN0U7O1FBRzRDSiwwREFBT0E7UUFDeERDLHNEQUFTQTs7O0tBSkpHIiwic291cmNlcyI6WyJEOlxcUHJvamV0XFxzY2hvbGFyaWZ5XFxkYXNoYm9hcmRcXHNyY1xcYXBwXFwoYXV0aClcXGxvZ2luXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuaW1wb3J0IHsgRm9ybUV2ZW50LCB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCBBcHBMb2dvIGZyb20gXCJAL2NvbXBvbmVudHMvQXBwTG9nb1wiO1xyXG5pbXBvcnQgSW5wdXQgZnJvbSBcIkAvY29tcG9uZW50cy9pbnB1dFwiO1xyXG5pbXBvcnQgRm9ybUhlYWRpbmcgZnJvbSBcIkAvY29tcG9uZW50cy9Gb3JtSGVhZGluZ1wiO1xyXG5pbXBvcnQgeyBMb2FkZXIyIH0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiO1xyXG5pbXBvcnQgTm90aWZpY2F0aW9uQ2FyZCBmcm9tIFwiQC9jb21wb25lbnRzL05vdGlmaWNhdGlvbkNhcmRcIjtcclxuaW1wb3J0ICdAL3N0eWxlcy9mb3JtU3R5bGUuY3NzJztcclxuaW1wb3J0IHVzZUF1dGggZnJvbSBcIkAvYXBwL2hvb2tzL3VzZUF1dGhcIjtcclxuaW1wb3J0IHsgcmVkaXJlY3QsIHVzZVJvdXRlciB9IGZyb20gXCJuZXh0L25hdmlnYXRpb25cIjtcclxuaW1wb3J0IENpcmN1bGFyTG9hZGVyIGZyb20gXCJAL2NvbXBvbmVudHMvd2lkZ2V0cy9DaXJjdWxhckxvYWRlclwiO1xyXG5pbXBvcnQgeyBnZXRDdXJyZW50VXNlciB9IGZyb20gXCJAL2FwcC9zZXJ2aWNlcy9Vc2VyU2VydmljZXNcIjtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExvZ2luKCkge1xyXG4gICAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICAgIGNvbnN0IFtpc0ZvcmdvdFBhc3N3b3JkTG9hZGluZywgc2V0SXNGb3Jnb3RQYXNzd29yZExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gICAgY29uc3QgeyBsb2dpbiwgaXNBdXRoZW50aWNhdGVkLCBsb2FkaW5nLCByZWRpcmVjdEFmdGVyTG9naW4gfSA9IHVzZUF1dGgoKTtcclxuICAgIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xyXG4gICAgLy8gU3RhdGUgcG91ciBsZXMgY2hhbXBzIGRlIGZvcm11bGFpcmVcclxuICAgIGNvbnN0IFtlbWFpbCwgc2V0RW1haWxdID0gdXNlU3RhdGUoXCJcIik7XHJcbiAgICBjb25zdCBbcGFzc3dvcmQsIHNldFBhc3N3b3JkXSA9IHVzZVN0YXRlKFwiXCIpO1xyXG4gICAgY29uc3QgW3JlbWVtYmVyTWUsIHNldFJlbWVtYmVyTWVdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG5cclxuICAgIGNvbnN0IFtlcnJvck1lc3NhZ2UsIHNldEVycm9yTWVzc2FnZV0gPSB1c2VTdGF0ZSgnJyk7XHJcbiAgICBjb25zdCBbaGFzRXJyb3IsIHNldEhhc0Vycm9yXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuXHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIGlmICghbG9hZGluZyAmJiBpc0F1dGhlbnRpY2F0ZWQpIHtcclxuICAgICAgICAgICAgY29uc3QgcmVkaXJlY3RUbyA9IHJlZGlyZWN0QWZ0ZXJMb2dpbiB8fCAnL3N1cGVyLWFkbWluL2Rhc2hib2FyZCc7XHJcbiAgICAgICAgICAgIHJvdXRlci5wdXNoKHJlZGlyZWN0VG8pO1xyXG4gICAgICAgIH1cclxuICAgIH0sIFtpc0F1dGhlbnRpY2F0ZWQsIGxvYWRpbmcsIHJlZGlyZWN0QWZ0ZXJMb2dpbiwgcm91dGVyXSk7XHJcblxyXG4gICAgaWYgKGxvYWRpbmcgfHwgaXNBdXRoZW50aWNhdGVkKSB7XHJcbiAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktY2VudGVyIGl0ZW1zLWNlbnRlciBoLXNjcmVlbiB3LWZ1bGwgYWJzb2x1dGUgdG9wLTAgbGVmdC0wIHotNTBcIj5cclxuICAgICAgICAgICAgICAgIDxDaXJjdWxhckxvYWRlciBzaXplPXs0MH0gY29sb3I9XCJ0ZWFsLTUwMFwiIC8+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICk7XHJcbiAgICB9XHJcbiAgICBjb25zdCBoYW5kbGVTdWJtaXQgPSBhc3luYyAoZTogRm9ybUV2ZW50KSA9PiB7XHJcbiAgICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xyXG4gICAgICAgIHNldElzTG9hZGluZyh0cnVlKTtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBhd2FpdCBsb2dpbihlbWFpbCwgcGFzc3dvcmQsIHJlbWVtYmVyTWUpO1xyXG4gICAgICAgICAgICBzZXRFcnJvck1lc3NhZ2UoJycpO1xyXG4gICAgICAgICAgICBzZXRIYXNFcnJvcihmYWxzZSk7XHJcbiAgICAgICAgICAgIHNldEVtYWlsKCcnKTtcclxuICAgICAgICAgICAgc2V0UGFzc3dvcmQoJycpO1xyXG4gICAgICAgICAgICBzZXRSZW1lbWJlck1lKGZhbHNlKTtcclxuICAgICAgICAgICAgbGV0IHVzZXIgPSBhd2FpdCBnZXRDdXJyZW50VXNlcigpO1xyXG4gICAgICAgICAgICBpZiAodXNlciAmJiB1c2VyLnJvbGUgPT09ICdzdXBlcicpIHtcclxuICAgICAgICAgICAgICAgIHJvdXRlci5wdXNoKCcvc3VwZXItYWRtaW4vZGFzaGJvYXJkJyk7XHJcbiAgICAgICAgICAgIH0gZWxzZSBpZiAodXNlciAmJiB1c2VyLnJvbGUgPT09ICdhZG1pbicpIHtcclxuICAgICAgICAgICAgICAgIHJvdXRlci5wdXNoKCcvc2Nob29sLWFkbWluL2Rhc2hib2FyZCcpO1xyXG4gICAgICAgICAgICB9IGVsc2UgaWYgKHVzZXIgJiYgdXNlci5yb2xlID09PSAndGVhY2hlcicpIHtcclxuICAgICAgICAgICAgICAgIHJvdXRlci5wdXNoKCcvc2Nob29sLWFkbWluL2Rhc2hib2FyZCcpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID1cclxuICAgICAgICAgICAgICAgIGVycm9yIGluc3RhbmNlb2YgRXJyb3JcclxuICAgICAgICAgICAgICAgICAgICA/IGVycm9yLm1lc3NhZ2VcclxuICAgICAgICAgICAgICAgICAgICA6IFwiVGhlIGVtYWlsIG9yIHBhc3N3b3JkIHlvdSBlbnRlcmVkIGRvZXNuJ3QgbWF0Y2ggb3VyIHJlY29yZHMuIFBsZWFzZSBkb3VibGUtY2hlY2sgYW5kIHRyeSBhZ2Fpbi5cIjtcclxuXHJcbiAgICAgICAgICAgIHNldEVycm9yTWVzc2FnZShlcnJvck1lc3NhZ2UpO1xyXG4gICAgICAgICAgICBzZXRIYXNFcnJvcih0cnVlKTtcclxuICAgICAgICB9IGZpbmFsbHkge1xyXG4gICAgICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xyXG4gICAgICAgICAgICBzZXRQYXNzd29yZCgnJyk7XHJcbiAgICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICAvLyBGb25jdGlvbiBwb3VyIGfDqXJlciBsZSBjbGljIHN1ciBsZSBsaWVuIFwiRm9yZ290IFBhc3N3b3JkP1wiXHJcbiAgICBjb25zdCBoYW5kbGVGb3Jnb3RQYXNzd29yZENsaWNrID0gKGU6IFJlYWN0Lk1vdXNlRXZlbnQ8SFRNTEFuY2hvckVsZW1lbnQ+KSA9PiB7XHJcbiAgICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xyXG4gICAgICAgIHNldElzRm9yZ290UGFzc3dvcmRMb2FkaW5nKHRydWUpO1xyXG5cclxuICAgICAgICAvLyBSZWRpcmlnZXIgdmVycyBsYSBwYWdlIGRlIHLDqWN1cMOpcmF0aW9uIGRlIG1vdCBkZSBwYXNzZSBhcHLDqHMgdW4gY291cnQgZMOpbGFpXHJcbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgICAgIHJvdXRlci5wdXNoKCcvZm9yZ290LXBhc3N3b3JkJyk7XHJcbiAgICAgICAgfSwgODAwKTsgLy8gRMOpbGFpIGRlIDgwMG1zIHBvdXIgbW9udHJlciBsJ2FuaW1hdGlvblxyXG4gICAgfTtcclxuXHJcbiAgICByZXR1cm4gPD5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggYmctd2hpdGUgZGFyazpiZy1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGUgIHJvdW5kZWQtbGcgc2hhZG93LWxnIGgtc2NyZWVuIHB4LTRcIj5cclxuICAgICAgICAgICAgey8qIFNlY3Rpb24gZGUgbCdpbWFnZSBsYXTDqXJhbGUgKi99XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYXNpZGVMb2dvIHctWzUwJV0gaC1zY3JlZW4gcHktMlwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhc2lkZUltYWdlIHctZnVsbCBoLWZ1bGxcIj5cclxuICAgICAgICAgICAgICAgICAgICA8aW1nIHNyYz1cIi9hc3NldHMvaW1hZ2VzL2FzaWRlSW1hZ2UyLnBuZ1wiIGNsYXNzTmFtZT1cImgtZnVsbCB3LWZ1bGwgcm91bmRlZC1bMjVweF1cIiBhbHQ9XCJBc2lkZSBJbWFnZVwiIC8+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYXNpZGVGb3JtICBiZy13aGl0ZSBkYXJrOmJnLWdyYXktOTAwIGZsZXggZmxleC1jb2wganVzdGlmeS1ldmVubHkgaXRlbXMtY2VudGVyIG0tYXV0byAgXCI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIiBmbGV4IGZsZXgtY29sIGp1c3RpZnktZXZlbmx5IGl0ZW1zLWNlbnRlciBtLWF1dG8gdy1mdWxsIG1heC13LVs1MDBweF0gIGRhcms6dGV4dC13aGl0ZSBweS02XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPEFwcExvZ28gbG9nb1NyYz1cIi9hc3NldHMvbG9nby5wbmdcIiBsb2dvQWx0PVwiTG9nb1wiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgbXgtYXV0byBwLTZcIiA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxGb3JtSGVhZGluZ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJOaWNlIHRvIHNlZSB5b3UhXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN1YnRpdGxlPVwiU2lnbiBpbiB0byBhY2Nlc3MgeW91ciBEYXNoYm9hcmRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9ybUljb249e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzdmcgd2lkdGg9XCI1MFwiIGhlaWdodD1cIjUwXCIgdmlld0JveD1cIjAgMCA1MCA1MFwiIGZpbGw9XCJub25lXCIgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cmVjdCB4PVwiMS4yNVwiIHk9XCIxLjI1XCIgd2lkdGg9XCI0Ny41XCIgaGVpZ2h0PVwiNDcuNVwiIHJ4PVwiMTMuNzVcIiBzdHJva2U9XCIjMTdCODkwXCIgc3Ryb2tlV2lkdGg9XCIyLjVcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTI3LjE2NyAxNy44MzJIMzAuNTAwM0MzMC45NDI0IDE3LjgzMiAzMS4zNjYzIDE4LjAwNzYgMzEuNjc4OCAxOC4zMjAyQzMxLjk5MTQgMTguNjMyNyAzMi4xNjcgMTkuMDU2NyAzMi4xNjcgMTkuNDk4N1YzMS4xNjU0QzMyLjE2NyAzMS42MDc0IDMxLjk5MTQgMzIuMDMxMyAzMS42Nzg4IDMyLjM0MzlDMzEuMzY2MyAzMi42NTY0IDMwLjk0MjQgMzIuODMyIDMwLjUwMDMgMzIuODMySDI3LjE2N1wiIHN0cm9rZT1cIiMxN0I4OTBcIiBzdHJva2VXaWR0aD1cIjIuNVwiIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTIzIDI5LjQ5NzRMMjcuMTY2NyAyNS4zMzA3TDIzIDIxLjE2NDFcIiBzdHJva2U9XCIjMTdCODkwXCIgc3Ryb2tlV2lkdGg9XCIyLjVcIiBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggZD1cIk0yNy4xNjcgMjUuMzMySDE3LjE2N1wiIHN0cm9rZT1cIiMxN0I4OTBcIiBzdHJva2VXaWR0aD1cIjIuNVwiIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8Tm90aWZpY2F0aW9uQ2FyZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJFcnJvclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpY29uPXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3ZnIHdpZHRoPVwiMjBcIiBoZWlnaHQ9XCIyMFwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIiBmaWxsPVwibm9uZVwiIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggZD1cIk04LjQzNzUgNi41NjI1QzguNDM3NSA2LjMxNTI3IDguNTEwODEgNi4wNzM2IDguNjQ4MTcgNS44NjgwNEM4Ljc4NTUyIDUuNjYyNDggOC45ODA3NCA1LjUwMjI2IDkuMjA5MTUgNS40MDc2NUM5LjQzNzU2IDUuMzEzMDQgOS42ODg4OSA1LjI4ODI5IDkuOTMxMzcgNS4zMzY1MkMxMC4xNzM4IDUuMzg0NzUgMTAuMzk2NiA1LjUwMzggMTAuNTcxNCA1LjY3ODYyQzEwLjc0NjIgNS44NTM0MyAxMC44NjUzIDYuMDc2MTYgMTAuOTEzNSA2LjMxODY0QzEwLjk2MTcgNi41NjExMSAxMC45MzcgNi44MTI0NSAxMC44NDI0IDcuMDQwODVDMTAuNzQ3NyA3LjI2OTI2IDEwLjU4NzUgNy40NjQ0OCAxMC4zODIgNy42MDE4NEMxMC4xNzY0IDcuNzM5MTkgOS45MzQ3MyA3LjgxMjUgOS42ODc1IDcuODEyNUM5LjM1NTk4IDcuODEyNSA5LjAzODA0IDcuNjgwOCA4LjgwMzYyIDcuNDQ2MzhDOC41NjkyIDcuMjExOTYgOC40Mzc1IDYuODk0MDIgOC40Mzc1IDYuNTYyNVpNMTguNDM3NSAxMEMxOC40Mzc1IDExLjY2ODggMTcuOTQyNyAxMy4zMDAxIDE3LjAxNTUgMTQuNjg3NkMxNi4wODg0IDE2LjA3NTIgMTQuNzcwNiAxNy4xNTY2IDEzLjIyODkgMTcuNzk1MkMxMS42ODcxIDE4LjQzMzggOS45OTA2NCAxOC42MDA5IDguMzUzOTMgMTguMjc1NEM2LjcxNzIxIDE3Ljk0OTggNS4yMTM4IDE3LjE0NjIgNC4wMzM3OSAxNS45NjYyQzIuODUzNzggMTQuNzg2MiAyLjA1MDE5IDEzLjI4MjggMS43MjQ2MyAxMS42NDYxQzEuMzk5MDcgMTAuMDA5NCAxLjU2NjE2IDguMzEyODYgMi4yMDQ3NyA2Ljc3MTExQzIuODQzMzggNS4yMjkzNiAzLjkyNDg0IDMuOTExNiA1LjMxMjM4IDIuOTg0NDhDNi42OTk5MiAyLjA1NzM1IDguMzMxMjIgMS41NjI1IDEwIDEuNTYyNUMxMi4yMzcgMS41NjQ5OCAxNC4zODE3IDIuNDU0NzMgMTUuOTYzNSA0LjAzNjUzQzE3LjU0NTMgNS42MTgzMyAxOC40MzUgNy43NjMgMTguNDM3NSAxMFpNMTYuNTYyNSAxMEMxNi41NjI1IDguNzAyMDYgMTYuMTc3NiA3LjQzMzI3IDE1LjQ1NjUgNi4zNTQwN0MxNC43MzU0IDUuMjc0ODcgMTMuNzEwNSA0LjQzMzc0IDEyLjUxMTQgMy45MzcwNEMxMS4zMTIyIDMuNDQwMzQgOS45OTI3MiAzLjMxMDM4IDguNzE5NzIgMy41NjM2QzcuNDQ2NzIgMy44MTY4MSA2LjI3NzQgNC40NDE4MyA1LjM1OTYyIDUuMzU5NjFDNC40NDE4MyA2LjI3NzM5IDMuODE2ODIgNy40NDY3MiAzLjU2MzYgOC43MTk3MkMzLjMxMDM4IDkuOTkyNzIgMy40NDAzNCAxMS4zMTIyIDMuOTM3MDQgMTIuNTExNEM0LjQzMzc0IDEzLjcxMDUgNS4yNzQ4OCAxNC43MzU0IDYuMzU0MDcgMTUuNDU2NUM3LjQzMzI3IDE2LjE3NzYgOC43MDIwNiAxNi41NjI1IDEwIDE2LjU2MjVDMTEuNzM5OSAxNi41NjA2IDEzLjQwOCAxNS44Njg2IDE0LjYzODMgMTQuNjM4M0MxNS44Njg2IDEzLjQwOCAxNi41NjA2IDExLjczOTkgMTYuNTYyNSAxMFpNMTAuOTM3NSAxMi44NjU2VjEwLjMxMjVDMTAuOTM3NSA5Ljg5ODEgMTAuNzcyOSA5LjUwMDY3IDEwLjQ3OTkgOS4yMDc2NEMxMC4xODY4IDguOTE0NjIgOS43ODk0IDguNzUgOS4zNzUgOC43NUM5LjE1MzYgOC43NDk2NyA4LjkzOTIzIDguODI3NzEgOC43Njk4NiA4Ljk3MDI5QzguNjAwNDggOS4xMTI4NyA4LjQ4NzAzIDkuMzEwNzkgOC40NDk2IDkuNTI5MDFDOC40MTIxNyA5Ljc0NzIyIDguNDUzMTggOS45NzE2NCA4LjU2NTM2IDEwLjE2MjVDOC42Nzc1NCAxMC4zNTM0IDguODUzNjUgMTAuNDk4NCA5LjA2MjUgMTAuNTcxOVYxMy4xMjVDOS4wNjI1IDEzLjUzOTQgOS4yMjcxMiAxMy45MzY4IDkuNTIwMTUgMTQuMjI5OUM5LjgxMzE3IDE0LjUyMjkgMTAuMjEwNiAxNC42ODc1IDEwLjYyNSAxNC42ODc1QzEwLjg0NjQgMTQuNjg3OCAxMS4wNjA4IDE0LjYwOTggMTEuMjMwMSAxNC40NjcyQzExLjM5OTUgMTQuMzI0NiAxMS41MTMgMTQuMTI2NyAxMS41NTA0IDEzLjkwODVDMTEuNTg3OCAxMy42OTAzIDExLjU0NjggMTMuNDY1OSAxMS40MzQ2IDEzLjI3NUMxMS4zMjI1IDEzLjA4NDEgMTEuMTQ2NCAxMi45MzkxIDEwLjkzNzUgMTIuODY1NlpcIiBmaWxsPVwiI0Y0M0Y1RVwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlPXtlcnJvck1lc3NhZ2V9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiZXJyb3JcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNWaXNpYmxlPXtoYXNFcnJvcn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xvc2U9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRFcnJvck1lc3NhZ2UoJycpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldEhhc0Vycm9yKGZhbHNlKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlU3VibWl0fSBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJFbWFpbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJlbWFpbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwiZW1haWxcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwcmVmaXhJY29uPXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzdmcgd2lkdGg9XCIyMFwiIGhlaWdodD1cIjIwXCIgdmlld0JveD1cIjAgMCAyMCAyMFwiIGZpbGw9XCJub25lXCIgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9XCJNMTcuNSAzLjQzNzVIMi41QzIuMjUxMzYgMy40Mzc1IDIuMDEyOSAzLjUzNjI3IDEuODM3MDkgMy43MTIwOUMxLjY2MTI3IDMuODg3OSAxLjU2MjUgNC4xMjYzNiAxLjU2MjUgNC4zNzVWMTVDMS41NjI1IDE1LjQxNDQgMS43MjcxMiAxNS44MTE4IDIuMDIwMTUgMTYuMTA0OUMyLjMxMzE3IDE2LjM5NzkgMi43MTA2IDE2LjU2MjUgMy4xMjUgMTYuNTYyNUgxNi44NzVDMTcuMjg5NCAxNi41NjI1IDE3LjY4NjggMTYuMzk3OSAxNy45Nzk5IDE2LjEwNDlDMTguMjcyOSAxNS44MTE4IDE4LjQzNzUgMTUuNDE0NCAxOC40Mzc1IDE1VjQuMzc1QzE4LjQzNzUgNC4xMjYzNiAxOC4zMzg3IDMuODg3OSAxOC4xNjI5IDMuNzEyMDlDMTcuOTg3MSAzLjUzNjI3IDE3Ljc0ODYgMy40Mzc1IDE3LjUgMy40Mzc1Wk0xNS4wODk4IDUuMzEyNUwxMCA5Ljk3ODEzTDQuOTEwMTYgNS4zMTI1SDE1LjA4OThaTTMuNDM3NSAxNC42ODc1VjYuNTA2MjVMOS4zNjY0MSAxMS45NDE0QzkuNTM5MzIgMTIuMSA5Ljc2NTQgMTIuMTg3OSAxMCAxMi4xODc5QzEwLjIzNDYgMTIuMTg3OSAxMC40NjA3IDEyLjEgMTAuNjMzNiAxMS45NDE0TDE2LjU2MjUgNi41MDYyNVYxNC42ODc1SDMuNDM3NVpcIiBmaWxsPVwiIzU3NUQ1RVwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17ZW1haWx9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0RW1haWwoZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcGwtMTAgcHItMyBweS0yIGJvcmRlciByb3VuZGVkLWxnIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbD1cIlBhc3N3b3JkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInBhc3N3b3JkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJwYXNzd29yZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHByZWZpeEljb249e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHN2ZyB3aWR0aD1cIjIwXCIgaGVpZ2h0PVwiMjBcIiB2aWV3Qm94PVwiMCAwIDIwIDIwXCIgZmlsbD1cIm5vbmVcIiB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggZD1cIk0xNi4yNSA1LjkzNzVIMTQuMDYyNVY0LjM3NUMxNC4wNjI1IDMuMjk3NTYgMTMuNjM0NSAyLjI2NDI1IDEyLjg3MjYgMS41MDIzOEMxMi4xMTA4IDAuNzQwNTEyIDExLjA3NzQgMC4zMTI1IDEwIDAuMzEyNUM4LjkyMjU2IDAuMzEyNSA3Ljg4OTI1IDAuNzQwNTEyIDcuMTI3MzggMS41MDIzOEM2LjM2NTUxIDIuMjY0MjUgNS45Mzc1IDMuMjk3NTYgNS45Mzc1IDQuMzc1VjUuOTM3NUgzLjc1QzMuMzM1NiA1LjkzNzUgMi45MzgxNyA2LjEwMjEyIDIuNjQ1MTUgNi4zOTUxNUMyLjM1MjEyIDYuNjg4MTcgMi4xODc1IDcuMDg1NiAyLjE4NzUgNy41VjE2LjI1QzIuMTg3NSAxNi42NjQ0IDIuMzUyMTIgMTcuMDYxOCAyLjY0NTE1IDE3LjM1NDlDMi45MzgxNyAxNy42NDc5IDMuMzM1NiAxNy44MTI1IDMuNzUgMTcuODEyNUgxNi4yNUMxNi42NjQ0IDE3LjgxMjUgMTcuMDYxOCAxNy42NDc5IDE3LjM1NDkgMTcuMzU0OUMxNy42NDc5IDE3LjA2MTggMTcuODEyNSAxNi42NjQ0IDE3LjgxMjUgMTYuMjVWNy41QzE3LjgxMjUgNy4wODU2IDE3LjY0NzkgNi42ODgxNyAxNy4zNTQ5IDYuMzk1MTVDMTcuMDYxOCA2LjEwMjEyIDE2LjY2NDQgNS45Mzc1IDE2LjI1IDUuOTM3NVpNNy44MTI1IDQuMzc1QzcuODEyNSAzLjc5NDg0IDguMDQyOTcgMy4yMzg0NCA4LjQ1MzIgMi44MjgyQzguODYzNDQgMi40MTc5NyA5LjQxOTg0IDIuMTg3NSAxMCAyLjE4NzVDMTAuNTgwMiAyLjE4NzUgMTEuMTM2NiAyLjQxNzk3IDExLjU0NjggMi44MjgyQzExLjk1NyAzLjIzODQ0IDEyLjE4NzUgMy43OTQ4NCAxMi4xODc1IDQuMzc1VjUuOTM3NUg3LjgxMjVWNC4zNzVaTTE1LjkzNzUgMTUuOTM3NUg0LjA2MjVWNy44MTI1SDE1LjkzNzVWMTUuOTM3NVpcIiBmaWxsPVwiIzU3NUQ1RVwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17cGFzc3dvcmR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0UGFzc3dvcmQoZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcGwtMTAgcHItMyBweS0yIGJvcmRlciByb3VuZGVkLWxnIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIG1iLTZcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17cmVtZW1iZXJNZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0UmVtZW1iZXJNZShlLnRhcmdldC5jaGVja2VkKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1yLTJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS04MDAgZGFyazp0ZXh0LWdyYXktMjAwXCI+UmVtZW1iZXIgZm9yIDMwIGRheXM8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPVwiL2ZvcmdvdC1wYXNzd29yZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUZvcmdvdFBhc3N3b3JkQ2xpY2t9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ibHVlLTYwMCBob3Zlcjp1bmRlcmxpbmUgZmxleCBpdGVtcy1jZW50ZXJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2lzRm9yZ290UGFzc3dvcmRMb2FkaW5nID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TG9hZGVyMiBjbGFzc05hbWU9XCJoLTMgdy0zIGFuaW1hdGUtc3BpbiBtci0xXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBSZWRpcmVjdGluZy4uLlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBcIkZvcmdvdCBQYXNzd29yZD9cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwic3VibWl0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgZmxleCBqdXN0aWZ5LWNlbnRlciBnYXAtMiBpdGVtcy1jZW50ZXIgYmctWyMxN0I4OTBdIHRleHQtd2hpdGUgcHktMiByb3VuZGVkLWZ1bGwgaG92ZXI6YmctWyMxN2I4OTBjNF0gdHJhbnNpdGlvbiBkdXJhdGlvbi0yMDBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtpc0xvYWRpbmcgJiYgPExvYWRlcjIgY2xhc3NOYW1lPVwiaC00IHctNCBhbmltYXRlLXNwaW5cIiAvPn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBTaWduIEluXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHN2ZyB3aWR0aD1cIjIxXCIgaGVpZ2h0PVwiMjBcIiB2aWV3Qm94PVwiMCAwIDIxIDIwXCIgZmlsbD1cIm5vbmVcIiB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9XCJNMTAuMTg3NSAxNi44NzVDMTAuMTg3NSAxNy4xMjM2IDEwLjA4ODcgMTcuMzYyMSA5LjkxMjkxIDE3LjUzNzlDOS43MzcxIDE3LjcxMzcgOS40OTg2NCAxNy44MTI1IDkuMjUgMTcuODEyNUg0LjI1QzQuMDAxMzYgMTcuODEyNSAzLjc2MjkgMTcuNzEzNyAzLjU4NzA5IDE3LjUzNzlDMy40MTEyNyAxNy4zNjIxIDMuMzEyNSAxNy4xMjM2IDMuMzEyNSAxNi44NzVWMy4xMjVDMy4zMTI1IDIuODc2MzYgMy40MTEyNyAyLjYzNzkgMy41ODcwOSAyLjQ2MjA5QzMuNzYyOSAyLjI4NjI3IDQuMDAxMzYgMi4xODc1IDQuMjUgMi4xODc1SDkuMjVDOS40OTg2NCAyLjE4NzUgOS43MzcxIDIuMjg2MjcgOS45MTI5MSAyLjQ2MjA5QzEwLjA4ODcgMi42Mzc5IDEwLjE4NzUgMi44NzYzNiAxMC4xODc1IDMuMTI1QzEwLjE4NzUgMy4zNzM2NCAxMC4wODg3IDMuNjEyMSA5LjkxMjkxIDMuNzg3OTFDOS43MzcxIDMuOTYzNzMgOS40OTg2NCA0LjA2MjUgOS4yNSA0LjA2MjVINS4xODc1VjE1LjkzNzVIOS4yNUM5LjQ5ODY0IDE1LjkzNzUgOS43MzcxIDE2LjAzNjMgOS45MTI5MSAxNi4yMTIxQzEwLjA4ODcgMTYuMzg3OSAxMC4xODc1IDE2LjYyNjQgMTAuMTg3NSAxNi44NzVaTTE4LjY2MzMgOS4zMzY3MkwxNS41MzgzIDYuMjExNzJDMTUuMzYyMiA2LjAzNTYgMTUuMTIzMyA1LjkzNjY1IDE0Ljg3NDIgNS45MzY2NUMxNC42MjUxIDUuOTM2NjUgMTQuMzg2MyA2LjAzNTYgMTQuMjEwMiA2LjIxMTcyQzE0LjAzNCA2LjM4Nzg0IDEzLjkzNTEgNi42MjY3MSAxMy45MzUxIDYuODc1NzhDMTMuOTM1MSA3LjEyNDg1IDE0LjAzNCA3LjM2MzcyIDE0LjIxMDIgNy41Mzk4NEwxNS43MzQ0IDkuMDYyNUg5LjI1QzkuMDAxMzYgOS4wNjI1IDguNzYyOSA5LjE2MTI3IDguNTg3MDkgOS4zMzcwOUM4LjQxMTI3IDkuNTEyOSA4LjMxMjUgOS43NTEzNiA4LjMxMjUgMTBDOC4zMTI1IDEwLjI0ODYgOC40MTEyNyAxMC40ODcxIDguNTg3MDkgMTAuNjYyOUM4Ljc2MjkgMTAuODM4NyA5LjAwMTM2IDEwLjkzNzUgOS4yNSAxMC45Mzc1SDE1LjczNDRMMTQuMjA5NCAxMi40NjE3QzE0LjAzMzMgMTIuNjM3OCAxMy45MzQzIDEyLjg3NjcgMTMuOTM0MyAxMy4xMjU4QzEzLjkzNDMgMTMuMzc0OSAxNC4wMzMzIDEzLjYxMzcgMTQuMjA5NCAxMy43ODk4QzE0LjM4NTUgMTMuOTY2IDE0LjYyNDQgMTQuMDY0OSAxNC44NzM0IDE0LjA2NDlDMTUuMTIyNSAxNC4wNjQ5IDE1LjM2MTQgMTMuOTY2IDE1LjUzNzUgMTMuNzg5OEwxOC42NjI1IDEwLjY2NDhDMTguNzQ5OSAxMC41Nzc4IDE4LjgxOTQgMTAuNDc0MyAxOC44NjY3IDEwLjM2MDRDMTguOTE0MSAxMC4yNDY1IDE4LjkzODYgMTAuMTI0MyAxOC45Mzg2IDEwLjAwMDlDMTguOTM4NyA5Ljg3NzU1IDE4LjkxNDQgOS43NTUzNyAxOC44NjcyIDkuNjQxMzhDMTguODE5OSA5LjUyNzQgMTguNzUwNiA5LjQyMzg3IDE4LjY2MzMgOS4zMzY3MlpcIiBmaWxsPVwiI0Y1RjZGQVwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZm9ybT5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBpdGVtcy1jZW50ZXIgZ2FwLTUgcHktNCB0ZXh0LWdyYXktODAwIGRhcms6dGV4dC1ncmF5LTIwMCB0ZXh0LXNtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwiaG92ZXI6dGV4dC1bIzE3Qjg5MF1cIj48YSBocmVmPVwiI1wiPkxpY2VuY2U8L2E+PC9saT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9XCJob3Zlcjp0ZXh0LVsjMTdCODkwXVwiPjxhIGhyZWY9XCIjXCI+VGVybXMgb2YgVXNlPC9hPjwvbGk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwiaG92ZXI6dGV4dC1bIzE3Qjg5MF1cIj48YSBocmVmPVwiI1wiPkJsb2c8L2E+PC9saT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC91bD5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgIDwvPlxyXG59Il0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVN0YXRlIiwiQXBwTG9nbyIsIklucHV0IiwiRm9ybUhlYWRpbmciLCJMb2FkZXIyIiwiTm90aWZpY2F0aW9uQ2FyZCIsInVzZUF1dGgiLCJ1c2VSb3V0ZXIiLCJDaXJjdWxhckxvYWRlciIsImdldEN1cnJlbnRVc2VyIiwiTG9naW4iLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJpc0ZvcmdvdFBhc3N3b3JkTG9hZGluZyIsInNldElzRm9yZ290UGFzc3dvcmRMb2FkaW5nIiwibG9naW4iLCJpc0F1dGhlbnRpY2F0ZWQiLCJsb2FkaW5nIiwicmVkaXJlY3RBZnRlckxvZ2luIiwicm91dGVyIiwiZW1haWwiLCJzZXRFbWFpbCIsInBhc3N3b3JkIiwic2V0UGFzc3dvcmQiLCJyZW1lbWJlck1lIiwic2V0UmVtZW1iZXJNZSIsImVycm9yTWVzc2FnZSIsInNldEVycm9yTWVzc2FnZSIsImhhc0Vycm9yIiwic2V0SGFzRXJyb3IiLCJyZWRpcmVjdFRvIiwicHVzaCIsImRpdiIsImNsYXNzTmFtZSIsInNpemUiLCJjb2xvciIsImhhbmRsZVN1Ym1pdCIsImUiLCJwcmV2ZW50RGVmYXVsdCIsInVzZXIiLCJyb2xlIiwiZXJyb3IiLCJFcnJvciIsIm1lc3NhZ2UiLCJoYW5kbGVGb3Jnb3RQYXNzd29yZENsaWNrIiwic2V0VGltZW91dCIsImltZyIsInNyYyIsImFsdCIsImxvZ29TcmMiLCJsb2dvQWx0IiwidGl0bGUiLCJzdWJ0aXRsZSIsImZvcm1JY29uIiwic3ZnIiwid2lkdGgiLCJoZWlnaHQiLCJ2aWV3Qm94IiwiZmlsbCIsInhtbG5zIiwicmVjdCIsIngiLCJ5IiwicngiLCJzdHJva2UiLCJzdHJva2VXaWR0aCIsInBhdGgiLCJkIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwiaWNvbiIsInR5cGUiLCJpc1Zpc2libGUiLCJvbkNsb3NlIiwiZm9ybSIsIm9uU3VibWl0IiwibGFiZWwiLCJpZCIsInByZWZpeEljb24iLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwidGFyZ2V0IiwicmVxdWlyZWQiLCJpbnB1dCIsImNoZWNrZWQiLCJzcGFuIiwiYSIsImhyZWYiLCJvbkNsaWNrIiwiYnV0dG9uIiwidWwiLCJsaSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(auth)/login/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/services/AuthContext.tsx":
/*!******************************************!*\
  !*** ./src/app/services/AuthContext.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthContext: () => (/* binding */ AuthContext),\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   BASE_API_URL: () => (/* binding */ BASE_API_URL)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! js-cookie */ \"(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _UserServices__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./UserServices */ \"(app-pages-browser)/./src/app/services/UserServices.tsx\");\n/* harmony import */ var _utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/httpInterceptor */ \"(app-pages-browser)/./src/app/utils/httpInterceptor.tsx\");\n/* __next_internal_client_entry_do_not_use__ BASE_API_URL,AuthContext,AuthProvider auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst BASE_API_URL = \"https://scolarify.onrender.com/api\";\n// Create a context for authentication\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)(undefined);\n// composant AuthProvider qui fournit le contexte d'authentification\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [redirectAfterLogin, setRedirectAfterLogin] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [authCheckInterval, setAuthCheckInterval] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Fonction pour forcer la déconnexion\n    const forceLogout = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"AuthProvider.useCallback[forceLogout]\": ()=>{\n            console.warn(\"Force logout triggered\");\n            setUser(null);\n            (0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__.clearSession)();\n            if (authCheckInterval) {\n                clearInterval(authCheckInterval);\n                setAuthCheckInterval(null);\n            }\n            if (true) {\n                window.location.href = '/login';\n            }\n        }\n    }[\"AuthProvider.useCallback[forceLogout]\"], [\n        authCheckInterval\n    ]);\n    // Fonction pour vérifier le statut d'authentification\n    const checkAuthStatus = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"AuthProvider.useCallback[checkAuthStatus]\": async ()=>{\n            try {\n                // Vérifier si le token existe\n                const token = js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(\"idToken\");\n                if (!token) {\n                    forceLogout();\n                    return;\n                }\n                // Vérifier si le token est expiré\n                if ((0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__.isTokenExpired)()) {\n                    console.warn(\"Token expired, logging out\");\n                    forceLogout();\n                    return;\n                }\n                // Vérifier avec le serveur\n                const currentUser = await (0,_UserServices__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)();\n                if (!currentUser) {\n                    forceLogout();\n                    return;\n                }\n                setUser(currentUser);\n            } catch (error) {\n                console.error(\"Auth check failed:\", error);\n                forceLogout();\n            }\n        }\n    }[\"AuthProvider.useCallback[checkAuthStatus]\"], [\n        forceLogout\n    ]);\n    // vérifier si un utilisateur est déja connecté\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const checkUserLoggedIn = {\n                \"AuthProvider.useEffect.checkUserLoggedIn\": async ()=>{\n                    try {\n                        // Vérifier d'abord si le token existe et n'est pas expiré\n                        const token = js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(\"idToken\");\n                        if (!token || (0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__.isTokenExpired)()) {\n                            setUser(null);\n                            setLoading(false);\n                            return;\n                        }\n                        const user = await (0,_UserServices__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)();\n                        if (user) {\n                            setUser(user);\n                            // Démarrer la vérification périodique\n                            const interval = setInterval(checkAuthStatus, 60000); // Vérifier toutes les minutes\n                            setAuthCheckInterval(interval);\n                        } else {\n                            js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"idToken\");\n                            setUser(null);\n                        }\n                    } catch (error) {\n                        console.error(\"Error verifying token:\", error);\n                        js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"idToken\");\n                        setUser(null);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.checkUserLoggedIn\"];\n            checkUserLoggedIn();\n            // Cleanup interval on unmount\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    if (authCheckInterval) {\n                        clearInterval(authCheckInterval);\n                    }\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        checkAuthStatus,\n        authCheckInterval\n    ]);\n    const login = async (email, password, rememberMe, redirectUrl)=>{\n        try {\n            const response = await fetch(\"\".concat(BASE_API_URL, \"/auth/login\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email: email,\n                    password: password\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                console.error(\"Login error:\", data.message || \"Unknown error\");\n                throw new Error(data.message || \"Login failed\");\n            }\n            const { idToken } = data;\n            if (!idToken) {\n                throw new Error(\"No idToken received\");\n            }\n            // Stocker le token dans les cookies\n            js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].set(\"idToken\", idToken, {\n                expires: rememberMe ? 30 : 7\n            }); // Expire dans 7 jours\n            const user = await (0,_UserServices__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)(); // Vérifier si l'utilisateur est connecté à nouveau après la connexion réussie\n            if (user) {\n                setUser(user);\n            }\n            // Si une URL de redirection est fournie, stocke-la\n            if (redirectUrl) {\n                setRedirectAfterLogin(redirectUrl);\n            }\n        } catch (error) {\n            console.error(\"Login failed:\", error);\n            throw error;\n        }\n    };\n    const logout = async ()=>{\n        setUser(null);\n        js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"idToken\"); // Supprimer le token des cookies\n        setRedirectAfterLogin(null); // Réinitialiser l'URL de redirection\n        return Promise.resolve();\n    };\n    const isAuthentiacted = !!user; // Vérifier si l'utilisateur est authentifié\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            isAuthenticated: isAuthentiacted,\n            loading,\n            setRedirectAfterLogin,\n            redirectAfterLogin,\n            login,\n            logout,\n            checkAuthStatus,\n            forceLogout\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\services\\\\AuthContext.tsx\",\n        lineNumber: 184,\n        columnNumber: 9\n    }, undefined);\n};\n_s(AuthProvider, \"IXehwLkee0KKjuVl2ztGE/oHnEQ=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/services/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/services/UserServices.tsx":
/*!*******************************************!*\
  !*** ./src/app/services/UserServices.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   deleteAllUsers: () => (/* binding */ deleteAllUsers),\n/* harmony export */   deleteMultipleUsers: () => (/* binding */ deleteMultipleUsers),\n/* harmony export */   deleteUser: () => (/* binding */ deleteUser),\n/* harmony export */   forget_password: () => (/* binding */ forget_password),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getMonthlyUserStarts: () => (/* binding */ getMonthlyUserStarts),\n/* harmony export */   getTokenFromCookie: () => (/* binding */ getTokenFromCookie),\n/* harmony export */   getTotalUsers: () => (/* binding */ getTotalUsers),\n/* harmony export */   getUserById: () => (/* binding */ getUserById),\n/* harmony export */   getUserBy_id: () => (/* binding */ getUserBy_id),\n/* harmony export */   getUserCountWithChange: () => (/* binding */ getUserCountWithChange),\n/* harmony export */   getUsers: () => (/* binding */ getUsers),\n/* harmony export */   handleUserSearch: () => (/* binding */ handleUserSearch),\n/* harmony export */   registerParent: () => (/* binding */ registerParent),\n/* harmony export */   resend_Code: () => (/* binding */ resend_Code),\n/* harmony export */   reset_password: () => (/* binding */ reset_password),\n/* harmony export */   updateUser: () => (/* binding */ updateUser),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verify_otp: () => (/* binding */ verify_otp)\n/* harmony export */ });\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var jwt_decode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jwt-decode */ \"(app-pages-browser)/./node_modules/jwt-decode/build/esm/index.js\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"(app-pages-browser)/./src/app/services/AuthContext.tsx\");\n/* harmony import */ var _utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/httpInterceptor */ \"(app-pages-browser)/./src/app/utils/httpInterceptor.tsx\");\n\n\n\n\nfunction getTokenFromCookie(name) {\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(name);\n    return token;\n}\nasync function getCurrentUser() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        if (!token) {\n            return null;\n        }\n        const decodedUser = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_1__.jwtDecode)(token);\n        const email = decodedUser.email;\n        const response = await (0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_3__.authenticatedGet)(\"/user/get-user-email/\".concat(email));\n        if (!response.ok) {\n            console.error(\"Error fetching user:\", response.statusText);\n            return null;\n        }\n        const user = await response.json();\n        return user;\n    } catch (error) {\n        console.error(\"Error fetching current user:\", error);\n        return null;\n    }\n}\nasync function getUsers() {\n    try {\n        const token = getTokenFromCookie(\"idToken\"); // Assuming this function gets the token from cookies\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/get-users\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching users:\", response.statusText);\n            throw new Error(\"Failed to fetch users data\");\n        }\n        const usersList = await response.json();\n        const users = usersList.map((user)=>{\n            return {\n                _id: user._id,\n                user_id: user.user_id,\n                firebaseUid: user.firebaseUid,\n                name: user.name,\n                email: user.email,\n                phone: user.phone,\n                role: user.role,\n                avatar: user.avatar,\n                address: user.address,\n                school_ids: user.school_ids,\n                isVerified: user.isVerified,\n                verificationCode: user.verificationCode,\n                verificationCodeExpires: user.verificationCodeExpires,\n                lastLogin: user.lastLogin,\n                createdAt: user.createdAt,\n                updatedAt: user.updatedAt\n            };\n        });\n        return users;\n    } catch (error) {\n        console.error(\"Error fetching users:\", error);\n        throw new Error(\"Failed to fetch users data\");\n    }\n}\nasync function createUser(userData) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/register-user\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n            },\n            body: JSON.stringify(userData)\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to create user data\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                // If parsing the error body fails, use default message\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            console.error(\"Error creating user:\", errorMessage);\n            throw new Error(errorMessage);\n        }\n        const data = await response.json(); // Parse the response as JSON\n        return data; // Return the response data (usually the created user object)\n    } catch (error) {\n        console.error(\"Error creating user:\", error);\n        throw new Error(error instanceof Error ? error.message : \"Failed to create user data\");\n    }\n}\nasync function updateUser(user_id, userData) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/update-user/\").concat(user_id), {\n            method: \"PUT\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n            },\n            body: JSON.stringify(userData)\n        });\n        if (!response.ok) {\n            console.error(\"Error updating user:\", response.statusText);\n            throw new Error(\"Failed to update user data\");\n        }\n        const data = await response.json(); // Parse the response as JSON\n        return data; // Return the updated user data (this could be the user object with updated fields)\n    } catch (error) {\n        console.error(\"Error updating user:\", error);\n        throw new Error(\"Failed to update user data\");\n    }\n}\nasync function getUserById(userId) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/get-user/\").concat(userId), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching user:\", response.statusText);\n        throw new Error(\"Failed to fetch user data\");\n    }\n    const data = await response.json();\n    const user = {\n        _id: data._id,\n        user_id: data.user_id,\n        name: data.name,\n        email: data.email,\n        phone: data.phone,\n        role: data.role,\n        address: data.address,\n        school_ids: data.school_ids,\n        isVerified: data.isVerified,\n        description: data.description,\n        firebaseUid: data.firebaseUid,\n        createdAt: data.createdAt,\n        updatedAt: data.updatedAt\n    };\n    return user;\n}\nasync function verifyPassword(password, email) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/auth/verify-password\"), {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n            email: email,\n            password: password\n        })\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching user:\", response.statusText);\n        return false;\n    }\n    return true;\n}\nasync function deleteUser(user_id) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/delete-user/\").concat(user_id), {\n            method: \"DELETE\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n            }\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to delete user\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            console.error(\"Error deleting user:\", errorMessage);\n            throw new Error(errorMessage);\n        }\n        const result = await response.json();\n        return result; // Might return a success message or deleted user data\n    } catch (error) {\n        console.error(\"Error deleting user:\", error);\n        throw new Error(error instanceof Error ? error.message : \"Failed to delete user\");\n    }\n}\nasync function getUserBy_id(_id) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/get-user-by-id/\").concat(_id), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching user:\", response.statusText);\n        throw new Error(\"Failed to fetch user data\");\n    }\n    const data = await response.json();\n    const user = {\n        _id: data._id,\n        user_id: data.user_id,\n        name: data.name,\n        email: data.email,\n        phone: data.phone,\n        role: data.role,\n        address: data.address,\n        school_ids: data.school_ids,\n        isVerified: data.isVerified,\n        description: data.description,\n        firebaseUid: data.firebaseUid,\n        createdAt: data.createdAt,\n        updatedAt: data.updatedAt\n    };\n    return user;\n}\nasync function forget_password(email) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/auth/forgot-password\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email: email\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to send reset password email: check your email\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error sending reset password email:\", error);\n        throw new Error(\"Failed to send reset password email\");\n    }\n}\nasync function verify_otp(code, email) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/auth/verify-code\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                code: code,\n                email: email\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to verify OTP\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error verifying OTP:\", error);\n        throw new Error(\"Failed to verify OTP\");\n    }\n}\nasync function resend_Code(email) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/auth/resend-code\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email: email\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to resend code\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error resending code:\", error);\n        throw new Error(\"Failed to resend code\");\n    }\n}\nasync function reset_password(newPassword, email, code) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/auth/reset-password\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email: email,\n                code: code,\n                newPassword: newPassword\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to reset password\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error resetting password:\", error);\n        throw new Error(\"Failed to reset password\");\n    }\n}\nasync function handleUserSearch(query) {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/search-users?query=\").concat(encodeURIComponent(query)), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error searching users:\", response.statusText);\n            throw new Error(\"Failed to search users\");\n        }\n        const results = await response.json();\n        const users = results.map((user)=>{\n            return {\n                _id: user._id,\n                user_id: user.user_id,\n                firebaseUid: user.firebaseUid,\n                name: user.name,\n                email: user.email,\n                phone: user.phone,\n                role: user.role,\n                avatar: user.avatar,\n                address: user.address,\n                school_ids: user.school_ids,\n                isVerified: user.isVerified,\n                verificationCode: user.verificationCode,\n                verificationCodeExpires: user.verificationCodeExpires,\n                lastLogin: user.lastLogin,\n                createdAt: user.createdAt,\n                updatedAt: user.updatedAt\n            };\n        });\n        return users;\n    } catch (error) {\n        console.error(\"Error searching users:\", error);\n        throw new Error(\"Failed to search users\");\n    }\n}\nasync function registerParent(parentData) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/register-parent\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n            },\n            body: JSON.stringify(parentData)\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to register parent\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            console.error(\"Error registering parent:\", errorMessage);\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data; // This includes user object and generatedPassword (if new)\n    } catch (error) {\n        console.error(\"Error in registerParent service:\", error);\n        throw new Error(error instanceof Error ? error.message : \"Failed to register parent\");\n    }\n}\nasync function deleteMultipleUsers(userIds) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/delete-users\"), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n        },\n        body: JSON.stringify({\n            ids: userIds\n        })\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting multiple users:\", response.statusText);\n        throw new Error(\"Failed to delete multiple users\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function deleteAllUsers() {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/delete-all-users\"), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting all users:\", response.statusText);\n        throw new Error(\"Failed to delete all users\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function getTotalUsers() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/total-users\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching total users:\", response.statusText);\n            throw new Error(\"Failed to fetch total users\");\n        }\n        const data = await response.json();\n        return data.totalUsers;\n    } catch (error) {\n        console.error(\"Error fetching total users:\", error);\n        throw error;\n    }\n}\nasync function getUserCountWithChange() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/users-count-change\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching user count change:\", response.statusText);\n            throw new Error(\"Failed to fetch user count change\");\n        }\n        const data = await response.json();\n        return {\n            totalUsersThisMonth: data.totalUsersThisMonth,\n            percentageChange: data.percentageChange\n        };\n    } catch (error) {\n        console.error(\"Error fetching user count change:\", error);\n        throw error;\n    }\n}\nasync function getMonthlyUserStarts() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/monthly-user-starts\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Failed to fetch monthly user stats:\", response.statusText);\n            throw new Error(\"Failed to fetch monthly user stats\");\n        }\n        const data = await response.json();\n        // data has type { [year: string]: MonthlyUserStat[] }\n        return data;\n    } catch (error) {\n        console.error(\"Error fetching monthly user stats:\", error);\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/services/UserServices.tsx\n"));

/***/ })

});