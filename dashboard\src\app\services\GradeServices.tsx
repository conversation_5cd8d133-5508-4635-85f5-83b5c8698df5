import { getTokenFromCookie } from "./UserServices";

const BASE_API_URL = process.env.BASE_API_URL || "https://scolarify.onrender.com/api";

export interface GradeRecord {
  _id: string;
  student_name: string;
  student_id: string;
  class_name: string;
  subject_name: string;
  exam_type: string;
  term: string;
  academic_year: string;
  score: number;
  grade: string;
  comments?: string;
  date_entered: string;
}

export interface GradeStats {
  totalGrades: number;
  averageScore: number;
  highestScore: number;
  lowestScore: number;
  passRate: number;
}

export interface GradeFilters {
  class_id?: string;
  subject_id?: string;
  term?: string;
  exam_type_id?: string;
  academic_year?: string;
  student_id?: string;
  page?: number;
  limit?: number;
}

export interface GradeResponse {
  grade_records: GradeRecord[];
  pagination: {
    current_page: number;
    total_pages: number;
    total_records: number;
    per_page: number;
  };
  message: string;
}

// Get grade records for a school with filters
export async function getGradeRecords(schoolId: string, filters: GradeFilters = {}): Promise<GradeResponse> {
  const token = getTokenFromCookie("idToken");

  try {
    // Build query string
    const queryParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const queryString = queryParams.toString();
    const url = `${BASE_API_URL}/grades/school/${schoolId}${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching grade records:", response.statusText);
      throw new Error("Failed to fetch grade records");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Fetch grade records error:", error);
    throw new Error("Failed to fetch grade records");
  }
}

// Get grade statistics for a school
export async function getGradeStats(schoolId: string, filters: Omit<GradeFilters, 'page' | 'limit'> = {}): Promise<{
  stats: GradeStats;
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    // Build query string
    const queryParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const queryString = queryParams.toString();
    const url = `${BASE_API_URL}/grades/school/${schoolId}/stats${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching grade stats:", response.statusText);
      throw new Error("Failed to fetch grade statistics");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Fetch grade stats error:", error);
    throw new Error("Failed to fetch grade statistics");
  }
}

// Create grade record (using legacy route)
export async function createGrade(gradeData: any): Promise<{
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/grades/create-grade`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(gradeData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error creating grade:", errorData);
      throw new Error(errorData.message || "Failed to create grade");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Create grade error:", error);
    throw error;
  }
}

// Update grade record (using legacy route)
export async function updateGrade(gradeId: string, gradeData: any): Promise<{
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/grades/update-grade/${gradeId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(gradeData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error updating grade:", errorData);
      throw new Error(errorData.message || "Failed to update grade");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Update grade error:", error);
    throw error;
  }
}

// Delete grade record (using legacy route)
export async function deleteGrade(gradeId: string): Promise<{
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/grades/delete-grade/${gradeId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error deleting grade:", errorData);
      throw new Error(errorData.message || "Failed to delete grade");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Delete grade error:", error);
    throw error;
  }
}

// Delete multiple grade records (using legacy route)
export async function deleteMultipleGrades(gradeIds: string[]): Promise<{
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/grades/delete-grades`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ ids: gradeIds }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error deleting multiple grades:", errorData);
      throw new Error(errorData.message || "Failed to delete grades");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Delete multiple grades error:", error);
    throw error;
  }
}
