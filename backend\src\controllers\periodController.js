const Period = require('../models/Periods');
const ActivityLog = require('../models/ActivityLog');
const { ensureUniqueId } = require('../utils/generateId');

// Get all periods for a school
const getPeriods = async (req, res) => {
  try {
    const { school_id } = req.params;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    const periods = await Period.find({ school_id })
      .sort({ period_number: 1 })
      .lean();

    res.status(200).json({
      periods,
      message: 'Periods retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching periods:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Create a new period
const createPeriod = async (req, res) => {
  try {
    const { school_id } = req.params;
    const { period_number, start_time, end_time } = req.body;

    if (!school_id || !period_number || !start_time || !end_time) {
      return res.status(400).json({ 
        message: 'School ID, period number, start time, and end time are required' 
      });
    }

    // Check if period number already exists for this school
    const existingPeriod = await Period.findOne({ 
      school_id, 
      period_number 
    });

    if (existingPeriod) {
      return res.status(400).json({ 
        message: `Period ${period_number} already exists for this school` 
      });
    }

    // Validate time format (HH:mm:ss)
    const timeRegex = /^([01]\d|2[0-3]):([0-5]\d):([0-5]\d)$/;
    if (!timeRegex.test(start_time) || !timeRegex.test(end_time)) {
      return res.status(400).json({ 
        message: 'Time must be in HH:mm:ss format' 
      });
    }

    // Validate that start_time is before end_time
    const startDate = new Date(`2000-01-01T${start_time}`);
    const endDate = new Date(`2000-01-01T${end_time}`);
    
    if (startDate >= endDate) {
      return res.status(400).json({ 
        message: 'Start time must be before end time' 
      });
    }

    const newPeriod = new Period({
      school_id,
      period_number,
      start_time,
      end_time
    });

    await newPeriod.save();

    // Log activity
    await ActivityLog.logActivity({
      user_id: req.user.id,
      school_id: school_id,
      action: 'period_created',
      target_type: 'period',
      target_id: newPeriod._id,
      target_name: `Period ${period_number}`,
      details: {
        period_number,
        start_time,
        end_time
      },
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    res.status(201).json({
      period: newPeriod,
      message: 'Period created successfully'
    });
  } catch (error) {
    console.error('Error creating period:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Update a period
const updatePeriod = async (req, res) => {
  try {
    const { school_id, period_id } = req.params;
    const { period_number, start_time, end_time } = req.body;

    if (!school_id || !period_id) {
      return res.status(400).json({ 
        message: 'School ID and Period ID are required' 
      });
    }

    const period = await Period.findOne({ 
      _id: period_id, 
      school_id 
    });

    if (!period) {
      return res.status(404).json({ message: 'Period not found' });
    }

    // If period_number is being changed, check for conflicts
    if (period_number && period_number !== period.period_number) {
      const existingPeriod = await Period.findOne({ 
        school_id, 
        period_number,
        _id: { $ne: period_id }
      });

      if (existingPeriod) {
        return res.status(400).json({ 
          message: `Period ${period_number} already exists for this school` 
        });
      }
    }

    // Validate time format if provided
    const timeRegex = /^([01]\d|2[0-3]):([0-5]\d):([0-5]\d)$/;
    if (start_time && !timeRegex.test(start_time)) {
      return res.status(400).json({ 
        message: 'Start time must be in HH:mm:ss format' 
      });
    }
    if (end_time && !timeRegex.test(end_time)) {
      return res.status(400).json({ 
        message: 'End time must be in HH:mm:ss format' 
      });
    }

    // Validate time order
    const finalStartTime = start_time || period.start_time;
    const finalEndTime = end_time || period.end_time;
    
    const startDate = new Date(`2000-01-01T${finalStartTime}`);
    const endDate = new Date(`2000-01-01T${finalEndTime}`);
    
    if (startDate >= endDate) {
      return res.status(400).json({ 
        message: 'Start time must be before end time' 
      });
    }

    // Update fields
    if (period_number) period.period_number = period_number;
    if (start_time) period.start_time = start_time;
    if (end_time) period.end_time = end_time;

    await period.save();

    // Log activity
    await ActivityLog.logActivity({
      user_id: req.user.id,
      school_id: school_id,
      action: 'period_updated',
      target_type: 'period',
      target_id: period._id,
      target_name: `Period ${period.period_number}`,
      details: {
        updated_fields: Object.keys(req.body),
        period_number: period.period_number,
        start_time: period.start_time,
        end_time: period.end_time
      },
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    res.status(200).json({
      period,
      message: 'Period updated successfully'
    });
  } catch (error) {
    console.error('Error updating period:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Delete a period
const deletePeriod = async (req, res) => {
  try {
    const { school_id, period_id } = req.params;

    if (!school_id || !period_id) {
      return res.status(400).json({ 
        message: 'School ID and Period ID are required' 
      });
    }

    const period = await Period.findOne({ 
      _id: period_id, 
      school_id 
    });

    if (!period) {
      return res.status(404).json({ message: 'Period not found' });
    }

    // TODO: Check if period is being used in any schedules
    // const ClassSchedule = require('../models/ClassSchedule');
    // const schedulesUsingPeriod = await ClassSchedule.find({ period_id });
    // if (schedulesUsingPeriod.length > 0) {
    //   return res.status(400).json({ 
    //     message: 'Cannot delete period that is being used in schedules' 
    //   });
    // }

    await Period.findByIdAndDelete(period_id);

    // Log activity
    await ActivityLog.logActivity({
      user_id: req.user.id,
      school_id: school_id,
      action: 'period_deleted',
      target_type: 'period',
      target_id: period_id,
      target_name: `Period ${period.period_number}`,
      details: {
        period_number: period.period_number,
        start_time: period.start_time,
        end_time: period.end_time
      },
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    res.status(200).json({ message: 'Period deleted successfully' });
  } catch (error) {
    console.error('Error deleting period:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

module.exports = {
  getPeriods,
  createPeriod,
  updatePeriod,
  deletePeriod
};
