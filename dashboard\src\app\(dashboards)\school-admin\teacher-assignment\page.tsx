"use client";

import { UserCheck, Calendar, Users, BookOpen, Filter, Plus, Edit, Trash2 } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import { Suspense, useEffect, useState } from "react";
import DataTableFix from "@/components/utils/TableFix";

import CircularLoader from "@/components/widgets/CircularLoader";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import { motion } from "framer-motion";
import {
  getTimetable,
  createScheduleEntry,
  ScheduleEntry
} from "@/app/services/TimetableServices";
import { getClassesBySchool } from "@/app/services/ClassServices";
import { getSubjectsBySchoolId } from "@/app/services/SubjectServices";
import { getTeachersBySchool } from "@/app/services/TeacherServices";
import { getPeriodsBySchool } from "@/app/services/PeriodServices";
import { verifyPassword } from "@/app/services/UserServices";
import TeacherAssignmentModal from "@/components/modals/TeacherAssignmentModal";
import PasswordConfirmDeleteModal from "@/components/modals/PasswordConfirmDeleteModal";
import { useToast, ToastContainer } from "@/components/ui/Toast";

const navigation = {
  icon: UserCheck,
  baseHref: "/school-admin/teacher-assignment",
  title: "Teacher Assignment"
};

const DAYS = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];

export default function TeacherAssignmentPage() {
  const { logout, user } = useAuth();
  const { toasts, removeToast, showSuccess, showError } = useToast();

  // State management
  const [assignments, setAssignments] = useState<ScheduleEntry[]>([]);
  const [loadingData, setLoadingData] = useState(true);
  const [selectedClass, setSelectedClass] = useState('all');
  const [selectedTeacher, setSelectedTeacher] = useState('all');
  const [selectedDay, setSelectedDay] = useState('all');

  // Modal states
  const [isAssignmentModalOpen, setIsAssignmentModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [assignmentToEdit, setAssignmentToEdit] = useState<ScheduleEntry | null>(null);
  const [assignmentToDelete, setAssignmentToDelete] = useState<ScheduleEntry | null>(null);
  const [deleteType, setDeleteType] = useState<"single" | "multiple">("single");
  const [selectedAssignments, setSelectedAssignments] = useState<ScheduleEntry[]>([]);

  // Additional data for forms
  const [classes, setClasses] = useState<any[]>([]);
  const [subjects, setSubjects] = useState<any[]>([]);
  const [teachers, setTeachers] = useState<any[]>([]);
  const [periods, setPeriods] = useState<any[]>([]);

  // Loading states
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [clearSelection, setClearSelection] = useState(false);

  // Get school ID from user
  const schoolId = user?.school_ids?.[0] || user?.school_id;

  // Fetch assignment data from API
  useEffect(() => {
    const fetchAssignmentData = async () => {
      if (!schoolId) {
        showError("Error", "No school ID found");
        setLoadingData(false);
        return;
      }

      try {
        setLoadingData(true);

        // Build filters
        const filters: any = {};
        if (selectedClass !== 'all') filters.class_id = selectedClass;
        if (selectedTeacher !== 'all') filters.teacher_id = selectedTeacher;
        if (selectedDay !== 'all') filters.day_of_week = selectedDay;

        // Fetch assignments (timetable entries)
        const response = await getTimetable(schoolId as string, filters);
        setAssignments(response.schedule_records);
      } catch (error) {
        console.error("Error fetching assignment data:", error);
        showError("Error", "Failed to load assignment data");
      } finally {
        setLoadingData(false);
      }
    };

    fetchAssignmentData();
  }, [schoolId, selectedClass, selectedTeacher, selectedDay]);

  // Fetch additional data for modals
  useEffect(() => {
    const fetchAdditionalData = async () => {
      if (!schoolId) return;

      try {
        // Fetch data with individual error handling
        const results = await Promise.allSettled([
          getClassesBySchool(schoolId as string),
          getSubjectsBySchoolId(schoolId as string),
          getTeachersBySchool(schoolId as string),
          getPeriodsBySchool(schoolId as string)
        ]);

        // Handle each result individually
        if (results[0].status === 'fulfilled') {
          setClasses(results[0].value);
        } else {
          console.error("Failed to fetch classes:", results[0].reason);
          setClasses([]);
        }

        if (results[1].status === 'fulfilled') {
          setSubjects(results[1].value);
        } else {
          console.error("Failed to fetch subjects:", results[1].reason);
          setSubjects([]);
        }

        if (results[2].status === 'fulfilled') {
          setTeachers(results[2].value);
        } else {
          console.error("Failed to fetch teachers:", results[2].reason);
          setTeachers([]);
        }

        if (results[3].status === 'fulfilled') {
          setPeriods(results[3].value);
        } else {
          console.error("Failed to fetch periods:", results[3].reason);
          setPeriods([]);
        }

        // Show warning if any critical data failed to load
        const anyDataFailed = results.some(result => result.status === 'rejected');
        if (anyDataFailed) {
          showError("Warning", "Some form data could not be loaded. Some features may be limited.");
        }
      } catch (error) {
        console.error("Error fetching additional data:", error);
        showError("Error", "Failed to load form data");
      }
    };

    fetchAdditionalData();
  }, [schoolId]);

  // CRUD Functions
  const handleCreateAssignment = () => {
    setAssignmentToEdit(null);
    setIsAssignmentModalOpen(true);
  };

  const handleEditAssignment = (assignment: ScheduleEntry) => {
    setAssignmentToEdit(assignment);
    setIsAssignmentModalOpen(true);
  };

  const handleDeleteAssignment = (assignment: ScheduleEntry) => {
    setAssignmentToDelete(assignment);
    setDeleteType("single");
    setIsDeleteModalOpen(true);
  };

  const handleDeleteMultiple = () => {
    setDeleteType("multiple");
    setIsDeleteModalOpen(true);
  };

  const handleSelectionChange = (selectedRows: ScheduleEntry[]) => {
    setSelectedAssignments(selectedRows);
  };

  const handleDeleteConfirm = async (password: string) => {
    if (!schoolId || !user) return;

    try {
      // Verify password
      const passwordVerified = await verifyPassword(password, user.email);
      if (!passwordVerified) {
        showError("Error", "Invalid password. Please try again.");
        throw new Error("Invalid password");
      }

      // TODO: Implement actual deletion logic when backend endpoints are available
      // For now, just show success message
      if (deleteType === "single" && assignmentToDelete) {
        // await deleteTeacherAssignment(assignmentToDelete._id);
        showSuccess("Assignment Deleted", "Teacher assignment has been deleted successfully.");
      } else if (deleteType === "multiple") {
        // const selectedIds = selectedAssignments.map(a => a._id);
        // await deleteMultipleTeacherAssignments(selectedIds);
        showSuccess("Assignments Deleted", `${selectedAssignments.length} teacher assignments have been deleted successfully.`);
        setClearSelection(true);
      }

      // Close modal and reset state
      setIsDeleteModalOpen(false);
      setAssignmentToDelete(null);
      setSelectedAssignments([]);

      // TODO: Refresh assignments list when backend endpoints are available
      // const filters: any = {};
      // if (selectedClass !== 'all') filters.class_id = selectedClass;
      // if (selectedTeacher !== 'all') filters.teacher_id = selectedTeacher;
      // if (selectedDay !== 'all') filters.day_of_week = selectedDay;
      // const response = await getTimetable(schoolId as string, filters);
      // setAssignments(response.schedule_records);

    } catch (error: any) {
      console.error("Error deleting assignment(s):", error);
      if (error.message !== "Invalid password") {
        showError("Error", "Failed to delete assignment(s). Please try again.");
      }
      throw error;
    }
  };

  // Modal submission function
  const handleAssignmentSubmit = async (data: any) => {
    if (!schoolId) {
      showError("Error", "No school ID found");
      return;
    }

    setIsSubmitting(true);
    try {
      // Create new assignment
      await createScheduleEntry(schoolId as string, data);

      // Refresh assignments list
      const filters: any = {};
      if (selectedClass !== 'all') filters.class_id = selectedClass;
      if (selectedTeacher !== 'all') filters.teacher_id = selectedTeacher;
      if (selectedDay !== 'all') filters.day_of_week = selectedDay;

      const response = await getTimetable(schoolId as string, filters);
      setAssignments(response.schedule_records);
      setIsAssignmentModalOpen(false);
      setAssignmentToEdit(null);

      // Show success notification
      showSuccess("Assignment Created", "Teacher assignment has been created successfully.");
    } catch (error) {
      console.error("Error submitting assignment:", error);
      showError("Error", "Failed to save assignment. Please try again.");
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  const getDayColor = (day: string) => {
    const colors = {
      Monday: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
      Tuesday: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
      Wednesday: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300',
      Thursday: 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',
      Friday: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300',
      Saturday: 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300',
      Sunday: 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
    };
    return colors[day as keyof typeof colors] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
  };

  // Table columns
  const columns = [
    { 
      header: "Teacher", 
      accessor: (row: ScheduleEntry) => (
        <div>
          <p className="font-medium text-foreground">{row.teacher_name}</p>
        </div>
      )
    },
    { 
      header: "Class", 
      accessor: (row: ScheduleEntry) => (
        <span className="text-sm font-medium">{row.class_name}</span>
      )
    },
    { 
      header: "Subject", 
      accessor: (row: ScheduleEntry) => (
        <span className="text-sm">{row.subject_name}</span>
      )
    },
    { 
      header: "Day", 
      accessor: (row: ScheduleEntry) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDayColor(row.day_of_week)}`}>
          {row.day_of_week}
        </span>
      )
    },
    { 
      header: "Period", 
      accessor: (row: ScheduleEntry) => (
        <div className="text-center">
          <div className="font-semibold">Period {row.period_number}</div>
          <div className="text-xs text-foreground/60">
            {row.start_time.slice(0, 5)} - {row.end_time.slice(0, 5)}
          </div>
        </div>
      )
    },
    { 
      header: "Type", 
      accessor: (row: ScheduleEntry) => (
        <span className="text-sm">{row.schedule_type}</span>
      )
    }
  ];

  // Actions for the table
  const actions = [
    {
      label: "Edit",
      onClick: (assignment: ScheduleEntry) => {
        handleEditAssignment(assignment);
      },
    },
    {
      label: "Delete",
      onClick: (assignment: ScheduleEntry) => {
        handleDeleteAssignment(assignment);
      },
    },
  ];

  // Filter data based on selections
  const filteredAssignments = assignments.filter(assignment => {
    if (selectedClass !== 'all') {
      // Find class name from classes array
      const selectedClassName = classes.find(c => c._id === selectedClass)?.name;
      if (assignment.class_name !== selectedClassName) return false;
    }
    if (selectedTeacher !== 'all') {
      // Find teacher name from teachers array
      const selectedTeacherObj = teachers.find(t => t._id === selectedTeacher);
      const selectedTeacherName = selectedTeacherObj ?
        `${selectedTeacherObj.first_name} ${selectedTeacherObj.last_name}` : '';
      if (assignment.teacher_name !== selectedTeacherName) return false;
    }
    if (selectedDay !== 'all' && assignment.day_of_week !== selectedDay) return false;
    return true;
  });

  if (loadingData) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <CircularLoader size={32} color="teal" />
      </div>
    );
  }

  return (
    <ProtectedRoute allowedRoles={["school_admin", "admin", "super"]}>
      <SchoolLayout navigation={navigation} onLogout={logout}>
        <div className="space-y-6">
          {/* Header */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-teal rounded-full flex items-center justify-center">
                  <UserCheck className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-foreground">Teacher Assignment</h1>
                  <p className="text-foreground/60">
                    Assign teachers to classes for specific periods and subjects
                  </p>
                </div>
              </div>
              
              <div className="text-right">
                <p className="text-2xl font-bold text-foreground">{assignments.length}</p>
                <p className="text-sm text-foreground/60">Total Assignments</p>
              </div>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Total Teachers</p>
                  <p className="text-2xl font-bold text-foreground">{teachers.length}</p>
                </div>
                <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                  <Users className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Total Classes</p>
                  <p className="text-2xl font-bold text-foreground">{classes.length}</p>
                </div>
                <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                  <BookOpen className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Active Assignments</p>
                  <p className="text-2xl font-bold text-foreground">{assignments.length}</p>
                </div>
                <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                  <UserCheck className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Subjects</p>
                  <p className="text-2xl font-bold text-foreground">{subjects.length}</p>
                </div>
                <div className="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center">
                  <BookOpen className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex flex-wrap items-center gap-4">
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-foreground/60" />
                <span className="text-sm font-medium text-foreground">Filters:</span>
              </div>

              <div className="flex items-center space-x-2">
                <label className="text-sm text-foreground/70">Class:</label>
                <select
                  value={selectedClass}
                  onChange={(e) => setSelectedClass(e.target.value)}
                  className="px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground"
                >
                  <option value="all">All Classes</option>
                  {classes.map((classItem) => (
                    <option key={classItem._id} value={classItem._id}>
                      {classItem.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex items-center space-x-2">
                <label className="text-sm text-foreground/70">Teacher:</label>
                <select
                  value={selectedTeacher}
                  onChange={(e) => setSelectedTeacher(e.target.value)}
                  className="px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground"
                >
                  <option value="all">All Teachers</option>
                  {teachers.map((teacher) => (
                    <option key={teacher._id} value={teacher._id}>
                      {teacher.first_name && teacher.last_name ?
                        `${teacher.first_name} ${teacher.last_name}` :
                        teacher.name || 'Unknown Teacher'}
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex items-center space-x-2">
                <label className="text-sm text-foreground/70">Day:</label>
                <select
                  value={selectedDay}
                  onChange={(e) => setSelectedDay(e.target.value)}
                  className="px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground"
                >
                  <option value="all">All Days</option>
                  {DAYS.map((day) => (
                    <option key={day} value={day}>
                      {day}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Assignments Table */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-foreground">
                Teacher Assignments ({filteredAssignments.length})
              </h2>

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleCreateAssignment}
                className="flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600"
              >
                <Plus size={16} />
                <span>New Assignment</span>
              </motion.button>
            </div>

            <Suspense fallback={<CircularLoader size={24} color="teal" />}>
              <DataTableFix<ScheduleEntry>
                data={filteredAssignments}
                columns={columns}
                actions={actions}
                defaultItemsPerPage={15}
                onSelectionChange={handleSelectionChange}
                handleDeleteMultiple={handleDeleteMultiple}
                clearSelection={clearSelection}
                onSelectionCleared={() => setClearSelection(false)}
              />
            </Suspense>
          </div>
        </div>

        {/* Teacher Assignment Modal */}
        <TeacherAssignmentModal
          isOpen={isAssignmentModalOpen}
          onClose={() => {
            setIsAssignmentModalOpen(false);
            setAssignmentToEdit(null);
          }}
          onSubmit={handleAssignmentSubmit}
          assignment={assignmentToEdit}
          classes={classes}
          subjects={subjects}
          teachers={teachers}
          periods={periods}
          loading={isSubmitting}
        />

        {/* Delete Confirmation Modal */}
        <PasswordConfirmDeleteModal
          isOpen={isDeleteModalOpen}
          onClose={() => {
            setIsDeleteModalOpen(false);
            setAssignmentToDelete(null);
          }}
          onConfirm={handleDeleteConfirm}
          title={
            deleteType === "single"
              ? "Delete Teacher Assignment"
              : "Delete Selected Assignments"
          }
          message={
            deleteType === "single"
              ? "Are you sure you want to delete this teacher assignment? This action cannot be undone."
              : `Are you sure you want to delete ${selectedAssignments.length} selected assignments? This action cannot be undone.`
          }
          itemName={
            deleteType === "single" && assignmentToDelete
              ? `${assignmentToDelete.teacher_name} - ${assignmentToDelete.class_name} (${assignmentToDelete.subject_name})`
              : undefined
          }
          itemCount={deleteType === "multiple" ? selectedAssignments.length : undefined}
          type={deleteType}
        />

        {/* Toast Notifications */}
        <ToastContainer toasts={toasts} onClose={removeToast} />
      </SchoolLayout>
    </ProtectedRoute>
  );
}
