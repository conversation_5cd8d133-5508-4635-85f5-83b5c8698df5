import React from 'react';
import DataTableWithBulkActions from '@/components/enhanced/DataTableWithBulkActions';
import { SubscriptionSchema } from '@/app/models/SubscriptionModel';
import { deleteMultipleSubscriptions, deleteAllSubscriptions } from '@/app/services/SubscriptionsServices';

interface SubscriptionsTableWithBulkActionsProps {
  subscriptions: SubscriptionSchema[];
  setSubscriptions: React.Dispatch<React.SetStateAction<SubscriptionSchema[]>>;
  columns: any[];
  actions: any[];
  loading?: boolean;
  onLoadingChange?: (loading: boolean) => void;
  onSelectionChange?: (selection: SubscriptionSchema[]) => void;
  onSuccess?: (message: string) => void;
  onError?: (message: string) => void;
}

const SubscriptionsTableWithBulkActions: React.FC<SubscriptionsTableWithBulkActionsProps> = ({
  subscriptions,
  setSubscriptions,
  columns,
  actions,
  loading = false,
  onLoadingChange,
  onSelectionChange,
  onSuccess,
  onError
}) => {
  return (
    <DataTableWithBulkActions
      columns={columns}
      data={subscriptions}
      actions={actions}
      defaultItemsPerPage={5}
      loading={loading}
      onLoadingChange={onLoadingChange}
      onSelectionChange={onSelectionChange}
      idAccessor="_id"
      enableBulkActions={true}
      deleteMultipleService={deleteMultipleSubscriptions}
      deleteAllService={deleteAllSubscriptions}
      itemName="subscription"
      setItems={setSubscriptions}
      onSuccess={onSuccess}
      onError={onError}
    />
  );
};

export default SubscriptionsTableWithBulkActions;
