import {
    Document,
    Page,
    Text,
    View,
    StyleSheet,
    Font,
    Image,
} from '@react-pdf/renderer';
type StudentSchema = {
    student_id: string;
    name: string;
    date_of_birth?: string;
    place_of_birth?: string;
    class_level: string | { $oid: string } | null;
    guardian_id?: string[];
    gender?: 'Male' | 'Female' | 'Other' | string;
    registered?: boolean;
};

import type { FC } from 'react';

// Register Roboto font
Font.register({
    family: 'Roboto',
    fonts: [
        { src: '/fonts/Roboto-Regular.ttf', fontWeight: 'normal' },
        { src: '/fonts/Roboto-Bold.ttf', fontWeight: 'bold' },
    ],
});

const styles = StyleSheet.create({
    page: {
        padding: 30,
        fontFamily: 'Roboto',
        fontSize: 11,
        backgroundColor: '#F5F6FA',
        color: '#1E3D59',
    },
    header: {
        alignItems: 'center',
        marginBottom: 20,
    },
    logo: {
        width: 60,
        height: 60,
        marginBottom: 6,
    },
    title: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#17B890',
    },
    subtitle: {
        fontSize: 12,
        marginTop: 4,
        color: '#1E3D59',
    },
    tableHeader: {
        flexDirection: 'row',
        backgroundColor: '#E0F7F4',
        borderBottomWidth: 1,
        borderBottomColor: '#0E9B6D',
        paddingVertical: 6,
        fontWeight: 'bold',
    },
    row: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
        paddingVertical: 4,
    },
    cell: { paddingHorizontal: 4 },
    footer: {
        marginTop: 16,
        paddingTop: 8,
        borderTopWidth: 1,
        borderTopColor: '#ccc',
        fontSize: 10,
        color: '#1E3D59',
    },
    poweredByContainer: {
        marginTop: 20,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
    },
    poweredByText: {
        fontSize: 10,
        color: '#999',
        marginRight: 6,
    },
    poweredByLogo: {
        width: 50,
        height: 15,
    },
});

const columns = [
    { title: '#', width: '5%' },

    { title: 'Name', width: '25%' },
    { title: 'G', width: '5%' },
    { title: 'DOB', width: '15%' },
    { title: 'Age', width: '10%' },
    { title: 'ID', width: '30%' },
    //   { title: 'POB', width: '15%' },
    { title: 'Registered', width: '30%' },
];

function calculateAge(dob?: string): number | 'N/A' {
    if (!dob) return 'N/A';
    const date = new Date(dob);
    if (isNaN(date.getTime())) return 'N/A';
    const diff = Date.now() - date.getTime();
    const age = new Date(diff);
    return Math.abs(age.getUTCFullYear() - 1970);
}

interface Props {
    school: { name: string; logoUrl?: string; year: string; };
    className: string;
    students: StudentSchema[];
}

const ClassListPDF: FC<Props> = ({ school, className, students }) => {
    const males = students.filter(s => s.gender === 'Male').length;
    const females = students.filter(s => s.gender === 'Female').length;
    const registered = students.filter(s => s.registered).length;
    const total = students.length;

    const ages = students.map(s => calculateAge(s.date_of_birth)).filter(a => typeof a === 'number') as number[];
    const avgAge = ages.length > 0 ? (ages.reduce((a, b) => a + b, 0) / ages.length).toFixed(1) : 'N/A';

    return (
        <Document>
            <Page size="A4" style={styles.page}>
                <View style={styles.header}>
                    {school.logoUrl && <Image style={styles.logo} src={school.logoUrl} />}
                    <Text style={styles.title}>{school.name}</Text>
                    <Text style={styles.subtitle}>Academic Year - {school.year}</Text>
                    <Text style={styles.subtitle}>Class List For - {className}</Text>
                </View>

                <View style={styles.tableHeader}>
                    {columns.map(col => (
                        <Text key={col.title} style={{ width: col.width, ...styles.cell }}>{col.title}</Text>
                    ))}
                </View>

                {students.map((s, i) => (
                    <View key={s.student_id} style={styles.row}>
                        <Text style={{ width: '5%', ...styles.cell }}>{String(i + 1).padStart(2, '0')}</Text>

                        <Text style={{ width: '25%', ...styles.cell }}>{s.name}</Text>
                        <Text style={{ width: '5%', ...styles.cell }}>{s.gender?.charAt(0) || '-'}</Text>
                        <Text style={{ width: '15%', ...styles.cell }}>{s.date_of_birth ? new Date(s.date_of_birth).toLocaleDateString() : 'N/A'}</Text>
                        <Text style={{ width: '10%', ...styles.cell }}>{calculateAge(s.date_of_birth)}</Text>
                        <Text style={{ width: '30%', ...styles.cell }}>{s.student_id}</Text>
                        {/* <Text style={{ width: '15%', ...styles.cell }}>{s.place_of_birth || 'N/A'}</Text> */}
                        <Text style={{ width: '30%', ...styles.cell }}>{s.registered ? 'Yes' : 'No'}</Text>
                    </View>
                ))}

                <View style={styles.footer}>
                    <Text>Total Students: {total}   Males: {males}   Females: {females}   Avg. Age: {avgAge}</Text>
                    <Text>Registered: {registered}   Not Registered: {total - registered}</Text>
                    <View style={styles.poweredByContainer}>
                        <Text style={styles.poweredByText}>Powered by</Text>
                        <Image style={styles.poweredByLogo} src="/assets/logo.png" />
                    </View>
                </View>
            </Page>
        </Document>
    );
};

export default ClassListPDF;
