"use client";
import { useSearchParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { getClassById, deleteClass, updateClass } from "@/app/services/ClassServices";
import { getClassLevels } from "@/app/services/ClassLevels";
import { getSchoolBy_id } from "@/app/services/SchoolServices";
import { getStudentsByClassAndSchool } from "@/app/services/StudentServices";
import { verifyPassword } from "@/app/services/UserServices";

import { ClassSchema, ClassUpdateSchema } from "@/app/models/ClassModel";
import { ClassLevelSchema } from "@/app/models/ClassLevel";
import { SchoolSchema } from "@/app/models/SchoolModel";
import { StudentSchema } from "@/app/models/StudentModel";
import { UserSchema } from "@/app/models/UserModel";

import Loading from "@/components/widgets/Loading";
import NoData from "@/components/widgets/NoData";
import DataTableFix from "@/components/utils/TableFix";
import UpdateClassModal from "@/app/(dashboards)/super-admin/classes/components/UpdateClassModal";
import DeleteClassModal from "@/app/(dashboards)/super-admin/classes/components/DeleteClassModal";
import NotificationCard, { NotificationType } from "@/components/NotificationCard";
import { calculateAge } from "@/components/utils/CalculateAge";
import { motion } from "framer-motion";

const BASE_URL = "/super-admin";

export default function ClassDetailView({ user }: { user: UserSchema }) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const classId = searchParams.get("classId");
  const schoolId = searchParams.get("schoolId");

  const [classData, setClassData] = useState<ClassSchema | null>(null);
  const [classLevels, setClassLevels] = useState<ClassLevelSchema[]>([]);
  const [school, setSchool] = useState<SchoolSchema | null>(null);
  const [students, setStudents] = useState<StudentSchema[]>([]);
  const [loading, setLoading] = useState(true);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isNotificationCard, setIsNotificationCard] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState("");
  const [notificationType, setNotificationType] = useState<NotificationType>("success");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<"success" | "failure" | null>(null);

  const studentColumns = [
    { header: "Student ID", accessor: (row: StudentSchema) => row.student_id },
    { header: "Full Name", accessor: (row: StudentSchema) => row.name },
    { header: "Age", accessor: (row: StudentSchema) => calculateAge(row.date_of_birth) },
    {
      header: "Gender",
      accessor: (row: StudentSchema) =>
        row.gender ? row.gender.charAt(0).toUpperCase() + row.gender.slice(1) : "N/A",
    },
    {
      header: "Status",
      accessor: (row: StudentSchema) => row.status || "Not specified",
    },
  ];

  const fetchData = async () => {
    if (!classId || !schoolId) {
      setLoading(false);
      return;
    }

    try {
      const [cls, levels, schoolRes] = await Promise.all([
        getClassById(classId),
        getClassLevels(),
        getSchoolBy_id(schoolId),
      ]);

      setClassData(cls);
      setSchool(schoolRes);

      if (cls) {
        const studentsData = await getStudentsByClassAndSchool(cls._id, schoolId);
        setStudents(studentsData);
      }

      setClassLevels(levels?.filter((lvl) => lvl.school_id === schoolId) || []);
    } catch (error) {
      console.error("Error fetching class data:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [classId, schoolId]);

  const handleSave = async (updatedData: ClassUpdateSchema) => {
    if (!user) return;

    setIsSubmitting(true);
    setSubmitStatus(null);
    try {
      const data = await updateClass(updatedData._id, updatedData);
      if (data) {
        setClassData(data);
        setSubmitStatus("success");
        setNotificationMessage("Class updated successfully!");
        setNotificationType("success");
        setIsNotificationCard(true);
        setTimeout(() => {
          setIsEditModalOpen(false);
          setSubmitStatus(null);
        }, 5000);
      }
    } catch (error) {
      const msg = error instanceof Error ? error.message : "Error updating class.";
      setSubmitStatus("failure");
      setNotificationMessage(msg);
      setNotificationType("error");
      setIsNotificationCard(true);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async (password: string) => {
    if (!user || !classData) return;

    setIsSubmitting(true);
    const verified = await verifyPassword(password, user.email);
    if (!verified) {
      setNotificationMessage("Invalid Password!");
      setNotificationType("error");
      setIsNotificationCard(true);
      setIsSubmitting(false);
      setSubmitStatus("failure");
      return;
    }

    try {
      await deleteClass(classData._id);
      fetchData();
      setSubmitStatus("success");
      setNotificationMessage("Class deleted successfully!");
      setNotificationType("success");
      setIsNotificationCard(true);
      setTimeout(() => {
        setIsDeleteModalOpen(false);
        router.push(`${BASE_URL}/classes`);
      }, 5000);
    } catch (error) {
      const msg = error instanceof Error ? error.message : "Error deleting class.";
      setSubmitStatus("failure");
      setNotificationMessage(msg);
      setNotificationType("error");
      setIsNotificationCard(true);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) return <Loading />;
  if (!classData) return <NoData />;

  return (
    <div className="md:py-6">
      {isNotificationCard && (
        <NotificationCard
          title="Notification"
          message={notificationMessage}
          onClose={() => setIsNotificationCard(false)}
          type={notificationType}
          isVisible={isNotificationCard}
          isFixed={true}
        />
      )}

      {isEditModalOpen && (
        <UpdateClassModal
          onClose={() => {
            setIsEditModalOpen(false);
            setSubmitStatus(null);
          }}
          onSave={handleSave}
          initialData={classData}
          classLevels={classLevels}
          submitStatus={submitStatus}
          isSubmitting={isSubmitting}
        />
      )}

      {isDeleteModalOpen && (
        <DeleteClassModal
          className={classData.name}
          onClose={() => {
            setIsDeleteModalOpen(false);
            setSubmitStatus(null);
            router.push(`${BASE_URL}/classes`);
          }}
          onDelete={handleDelete}
          submitStatus={submitStatus}
          isSubmitting={isSubmitting}
        />
      )}

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-foreground mb-4">
          {classData.name} - <span className="opacity-60">{school?.name}</span>
        </h1>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
          <Info label="Class Name" value={classData.name} />
          <Info label="Class Code" value={classData.class_code} />
          <Info
            label="Class Level"
            value={classLevels.find((lvl) => lvl._id === classData.class_level)?.name || "Unknown"}
          />
          <Info label="School" value={school?.name || "N/A"} />
        </div>

        <div className="flex justify-end space-x-2">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setIsEditModalOpen(true)}
            className="px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-700"
          >
            Edit Class
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setIsDeleteModalOpen(true)}
            className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
          >
            Delete Class
          </motion.button>
        </div>
      </div>

      <div className="dark:bg-gray-800 mt-4">
        <h2 className="text-2xl font-bold text-foreground mb-4 p-6">
          Students of {classData.name} - <span className="opacity-60">{school?.name}</span>
        </h2>
        <DataTableFix data={students} columns={studentColumns} hasSearch showCheckbox={false} />
      </div>
    </div>
  );
}

function Info({ label, value }: { label: string; value: string }) {
  return (
    <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded-md">
      <p className="text-sm font-semibold text-gray-600 dark:text-gray-300">{label}</p>
      <p className="text-sm text-foreground">{value}</p>
    </div>
  );
}
