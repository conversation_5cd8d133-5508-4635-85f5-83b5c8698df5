"use client";

import React, { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { getInvitations, resendInvitationToken, updateInvitation } from "@/app/services/InvitationServices";
import { getSchools } from "@/app/services/SchoolServices";
import { getStudents } from "@/app/services/StudentServices";
import { InvitationSchema, InvitationUpdateSchema } from "@/app/models/Invitation";
import { SchoolSchema } from "@/app/models/SchoolModel";
import { StudentSchema } from "@/app/models/StudentModel";
import CircularLoader from "@/components/widgets/CircularLoader";
import SuperLayout from "@/components/Dashboard/Layouts/SuperLayout";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import NotificationCard from "@/components/NotificationCard";
import UpdateInvitationModal from "../components/UpdateInviteModal";
import { motion } from "framer-motion";
import { getParents, resetParentPasswordService, updateUser } from "@/app/services/UserServices";
import { UserSchema, UserUpdateSchema } from "@/app/models/UserModel";


export default function ViewParentPage() {
    const [parent, setParent] = useState<UserSchema | null>(null);
    const [schools, setSchools] = useState<SchoolSchema[]>([]);
    const [students, setStudents] = useState<StudentSchema[]>([]);
    const [loading, setLoading] = useState(true);
    const [loadingData, setLoadingData] = useState(false);
    const [isNotificationCard, setIsNotificationCard] = useState(false);
    const [notificationMessage, setNotificationMessage] = useState("");
    const [notificationType, setNotificationType] = useState<"success" | "error" | "info" | "warning">("success");
    const searchParams = useSearchParams();
    const userId = searchParams.get("id");
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitStatus, setSubmitStatus] = useState<"success" | "failure" | null>(null);
    const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false);

    const fetchData = async () => {
        setLoading(true);
        try {
            const [parents, schoolData, studentData] = await Promise.all([
                getParents(),
                getSchools(),
                getStudents(),
            ]);

            const found = parents.find((user: { _id: string | null; }) => user._id === userId);
            if (found) setParent(found);
            setSchools(schoolData);
            setStudents(studentData);
        } catch (error) {
            console.error("Error loading Parent data", error);
        } finally {
            setLoading(false);
        }
    }
    useEffect(() => {

        if (userId) fetchData();
    }, [userId]);

    const getSchoolNames = (ids?: string[]) =>
        ids?.map((id) => schools.find((s) => s._id === id)?.name || "Unknown").join(", ") || "No Schools";

    const getStudentNames = (ids?: string[]) =>
        ids?.map((id) => students.find((s) => s._id === id)?.name || "Unknown").join(", ") || "No Children";

    const handlePasswordReset = async () => {
        const contact = parent?.email
            ? { email: parent.email }
            : parent?.phone
                ? { phone: parent.phone }
                : null;
        console.log("Contact for reset:", contact);
        if (!contact) return;
        setLoadingData(true);
        try {
            const res = await resetParentPasswordService(contact);
            setIsNotificationCard(true);
            setNotificationMessage("Password reset link has been sent.");
            setNotificationType("success");
        } catch (error) {
            const message = error instanceof Error ? error.message : "Failed to send reset link.";
            setIsNotificationCard(true);
            setNotificationMessage(message);
            setNotificationType("error");
        } finally {
            setLoadingData(false);
        }
    };

    const handleUpdate = async (invitationData: UserSchema) => {
        if (parent && userId) {
            setIsSubmitting(true);         // Start submitting
            setSubmitStatus(null);
            setLoadingData(true);
            try {
                const updatedInvitation: UserUpdateSchema = {
                    _id: invitationData._id,
                    user_id: invitationData.user_id,
                    student_ids: invitationData.childrenIds,
                    school_ids: invitationData.school_ids,
                    name: invitationData.name,
                    createdAt: invitationData.createdAt,
                }
                const data = await updateUser(invitationData.user_id, updatedInvitation);
                console.log("Updated Parent Data:", data);

                if (data) {
                    setSubmitStatus("success");
                    fetchData();
                    setIsNotificationCard(true);
                    setNotificationMessage("Parent updated successfully.");
                }
                // optional: close modal after delay
                // setTimeout(() => {
                //     setIsUpdateModalOpen(false);
                //     setSubmitStatus(null); // reset
                // }, 5000); 

            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : "Error updating Parent:";
                setSubmitStatus("failure");                  // ✅ update failure
                setNotificationMessage(errorMessage);
                setIsNotificationCard(true);
                setNotificationType("error");
            } finally {
                setIsSubmitting(false);                     // ✅ end submitting
                setLoadingData(false);
            }
        }
    }
    return (
        <SuperLayout
            navigation={{ icon: ArrowLeft, title: "Parent Details", baseHref: "/super-admin/parents" }}
            showGoPro={true}
            onLogout={() => console.log("Logout")}
        >
            {isNotificationCard && (
                <NotificationCard
                    title="Notification"
                    icon={
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z" stroke="#15803d" strokeWidth="1.5" />
                            <path d="M7.75 11.9999L10.58 14.8299L16.25 9.16992" stroke="#15803d" strokeWidth="1.5" />
                        </svg>
                    }
                    message={notificationMessage}
                    onClose={() => setIsNotificationCard(false)}
                    type={notificationType}
                    isVisible={isNotificationCard}
                    isFixed={true}
                />
            )}
            {parent && isUpdateModalOpen && (
                <UpdateInvitationModal
                    onClose={() => { setIsUpdateModalOpen(false); setSubmitStatus(null); }}
                    initialData={parent}
                    onSave={handleUpdate}
                    isSubmitting={isSubmitting}
                    submitStatus={submitStatus}
                />
            )}

            {/* Loading State */}
            {loading ? (
                <div className="fixed inset-0 flex items-center justify-center z-[100] bg-black/90">
                    <CircularLoader size={32} color="teal" />
                </div>
            ) : !parent ? (
                <div className="text-center text-red-600 font-semibold">Parent not found.</div>
            ) : (
                <div className="md:p-6">
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                        {/* Title */}
                        <h1 className="text-2xl font-bold text-foreground mb-4">
                            Parent Details
                        </h1>

                        {/* Parent Information Grid */}
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                            {/* Name */}
                            <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded-md">
                                <p className="text-sm font-semibold text-gray-600 dark:text-gray-300">Name</p>
                                <p className="text-sm text-foreground">{parent.name || "N/A"}</p>
                            </div>

                            {/* Email */}
                            <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded-md">
                                <p className="text-sm font-semibold text-gray-600 dark:text-gray-300">Email</p>
                                <p className="text-sm text-foreground">{parent.email}</p>
                            </div>

                            {/* Phone */}
                            <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded-md">
                                <p className="text-sm font-semibold text-gray-600 dark:text-gray-300">Phone</p>
                                <p className="text-sm text-foreground">{parent.phone || "N/A"}</p>
                            </div>

                            {/* Schools */}
                            <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded-md">
                                <p className="text-sm font-semibold text-gray-600 dark:text-gray-300">Schools</p>
                                <p className="text-sm text-foreground">{getSchoolNames(parent.school_ids)}</p>
                            </div>

                            {/* Children */}
                            <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded-md">
                                <p className="text-sm font-semibold text-gray-600 dark:text-gray-300">Children</p>
                                <p className="text-sm text-foreground">{getStudentNames(parent.student_ids)}</p>
                            </div>

                            {/* Invited At */}
                            <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded-md">
                                <p className="text-sm font-semibold text-gray-600 dark:text-gray-300">Invited At</p>
                                <p className="text-sm text-foreground">{parent.createdAt ? new Date(parent.createdAt).toLocaleString() : "N/A"}</p>
                            </div>

                        </div>

                        {/* Action Buttons */}
                        <div className="flex justify-end space-x-2">
                            {/* Back to Invitations Button */}
                            <Link
                                href="/super-admin/parents"
                                className="px-4 py-2 border border-gray-500 text-foreground rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
                            >
                                Back to Parents
                            </Link>
                            <motion.button
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                transition={{ type: 'spring', stiffness: 300 }}
                                onClick={() => setIsUpdateModalOpen(true)}
                                className="px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 flex items-center gap-2"
                            >
                                Edit Parent
                            </motion.button>
                            <motion.button
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                transition={{ type: 'spring', stiffness: 300 }}
                                onClick={handlePasswordReset}
                                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 flex items-center gap-2"
                                disabled={loadingData}
                            >
                                {loadingData ? "Sending..." : "Reset Password"}
                            </motion.button>
                        </div>

                    </div>
                </div>
            )}
        </SuperLayout>
    );
}
