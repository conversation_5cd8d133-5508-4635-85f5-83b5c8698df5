"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/school/page",{

/***/ "(app-pages-browser)/./src/app/(dashboards)/school-admin/school/page.tsx":
/*!***********************************************************!*\
  !*** ./src/app/(dashboards)/school-admin/school/page.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Page)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_School_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=School!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/school.js\");\n/* harmony import */ var _components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Dashboard/Layouts/SchoolLayout */ \"(app-pages-browser)/./src/components/Dashboard/Layouts/SchoolLayout.tsx\");\n/* harmony import */ var _components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/widgets/CircularLoader */ \"(app-pages-browser)/./src/components/widgets/CircularLoader.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var _app_services_SchoolServices__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/services/SchoolServices */ \"(app-pages-browser)/./src/app/services/SchoolServices.tsx\");\n/* harmony import */ var _components_NotificationCard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/NotificationCard */ \"(app-pages-browser)/./src/components/NotificationCard.tsx\");\n/* harmony import */ var _app_types_notification__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/types/notification */ \"(app-pages-browser)/./src/app/types/notification.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst BASE_URL = \"/school-admin\";\nconst navigation = {\n    icon: _barrel_optimize_names_School_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    baseHref: \"\".concat(BASE_URL, \"/school\"),\n    title: \"School\"\n};\nfunction SchoolContent() {\n    _s();\n    const [school, setSchool] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [submitStatus, setSubmitStatus] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const { user } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    // Charger les données de l'école au chargement de la page\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"SchoolContent.useEffect\": ()=>{\n            const fetchSchool = {\n                \"SchoolContent.useEffect.fetchSchool\": async ()=>{\n                    try {\n                        setLoading(true);\n                        // Si l'utilisateur est connecté et a un ID d'école associé\n                        if (user && user.school_ids && user.school_ids.length > 0) {\n                            const schoolId = user.school_ids[0]; // Utiliser le premier ID d'école\n                            const schoolData = await (0,_app_services_SchoolServices__WEBPACK_IMPORTED_MODULE_5__.getSchoolById)(schoolId);\n                            setSchool(schoolData);\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching school:\", error);\n                        setSubmitStatus((0,_app_types_notification__WEBPACK_IMPORTED_MODULE_7__.createErrorNotification)(\"Failed to fetch school data\"));\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"SchoolContent.useEffect.fetchSchool\"];\n            fetchSchool();\n        }\n    }[\"SchoolContent.useEffect\"], [\n        user\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                size: 32,\n                color: \"teal\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                lineNumber: 52,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this);\n    }\n    if (!school) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"School Information\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-100 p-4 rounded-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-700\",\n                        children: \"No school information found. Please contact support.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold mb-4\",\n                children: \"School Information\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n            submitStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NotificationCard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    type: submitStatus.type,\n                    title: submitStatus.title,\n                    message: submitStatus.message,\n                    onClose: ()=>setSubmitStatus(null),\n                    isVisible: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                lineNumber: 73,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: school.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"ID:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" \",\n                                            school.school_id\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Email:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" \",\n                                            school.email || \"Not specified\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Phone:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" \",\n                                            school.phone_number || \"Not specified\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Website:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" \",\n                                            school.website || \"Not specified\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Principal:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" \",\n                                            school.principal_name\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Established:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" \",\n                                            school.established_year\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Address:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" \",\n                                            school.address || \"Not specified\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium mb-2\",\n                                children: \"Description\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: school.description || \"No description available.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, this);\n}\n_s(SchoolContent, \"G6TSoH+kwGVJQaK0OifZ9IW/B8s=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    ];\n});\n_c = SchoolContent;\nfunction Page() {\n    _s1();\n    const { logout } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center h-screen absolute top-0 left-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    size: 32,\n                    color: \"teal\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                lineNumber: 129,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n            lineNumber: 128,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            navigation: navigation,\n            showGoPro: true,\n            onLogout: ()=>logout(),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SchoolContent, {}, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n            lineNumber: 134,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\school\\\\page.tsx\",\n        lineNumber: 127,\n        columnNumber: 5\n    }, this);\n}\n_s1(Page, \"JN45pPWTCN0QEZFRvGkjOxroHA0=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    ];\n});\n_c1 = Page;\nvar _c, _c1;\n$RefreshReg$(_c, \"SchoolContent\");\n$RefreshReg$(_c1, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboards)/school-admin/school/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/services/AuthContext.tsx":
/*!******************************************!*\
  !*** ./src/app/services/AuthContext.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthContext: () => (/* binding */ AuthContext),\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   BASE_API_URL: () => (/* binding */ BASE_API_URL)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! js-cookie */ \"(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _UserServices__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./UserServices */ \"(app-pages-browser)/./src/app/services/UserServices.tsx\");\n/* harmony import */ var _utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/httpInterceptor */ \"(app-pages-browser)/./src/app/utils/httpInterceptor.tsx\");\n/* __next_internal_client_entry_do_not_use__ BASE_API_URL,AuthContext,AuthProvider auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst BASE_API_URL = \"https://scolarify.onrender.com/api\";\n// Create a context for authentication\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)(undefined);\n// composant AuthProvider qui fournit le contexte d'authentification\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [redirectAfterLogin, setRedirectAfterLogin] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [authCheckInterval, setAuthCheckInterval] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Fonction pour forcer la déconnexion\n    const forceLogout = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"AuthProvider.useCallback[forceLogout]\": ()=>{\n            console.warn(\"Force logout triggered\");\n            setUser(null);\n            (0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__.clearSession)();\n            if (authCheckInterval) {\n                clearInterval(authCheckInterval);\n                setAuthCheckInterval(null);\n            }\n            if (true) {\n                window.location.href = '/login';\n            }\n        }\n    }[\"AuthProvider.useCallback[forceLogout]\"], [\n        authCheckInterval\n    ]);\n    // Fonction pour vérifier le statut d'authentification\n    const checkAuthStatus = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"AuthProvider.useCallback[checkAuthStatus]\": async ()=>{\n            try {\n                // Vérifier si le token existe\n                const token = js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(\"idToken\");\n                if (!token) {\n                    forceLogout();\n                    return;\n                }\n                // Vérifier si le token est expiré\n                if ((0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__.isTokenExpired)()) {\n                    console.warn(\"Token expired, logging out\");\n                    forceLogout();\n                    return;\n                }\n                // Vérifier avec le serveur\n                const currentUser = await (0,_UserServices__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)();\n                if (!currentUser) {\n                    forceLogout();\n                    return;\n                }\n                setUser(currentUser);\n            } catch (error) {\n                console.error(\"Auth check failed:\", error);\n                forceLogout();\n            }\n        }\n    }[\"AuthProvider.useCallback[checkAuthStatus]\"], [\n        forceLogout\n    ]);\n    // vérifier si un utilisateur est déja connecté\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const checkUserLoggedIn = {\n                \"AuthProvider.useEffect.checkUserLoggedIn\": async ()=>{\n                    try {\n                        // Vérifier d'abord si le token existe et n'est pas expiré\n                        const token = js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(\"idToken\");\n                        if (!token || (0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__.isTokenExpired)()) {\n                            setUser(null);\n                            setLoading(false);\n                            return;\n                        }\n                        const user = await (0,_UserServices__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)();\n                        if (user) {\n                            setUser(user);\n                            // Démarrer la vérification périodique\n                            const interval = setInterval(checkAuthStatus, 60000); // Vérifier toutes les minutes\n                            setAuthCheckInterval(interval);\n                        } else {\n                            js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"idToken\");\n                            setUser(null);\n                        }\n                    } catch (error) {\n                        console.error(\"Error verifying token:\", error);\n                        js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"idToken\");\n                        setUser(null);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.checkUserLoggedIn\"];\n            checkUserLoggedIn();\n            // Cleanup interval on unmount\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    if (authCheckInterval) {\n                        clearInterval(authCheckInterval);\n                    }\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        checkAuthStatus,\n        authCheckInterval\n    ]);\n    const login = async (email, password, rememberMe, redirectUrl)=>{\n        try {\n            const response = await fetch(\"\".concat(BASE_API_URL, \"/auth/login\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email: email,\n                    password: password\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                console.error(\"Login error:\", data.message || \"Unknown error\");\n                throw new Error(data.message || \"Login failed\");\n            }\n            const { idToken } = data;\n            if (!idToken) {\n                throw new Error(\"No idToken received\");\n            }\n            // Stocker le token dans les cookies\n            js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].set(\"idToken\", idToken, {\n                expires: rememberMe ? 30 : 7\n            }); // Expire dans 7 jours\n            const user = await (0,_UserServices__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)(); // Vérifier si l'utilisateur est connecté à nouveau après la connexion réussie\n            if (user) {\n                setUser(user);\n            }\n            // Si une URL de redirection est fournie, stocke-la\n            if (redirectUrl) {\n                setRedirectAfterLogin(redirectUrl);\n            }\n        } catch (error) {\n            console.error(\"Login failed:\", error);\n            throw error;\n        }\n    };\n    const logout = async ()=>{\n        setUser(null);\n        js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"idToken\"); // Supprimer le token des cookies\n        setRedirectAfterLogin(null); // Réinitialiser l'URL de redirection\n        return Promise.resolve();\n    };\n    const isAuthentiacted = !!user; // Vérifier si l'utilisateur est authentifié\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            isAuthenticated: isAuthentiacted,\n            loading,\n            setRedirectAfterLogin,\n            redirectAfterLogin,\n            login,\n            logout,\n            checkAuthStatus,\n            forceLogout\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\services\\\\AuthContext.tsx\",\n        lineNumber: 184,\n        columnNumber: 9\n    }, undefined);\n};\n_s(AuthProvider, \"IXehwLkee0KKjuVl2ztGE/oHnEQ=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvc2VydmljZXMvQXV0aENvbnRleHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQ2dDO0FBQ3dDO0FBRXhCO0FBRXdCO0FBR2pFLE1BQU1RLGVBQWUscUNBQXFDO0FBa0JqRSxzQ0FBc0M7QUFDL0IsTUFBTUMsNEJBQWNSLG9EQUFhQSxDQUE4QlMsV0FBVztBQU9qRixvRUFBb0U7QUFFN0QsTUFBTUMsZUFBNEM7UUFBQyxFQUFFQyxRQUFRLEVBQUU7O0lBQ2xFLE1BQU0sQ0FBQ0MsTUFBTUMsUUFBUSxHQUFHWCwrQ0FBUUEsQ0FBb0I7SUFDcEQsTUFBTSxDQUFDWSxTQUFTQyxXQUFXLEdBQUdiLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ2Msb0JBQW9CQyxzQkFBc0IsR0FBR2YsK0NBQVFBLENBQWdCO0lBQzVFLE1BQU0sQ0FBQ2dCLG1CQUFtQkMscUJBQXFCLEdBQUdqQiwrQ0FBUUEsQ0FBd0I7SUFFbEYsc0NBQXNDO0lBQ3RDLE1BQU1rQixjQUFjakIsa0RBQVdBO2lEQUFDO1lBQzVCa0IsUUFBUUMsSUFBSSxDQUFDO1lBQ2JULFFBQVE7WUFDUlAsb0VBQVlBO1lBQ1osSUFBSVksbUJBQW1CO2dCQUNuQkssY0FBY0w7Z0JBQ2RDLHFCQUFxQjtZQUN6QjtZQUNBLElBQUksSUFBNkIsRUFBRTtnQkFDL0JLLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHO1lBQzNCO1FBQ0o7Z0RBQUc7UUFBQ1I7S0FBa0I7SUFFdEIsc0RBQXNEO0lBQ3RELE1BQU1TLGtCQUFrQnhCLGtEQUFXQTtxREFBQztZQUNoQyxJQUFJO2dCQUNBLDhCQUE4QjtnQkFDOUIsTUFBTXlCLFFBQVE3QixpREFBT0EsQ0FBQzhCLEdBQUcsQ0FBQztnQkFDMUIsSUFBSSxDQUFDRCxPQUFPO29CQUNSUjtvQkFDQTtnQkFDSjtnQkFFQSxrQ0FBa0M7Z0JBQ2xDLElBQUlmLHNFQUFjQSxJQUFJO29CQUNsQmdCLFFBQVFDLElBQUksQ0FBQztvQkFDYkY7b0JBQ0E7Z0JBQ0o7Z0JBRUEsMkJBQTJCO2dCQUMzQixNQUFNVSxjQUFjLE1BQU0xQiw2REFBY0E7Z0JBQ3hDLElBQUksQ0FBQzBCLGFBQWE7b0JBQ2RWO29CQUNBO2dCQUNKO2dCQUVBUCxRQUFRaUI7WUFDWixFQUFFLE9BQU9DLE9BQU87Z0JBQ1pWLFFBQVFVLEtBQUssQ0FBQyxzQkFBc0JBO2dCQUNwQ1g7WUFDSjtRQUNKO29EQUFHO1FBQUNBO0tBQVk7SUFFaEIsK0NBQStDO0lBQy9DbkIsZ0RBQVNBO2tDQUFDO1lBQ04sTUFBTStCOzREQUFvQjtvQkFDdEIsSUFBSTt3QkFDQSwwREFBMEQ7d0JBQzFELE1BQU1KLFFBQVE3QixpREFBT0EsQ0FBQzhCLEdBQUcsQ0FBQzt3QkFDMUIsSUFBSSxDQUFDRCxTQUFTdkIsc0VBQWNBLElBQUk7NEJBQzVCUSxRQUFROzRCQUNSRSxXQUFXOzRCQUNYO3dCQUNKO3dCQUVBLE1BQU1ILE9BQTBCLE1BQU1SLDZEQUFjQTt3QkFDcEQsSUFBSVEsTUFBTTs0QkFDTkMsUUFBUUQ7NEJBRVIsc0NBQXNDOzRCQUN0QyxNQUFNcUIsV0FBV0MsWUFBWVAsaUJBQWlCLFFBQVEsOEJBQThCOzRCQUNwRlIscUJBQXFCYzt3QkFDekIsT0FBTzs0QkFDSGxDLGlEQUFPQSxDQUFDb0MsTUFBTSxDQUFDOzRCQUNmdEIsUUFBUTt3QkFDWjtvQkFFSixFQUFFLE9BQU9rQixPQUFPO3dCQUNaVixRQUFRVSxLQUFLLENBQUMsMEJBQTBCQTt3QkFDeENoQyxpREFBT0EsQ0FBQ29DLE1BQU0sQ0FBQzt3QkFDZnRCLFFBQVE7b0JBQ1osU0FBVTt3QkFDTkUsV0FBVztvQkFDZjtnQkFDSjs7WUFDQWlCO1lBRUEsOEJBQThCO1lBQzlCOzBDQUFPO29CQUNILElBQUlkLG1CQUFtQjt3QkFDbkJLLGNBQWNMO29CQUNsQjtnQkFDSjs7UUFDSjtpQ0FBRztRQUFDUztRQUFpQlQ7S0FBa0I7SUFFdkMsTUFBTWtCLFFBQVEsT0FBT0MsT0FBZUMsVUFBa0JDLFlBQXFCQztRQUN2RSxJQUFJO1lBQ0EsTUFBTUMsV0FBVyxNQUFNQyxNQUFNLEdBQWdCLE9BQWJuQyxjQUFhLGdCQUFjO2dCQUN2RG9DLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ0wsZ0JBQWdCO2dCQUNwQjtnQkFDQUMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO29CQUNqQlYsT0FBT0E7b0JBQ1BDLFVBQVVBO2dCQUNkO1lBQ0o7WUFFQSxNQUFNVSxPQUFPLE1BQU1QLFNBQVNRLElBQUk7WUFDaEMsSUFBSSxDQUFDUixTQUFTUyxFQUFFLEVBQUU7Z0JBQ2Q3QixRQUFRVSxLQUFLLENBQUMsZ0JBQWdCaUIsS0FBS0csT0FBTyxJQUFJO2dCQUM5QyxNQUFNLElBQUlDLE1BQU1KLEtBQUtHLE9BQU8sSUFBSTtZQUNwQztZQUVBLE1BQU0sRUFBRUUsT0FBTyxFQUFFLEdBQUdMO1lBQ3BCLElBQUksQ0FBQ0ssU0FBUztnQkFDVixNQUFNLElBQUlELE1BQU07WUFDcEI7WUFFQSxvQ0FBb0M7WUFDcENyRCxpREFBT0EsQ0FBQ3VELEdBQUcsQ0FBQyxXQUFXRCxTQUFTO2dCQUFFRSxTQUFTaEIsYUFBYSxLQUFLO1lBQUUsSUFBSSxzQkFBc0I7WUFFekYsTUFBTTNCLE9BQTBCLE1BQU1SLDZEQUFjQSxJQUFJLDhFQUE4RTtZQUN0SSxJQUFJUSxNQUFNO2dCQUNOQyxRQUFRRDtZQUNaO1lBRUEsbURBQW1EO1lBQ25ELElBQUk0QixhQUFhO2dCQUNidkIsc0JBQXNCdUI7WUFDMUI7UUFFSixFQUFFLE9BQU9ULE9BQU87WUFDWlYsUUFBUVUsS0FBSyxDQUFDLGlCQUFpQkE7WUFDL0IsTUFBTUE7UUFDVjtJQUNKO0lBRUEsTUFBTXlCLFNBQVM7UUFDWDNDLFFBQVE7UUFDUmQsaURBQU9BLENBQUNvQyxNQUFNLENBQUMsWUFBWSxpQ0FBaUM7UUFDNURsQixzQkFBc0IsT0FBTyxxQ0FBcUM7UUFDbEUsT0FBT3dDLFFBQVFDLE9BQU87SUFDMUI7SUFFQSxNQUFNQyxrQkFBa0IsQ0FBQyxDQUFDL0MsTUFBTSw0Q0FBNEM7SUFFNUUscUJBQ0ksOERBQUNKLFlBQVlvRCxRQUFRO1FBQUNDLE9BQU87WUFDekJqRDtZQUNBa0QsaUJBQWlCSDtZQUNqQjdDO1lBQ0FHO1lBQ0FEO1lBQ0FvQjtZQUNBb0I7WUFDQTdCO1lBQ0FQO1FBQ0o7a0JBQ0tUOzs7Ozs7QUFHYixFQUFDO0dBaEtZRDtLQUFBQSIsInNvdXJjZXMiOlsiRDpcXFByb2pldFxcc2Nob2xhcmlmeVxcZGFzaGJvYXJkXFxzcmNcXGFwcFxcc2VydmljZXNcXEF1dGhDb250ZXh0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcbmltcG9ydCBDb29raWVzIGZyb20gXCJqcy1jb29raWVcIjtcclxuaW1wb3J0IHsgY3JlYXRlQ29udGV4dCwgdXNlRWZmZWN0LCB1c2VTdGF0ZSwgdXNlQ2FsbGJhY2sgfSBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IHsgand0RGVjb2RlIH0gZnJvbSBcImp3dC1kZWNvZGVcIjtcclxuaW1wb3J0IHsgZ2V0Q3VycmVudFVzZXIgfSBmcm9tIFwiLi9Vc2VyU2VydmljZXNcIjtcclxuaW1wb3J0IHsgVXNlclNjaGVtYSB9IGZyb20gXCIuLi9tb2RlbHMvVXNlck1vZGVsXCI7XHJcbmltcG9ydCB7IGlzVG9rZW5FeHBpcmVkLCBjbGVhclNlc3Npb24gfSBmcm9tIFwiLi4vdXRpbHMvaHR0cEludGVyY2VwdG9yXCI7XHJcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gXCJuZXh0L25hdmlnYXRpb25cIjtcclxuXHJcbmV4cG9ydCBjb25zdCBCQVNFX0FQSV9VUkwgPSBcImh0dHBzOi8vc2NvbGFyaWZ5Lm9ucmVuZGVyLmNvbS9hcGlcIjtcclxuXHJcblxyXG5cclxuLy8gQ29udGV4dCBmb3IgYXV0aGVudGljYXRpb24gYW5kIHVzZXIgZGF0YVxyXG5cclxuaW50ZXJmYWNlIEF1dGhDb250ZXh0VHlwZSB7XHJcbiAgICB1c2VyOiBVc2VyU2NoZW1hIHwgbnVsbDtcclxuICAgIGlzQXV0aGVudGljYXRlZDogYm9vbGVhbjtcclxuICAgIGxvYWRpbmc6IGJvb2xlYW47XHJcbiAgICBsb2dpbjogKGVtYWlsOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcsIHJlbWVtYmVyTWU6IGJvb2xlYW4sIHJlZGlyZWN0VXJsPzogc3RyaW5nKSA9PiBQcm9taXNlPHZvaWQ+O1xyXG4gICAgbG9nb3V0OiAoKSA9PiBQcm9taXNlPHZvaWQ+O1xyXG4gICAgcmVkaXJlY3RBZnRlckxvZ2luOiBzdHJpbmcgfCBudWxsO1xyXG4gICAgc2V0UmVkaXJlY3RBZnRlckxvZ2luOiAodXJsOiBzdHJpbmcpID0+IHZvaWQ7XHJcbiAgICBjaGVja0F1dGhTdGF0dXM6ICgpID0+IFByb21pc2U8dm9pZD47XHJcbiAgICBmb3JjZUxvZ291dDogKCkgPT4gdm9pZDtcclxufVxyXG5cclxuLy8gQ3JlYXRlIGEgY29udGV4dCBmb3IgYXV0aGVudGljYXRpb25cclxuZXhwb3J0IGNvbnN0IEF1dGhDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxBdXRoQ29udGV4dFR5cGUgfCB1bmRlZmluZWQ+KHVuZGVmaW5lZCk7XHJcblxyXG4vLyBJbnRlcmZhY2UgZm9yIHRoZSBwcm92aWRlciBwcm9wc1xyXG5pbnRlcmZhY2UgQXV0aFByb3ZpZGVyUHJvcHMge1xyXG4gICAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcclxufVxyXG5cclxuLy8gY29tcG9zYW50IEF1dGhQcm92aWRlciBxdWkgZm91cm5pdCBsZSBjb250ZXh0ZSBkJ2F1dGhlbnRpZmljYXRpb25cclxuXHJcbmV4cG9ydCBjb25zdCBBdXRoUHJvdmlkZXI6IFJlYWN0LkZDPEF1dGhQcm92aWRlclByb3BzPiA9ICh7IGNoaWxkcmVuIH0pID0+IHtcclxuICAgIGNvbnN0IFt1c2VyLCBzZXRVc2VyXSA9IHVzZVN0YXRlPFVzZXJTY2hlbWEgfCBudWxsPihudWxsKTtcclxuICAgIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xyXG4gICAgY29uc3QgW3JlZGlyZWN0QWZ0ZXJMb2dpbiwgc2V0UmVkaXJlY3RBZnRlckxvZ2luXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xyXG4gICAgY29uc3QgW2F1dGhDaGVja0ludGVydmFsLCBzZXRBdXRoQ2hlY2tJbnRlcnZhbF0gPSB1c2VTdGF0ZTxOb2RlSlMuVGltZW91dCB8IG51bGw+KG51bGwpO1xyXG5cclxuICAgIC8vIEZvbmN0aW9uIHBvdXIgZm9yY2VyIGxhIGTDqWNvbm5leGlvblxyXG4gICAgY29uc3QgZm9yY2VMb2dvdXQgPSB1c2VDYWxsYmFjaygoKSA9PiB7XHJcbiAgICAgICAgY29uc29sZS53YXJuKFwiRm9yY2UgbG9nb3V0IHRyaWdnZXJlZFwiKTtcclxuICAgICAgICBzZXRVc2VyKG51bGwpO1xyXG4gICAgICAgIGNsZWFyU2Vzc2lvbigpO1xyXG4gICAgICAgIGlmIChhdXRoQ2hlY2tJbnRlcnZhbCkge1xyXG4gICAgICAgICAgICBjbGVhckludGVydmFsKGF1dGhDaGVja0ludGVydmFsKTtcclxuICAgICAgICAgICAgc2V0QXV0aENoZWNrSW50ZXJ2YWwobnVsbCk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xyXG4gICAgICAgICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9ICcvbG9naW4nO1xyXG4gICAgICAgIH1cclxuICAgIH0sIFthdXRoQ2hlY2tJbnRlcnZhbF0pO1xyXG5cclxuICAgIC8vIEZvbmN0aW9uIHBvdXIgdsOpcmlmaWVyIGxlIHN0YXR1dCBkJ2F1dGhlbnRpZmljYXRpb25cclxuICAgIGNvbnN0IGNoZWNrQXV0aFN0YXR1cyA9IHVzZUNhbGxiYWNrKGFzeW5jICgpID0+IHtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICAvLyBWw6lyaWZpZXIgc2kgbGUgdG9rZW4gZXhpc3RlXHJcbiAgICAgICAgICAgIGNvbnN0IHRva2VuID0gQ29va2llcy5nZXQoXCJpZFRva2VuXCIpO1xyXG4gICAgICAgICAgICBpZiAoIXRva2VuKSB7XHJcbiAgICAgICAgICAgICAgICBmb3JjZUxvZ291dCgpO1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAvLyBWw6lyaWZpZXIgc2kgbGUgdG9rZW4gZXN0IGV4cGlyw6lcclxuICAgICAgICAgICAgaWYgKGlzVG9rZW5FeHBpcmVkKCkpIHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUud2FybihcIlRva2VuIGV4cGlyZWQsIGxvZ2dpbmcgb3V0XCIpO1xyXG4gICAgICAgICAgICAgICAgZm9yY2VMb2dvdXQoKTtcclxuICAgICAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLy8gVsOpcmlmaWVyIGF2ZWMgbGUgc2VydmV1clxyXG4gICAgICAgICAgICBjb25zdCBjdXJyZW50VXNlciA9IGF3YWl0IGdldEN1cnJlbnRVc2VyKCk7XHJcbiAgICAgICAgICAgIGlmICghY3VycmVudFVzZXIpIHtcclxuICAgICAgICAgICAgICAgIGZvcmNlTG9nb3V0KCk7XHJcbiAgICAgICAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIHNldFVzZXIoY3VycmVudFVzZXIpO1xyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJBdXRoIGNoZWNrIGZhaWxlZDpcIiwgZXJyb3IpO1xyXG4gICAgICAgICAgICBmb3JjZUxvZ291dCgpO1xyXG4gICAgICAgIH1cclxuICAgIH0sIFtmb3JjZUxvZ291dF0pO1xyXG5cclxuICAgIC8vIHbDqXJpZmllciBzaSB1biB1dGlsaXNhdGV1ciBlc3QgZMOpamEgY29ubmVjdMOpXHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIGNvbnN0IGNoZWNrVXNlckxvZ2dlZEluID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICAgICAgLy8gVsOpcmlmaWVyIGQnYWJvcmQgc2kgbGUgdG9rZW4gZXhpc3RlIGV0IG4nZXN0IHBhcyBleHBpcsOpXHJcbiAgICAgICAgICAgICAgICBjb25zdCB0b2tlbiA9IENvb2tpZXMuZ2V0KFwiaWRUb2tlblwiKTtcclxuICAgICAgICAgICAgICAgIGlmICghdG9rZW4gfHwgaXNUb2tlbkV4cGlyZWQoKSkge1xyXG4gICAgICAgICAgICAgICAgICAgIHNldFVzZXIobnVsbCk7XHJcbiAgICAgICAgICAgICAgICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgIGNvbnN0IHVzZXI6IFVzZXJTY2hlbWEgfCBudWxsID0gYXdhaXQgZ2V0Q3VycmVudFVzZXIoKTtcclxuICAgICAgICAgICAgICAgIGlmICh1c2VyKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgc2V0VXNlcih1c2VyKTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLy8gRMOpbWFycmVyIGxhIHbDqXJpZmljYXRpb24gcMOpcmlvZGlxdWVcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBpbnRlcnZhbCA9IHNldEludGVydmFsKGNoZWNrQXV0aFN0YXR1cywgNjAwMDApOyAvLyBWw6lyaWZpZXIgdG91dGVzIGxlcyBtaW51dGVzXHJcbiAgICAgICAgICAgICAgICAgICAgc2V0QXV0aENoZWNrSW50ZXJ2YWwoaW50ZXJ2YWwpO1xyXG4gICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICBDb29raWVzLnJlbW92ZShcImlkVG9rZW5cIik7XHJcbiAgICAgICAgICAgICAgICAgICAgc2V0VXNlcihudWxsKTtcclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgdmVyaWZ5aW5nIHRva2VuOlwiLCBlcnJvcik7XHJcbiAgICAgICAgICAgICAgICBDb29raWVzLnJlbW92ZShcImlkVG9rZW5cIik7XHJcbiAgICAgICAgICAgICAgICBzZXRVc2VyKG51bGwpO1xyXG4gICAgICAgICAgICB9IGZpbmFsbHkge1xyXG4gICAgICAgICAgICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9O1xyXG4gICAgICAgIGNoZWNrVXNlckxvZ2dlZEluKCk7XHJcblxyXG4gICAgICAgIC8vIENsZWFudXAgaW50ZXJ2YWwgb24gdW5tb3VudFxyXG4gICAgICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgICAgICAgIGlmIChhdXRoQ2hlY2tJbnRlcnZhbCkge1xyXG4gICAgICAgICAgICAgICAgY2xlYXJJbnRlcnZhbChhdXRoQ2hlY2tJbnRlcnZhbCk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9O1xyXG4gICAgfSwgW2NoZWNrQXV0aFN0YXR1cywgYXV0aENoZWNrSW50ZXJ2YWxdKTtcclxuXHJcbiAgICBjb25zdCBsb2dpbiA9IGFzeW5jIChlbWFpbDogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nLCByZW1lbWJlck1lOiBib29sZWFuLCByZWRpcmVjdFVybD86IHN0cmluZykgPT4ge1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7QkFTRV9BUElfVVJMfS9hdXRoL2xvZ2luYCwge1xyXG4gICAgICAgICAgICAgICAgbWV0aG9kOiBcIlBPU1RcIixcclxuICAgICAgICAgICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICAgICAgICAgICBcIkNvbnRlbnQtVHlwZVwiOiBcImFwcGxpY2F0aW9uL2pzb25cIixcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XHJcbiAgICAgICAgICAgICAgICAgICAgZW1haWw6IGVtYWlsLFxyXG4gICAgICAgICAgICAgICAgICAgIHBhc3N3b3JkOiBwYXNzd29yZCxcclxuICAgICAgICAgICAgICAgIH0pLFxyXG4gICAgICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICAgICAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJMb2dpbiBlcnJvcjpcIiwgZGF0YS5tZXNzYWdlIHx8IFwiVW5rbm93biBlcnJvclwiKTtcclxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihkYXRhLm1lc3NhZ2UgfHwgXCJMb2dpbiBmYWlsZWRcIik7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIGNvbnN0IHsgaWRUb2tlbiB9ID0gZGF0YTtcclxuICAgICAgICAgICAgaWYgKCFpZFRva2VuKSB7XHJcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJObyBpZFRva2VuIHJlY2VpdmVkXCIpO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAvLyBTdG9ja2VyIGxlIHRva2VuIGRhbnMgbGVzIGNvb2tpZXNcclxuICAgICAgICAgICAgQ29va2llcy5zZXQoXCJpZFRva2VuXCIsIGlkVG9rZW4sIHsgZXhwaXJlczogcmVtZW1iZXJNZSA/IDMwIDogNyB9KTsgLy8gRXhwaXJlIGRhbnMgNyBqb3Vyc1xyXG5cclxuICAgICAgICAgICAgY29uc3QgdXNlcjogVXNlclNjaGVtYSB8IG51bGwgPSBhd2FpdCBnZXRDdXJyZW50VXNlcigpOyAvLyBWw6lyaWZpZXIgc2kgbCd1dGlsaXNhdGV1ciBlc3QgY29ubmVjdMOpIMOgIG5vdXZlYXUgYXByw6hzIGxhIGNvbm5leGlvbiByw6l1c3NpZVxyXG4gICAgICAgICAgICBpZiAodXNlcikge1xyXG4gICAgICAgICAgICAgICAgc2V0VXNlcih1c2VyKTtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLy8gU2kgdW5lIFVSTCBkZSByZWRpcmVjdGlvbiBlc3QgZm91cm5pZSwgc3RvY2tlLWxhXHJcbiAgICAgICAgICAgIGlmIChyZWRpcmVjdFVybCkge1xyXG4gICAgICAgICAgICAgICAgc2V0UmVkaXJlY3RBZnRlckxvZ2luKHJlZGlyZWN0VXJsKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBcclxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFwiTG9naW4gZmFpbGVkOlwiLCBlcnJvcik7XHJcbiAgICAgICAgICAgIHRocm93IGVycm9yO1xyXG4gICAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgY29uc3QgbG9nb3V0ID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICAgIHNldFVzZXIobnVsbCk7XHJcbiAgICAgICAgQ29va2llcy5yZW1vdmUoXCJpZFRva2VuXCIpOyAvLyBTdXBwcmltZXIgbGUgdG9rZW4gZGVzIGNvb2tpZXNcclxuICAgICAgICBzZXRSZWRpcmVjdEFmdGVyTG9naW4obnVsbCk7IC8vIFLDqWluaXRpYWxpc2VyIGwnVVJMIGRlIHJlZGlyZWN0aW9uXHJcbiAgICAgICAgcmV0dXJuIFByb21pc2UucmVzb2x2ZSgpO1xyXG4gICAgfTtcclxuXHJcbiAgICBjb25zdCBpc0F1dGhlbnRpYWN0ZWQgPSAhIXVzZXI7IC8vIFbDqXJpZmllciBzaSBsJ3V0aWxpc2F0ZXVyIGVzdCBhdXRoZW50aWZpw6lcclxuXHJcbiAgICByZXR1cm4gKFxyXG4gICAgICAgIDxBdXRoQ29udGV4dC5Qcm92aWRlciB2YWx1ZT17e1xyXG4gICAgICAgICAgICB1c2VyLFxyXG4gICAgICAgICAgICBpc0F1dGhlbnRpY2F0ZWQ6IGlzQXV0aGVudGlhY3RlZCxcclxuICAgICAgICAgICAgbG9hZGluZyxcclxuICAgICAgICAgICAgc2V0UmVkaXJlY3RBZnRlckxvZ2luLFxyXG4gICAgICAgICAgICByZWRpcmVjdEFmdGVyTG9naW4sXHJcbiAgICAgICAgICAgIGxvZ2luLFxyXG4gICAgICAgICAgICBsb2dvdXQsXHJcbiAgICAgICAgICAgIGNoZWNrQXV0aFN0YXR1cyxcclxuICAgICAgICAgICAgZm9yY2VMb2dvdXRcclxuICAgICAgICB9fT5cclxuICAgICAgICAgICAge2NoaWxkcmVufVxyXG4gICAgICAgIDwvQXV0aENvbnRleHQuUHJvdmlkZXI+XHJcbiAgICApO1xyXG59Il0sIm5hbWVzIjpbIkNvb2tpZXMiLCJjcmVhdGVDb250ZXh0IiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJ1c2VDYWxsYmFjayIsImdldEN1cnJlbnRVc2VyIiwiaXNUb2tlbkV4cGlyZWQiLCJjbGVhclNlc3Npb24iLCJCQVNFX0FQSV9VUkwiLCJBdXRoQ29udGV4dCIsInVuZGVmaW5lZCIsIkF1dGhQcm92aWRlciIsImNoaWxkcmVuIiwidXNlciIsInNldFVzZXIiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsInJlZGlyZWN0QWZ0ZXJMb2dpbiIsInNldFJlZGlyZWN0QWZ0ZXJMb2dpbiIsImF1dGhDaGVja0ludGVydmFsIiwic2V0QXV0aENoZWNrSW50ZXJ2YWwiLCJmb3JjZUxvZ291dCIsImNvbnNvbGUiLCJ3YXJuIiwiY2xlYXJJbnRlcnZhbCIsIndpbmRvdyIsImxvY2F0aW9uIiwiaHJlZiIsImNoZWNrQXV0aFN0YXR1cyIsInRva2VuIiwiZ2V0IiwiY3VycmVudFVzZXIiLCJlcnJvciIsImNoZWNrVXNlckxvZ2dlZEluIiwiaW50ZXJ2YWwiLCJzZXRJbnRlcnZhbCIsInJlbW92ZSIsImxvZ2luIiwiZW1haWwiLCJwYXNzd29yZCIsInJlbWVtYmVyTWUiLCJyZWRpcmVjdFVybCIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJkYXRhIiwianNvbiIsIm9rIiwibWVzc2FnZSIsIkVycm9yIiwiaWRUb2tlbiIsInNldCIsImV4cGlyZXMiLCJsb2dvdXQiLCJQcm9taXNlIiwicmVzb2x2ZSIsImlzQXV0aGVudGlhY3RlZCIsIlByb3ZpZGVyIiwidmFsdWUiLCJpc0F1dGhlbnRpY2F0ZWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/services/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/services/SchoolServices.tsx":
/*!*********************************************!*\
  !*** ./src/app/services/SchoolServices.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSchool: () => (/* binding */ createSchool),\n/* harmony export */   deleteAllSchools: () => (/* binding */ deleteAllSchools),\n/* harmony export */   deleteMultipleSchools: () => (/* binding */ deleteMultipleSchools),\n/* harmony export */   deleteSchool: () => (/* binding */ deleteSchool),\n/* harmony export */   getSchoolById: () => (/* binding */ getSchoolById),\n/* harmony export */   getSchoolBy_id: () => (/* binding */ getSchoolBy_id),\n/* harmony export */   getSchoolCountChange: () => (/* binding */ getSchoolCountChange),\n/* harmony export */   getSchoolPerformance: () => (/* binding */ getSchoolPerformance),\n/* harmony export */   getSchools: () => (/* binding */ getSchools),\n/* harmony export */   getTotalSchools: () => (/* binding */ getTotalSchools),\n/* harmony export */   updateSchool: () => (/* binding */ updateSchool)\n/* harmony export */ });\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./AuthContext */ \"(app-pages-browser)/./src/app/services/AuthContext.tsx\");\n/* harmony import */ var _UserServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./UserServices */ \"(app-pages-browser)/./src/app/services/UserServices.tsx\");\n\n\nasync function getSchools() {\n    try {\n        const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/get-schools\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching schools:\", response.statusText);\n            throw new Error(\"Failed to fetch schools data\");\n        }\n        const schoolsList = await response.json();\n        const schools = schoolsList.map((school)=>{\n            return {\n                _id: school._id,\n                school_id: school.school_id,\n                name: school.name,\n                email: school.email,\n                address: school.address,\n                website: school.website,\n                phone_number: school.phone_number,\n                principal_name: school.principal_name,\n                established_year: school.established_year,\n                description: school.description\n            };\n        });\n        return schools;\n    } catch (error) {\n        console.error(\"Error fetching schools:\", error);\n        throw new Error(\"Failed to fetch schools data\");\n    }\n}\nasync function getSchoolById(schoolId) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/get-school/\").concat(schoolId), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat((0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching school:\", response.statusText);\n        throw new Error(\"Failed to fetch school data\");\n    }\n    const data = await response.json();\n    const school = {\n        school_id: data.school_id,\n        name: data.name,\n        email: data.email,\n        address: data.address,\n        website: data.website,\n        phone_number: data.phone_number,\n        principal_name: data.principal_name,\n        established_year: data.established_year,\n        description: data.description\n    };\n    return school;\n}\nasync function getSchoolBy_id(schoolId) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/get-school_id/\").concat(schoolId), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat((0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching school:\", response.statusText);\n        throw new Error(\"Failed to fetch school data\");\n    }\n    const data = await response.json();\n    const school = {\n        school_id: data.school_id,\n        name: data.name,\n        email: data.email,\n        address: data.address,\n        website: data.website,\n        phone_number: data.phone_number,\n        principal_name: data.principal_name,\n        established_year: data.established_year,\n        description: data.description\n    };\n    return school;\n}\nasync function createSchool(schoolData) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/create-school\"), {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat((0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\"))\n        },\n        body: JSON.stringify(schoolData)\n    });\n    if (!response.ok) {\n        console.error(\"Error creating school:\", response.statusText);\n        throw new Error(\"Failed to create school data\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function updateSchool(schoolId, schoolData) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/update-school/\").concat(schoolId), {\n        method: \"PUT\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat((0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\"))\n        },\n        body: JSON.stringify(schoolData)\n    });\n    if (!response.ok) {\n        console.error(\"Error updating school:\", response.statusText);\n        throw new Error(\"Failed to update school data\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function deleteSchool(schoolId) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/delete-school/\").concat(schoolId), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat((0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting school:\", response.statusText);\n        throw new Error(\"Failed to delete school data\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function deleteMultipleSchools(schoolIds) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/delete-schools\"), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat((0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\"))\n        },\n        body: JSON.stringify({\n            ids: schoolIds\n        })\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting multiple schools:\", response.statusText);\n        throw new Error(\"Failed to delete multiple schools\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function deleteAllSchools() {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/delete-all-schools\"), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat((0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting all schools:\", response.statusText);\n        throw new Error(\"Failed to delete all schools\");\n    }\n    const data = await response.json();\n    return data;\n}\n// Get total number of schools\nasync function getTotalSchools() {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/total-schools\"), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching total schools:\", response.statusText);\n        throw new Error(\"Failed to fetch total schools\");\n    }\n    const data = await response.json();\n    return data.totalSchools;\n}\n// Get schools created this month and percentage change from last month\nasync function getSchoolCountChange() {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/schools-count-change\"), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching school count change:\", response.statusText);\n        throw new Error(\"Failed to fetch school count change\");\n    }\n    const data = await response.json();\n    return {\n        totalThisMonth: data.totalSchoolsThisMonth,\n        percentageChange: data.percentageChange\n    };\n}\nasync function getSchoolPerformance() {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/performance\"), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching performance metrics:\", response.statusText);\n        throw new Error(\"Failed to fetch school performance metrics\");\n    }\n    const data = await response.json();\n    return data;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/services/SchoolServices.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/services/UserServices.tsx":
/*!*******************************************!*\
  !*** ./src/app/services/UserServices.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   deleteAllUsers: () => (/* binding */ deleteAllUsers),\n/* harmony export */   deleteMultipleUsers: () => (/* binding */ deleteMultipleUsers),\n/* harmony export */   deleteUser: () => (/* binding */ deleteUser),\n/* harmony export */   forget_password: () => (/* binding */ forget_password),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getMonthlyUserStarts: () => (/* binding */ getMonthlyUserStarts),\n/* harmony export */   getTokenFromCookie: () => (/* binding */ getTokenFromCookie),\n/* harmony export */   getTotalUsers: () => (/* binding */ getTotalUsers),\n/* harmony export */   getUserById: () => (/* binding */ getUserById),\n/* harmony export */   getUserBy_id: () => (/* binding */ getUserBy_id),\n/* harmony export */   getUserCountWithChange: () => (/* binding */ getUserCountWithChange),\n/* harmony export */   getUsers: () => (/* binding */ getUsers),\n/* harmony export */   handleUserSearch: () => (/* binding */ handleUserSearch),\n/* harmony export */   registerParent: () => (/* binding */ registerParent),\n/* harmony export */   resend_Code: () => (/* binding */ resend_Code),\n/* harmony export */   reset_password: () => (/* binding */ reset_password),\n/* harmony export */   updateUser: () => (/* binding */ updateUser),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verify_otp: () => (/* binding */ verify_otp)\n/* harmony export */ });\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var jwt_decode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jwt-decode */ \"(app-pages-browser)/./node_modules/jwt-decode/build/esm/index.js\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"(app-pages-browser)/./src/app/services/AuthContext.tsx\");\n/* harmony import */ var _utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/httpInterceptor */ \"(app-pages-browser)/./src/app/utils/httpInterceptor.tsx\");\n\n\n\n\nfunction getTokenFromCookie(name) {\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(name);\n    return token;\n}\nasync function getCurrentUser() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        if (!token) {\n            return null;\n        }\n        const decodedUser = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_1__.jwtDecode)(token);\n        const email = decodedUser.email;\n        const response = await (0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_3__.authenticatedGet)(\"/user/get-user-email/\".concat(email));\n        if (!response.ok) {\n            console.error(\"Error fetching user:\", response.statusText);\n            return null;\n        }\n        const user = await response.json();\n        return user;\n    } catch (error) {\n        console.error(\"Error fetching current user:\", error);\n        return null;\n    }\n}\nasync function getUsers() {\n    try {\n        const token = getTokenFromCookie(\"idToken\"); // Assuming this function gets the token from cookies\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/get-users\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching users:\", response.statusText);\n            throw new Error(\"Failed to fetch users data\");\n        }\n        const usersList = await response.json();\n        const users = usersList.map((user)=>{\n            return {\n                _id: user._id,\n                user_id: user.user_id,\n                firebaseUid: user.firebaseUid,\n                name: user.name,\n                email: user.email,\n                phone: user.phone,\n                role: user.role,\n                avatar: user.avatar,\n                address: user.address,\n                school_ids: user.school_ids,\n                isVerified: user.isVerified,\n                verificationCode: user.verificationCode,\n                verificationCodeExpires: user.verificationCodeExpires,\n                lastLogin: user.lastLogin,\n                createdAt: user.createdAt,\n                updatedAt: user.updatedAt\n            };\n        });\n        return users;\n    } catch (error) {\n        console.error(\"Error fetching users:\", error);\n        throw new Error(\"Failed to fetch users data\");\n    }\n}\nasync function createUser(userData) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/register-user\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n            },\n            body: JSON.stringify(userData)\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to create user data\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                // If parsing the error body fails, use default message\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            console.error(\"Error creating user:\", errorMessage);\n            throw new Error(errorMessage);\n        }\n        const data = await response.json(); // Parse the response as JSON\n        return data; // Return the response data (usually the created user object)\n    } catch (error) {\n        console.error(\"Error creating user:\", error);\n        throw new Error(error instanceof Error ? error.message : \"Failed to create user data\");\n    }\n}\nasync function updateUser(user_id, userData) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/update-user/\").concat(user_id), {\n            method: \"PUT\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n            },\n            body: JSON.stringify(userData)\n        });\n        if (!response.ok) {\n            console.error(\"Error updating user:\", response.statusText);\n            throw new Error(\"Failed to update user data\");\n        }\n        const data = await response.json(); // Parse the response as JSON\n        return data; // Return the updated user data (this could be the user object with updated fields)\n    } catch (error) {\n        console.error(\"Error updating user:\", error);\n        throw new Error(\"Failed to update user data\");\n    }\n}\nasync function getUserById(userId) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/get-user/\").concat(userId), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching user:\", response.statusText);\n        throw new Error(\"Failed to fetch user data\");\n    }\n    const data = await response.json();\n    const user = {\n        _id: data._id,\n        user_id: data.user_id,\n        name: data.name,\n        email: data.email,\n        phone: data.phone,\n        role: data.role,\n        address: data.address,\n        school_ids: data.school_ids,\n        isVerified: data.isVerified,\n        description: data.description,\n        firebaseUid: data.firebaseUid,\n        createdAt: data.createdAt,\n        updatedAt: data.updatedAt\n    };\n    return user;\n}\nasync function verifyPassword(password, email) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/auth/verify-password\"), {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n            email: email,\n            password: password\n        })\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching user:\", response.statusText);\n        return false;\n    }\n    return true;\n}\nasync function deleteUser(user_id) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/delete-user/\").concat(user_id), {\n            method: \"DELETE\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n            }\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to delete user\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            console.error(\"Error deleting user:\", errorMessage);\n            throw new Error(errorMessage);\n        }\n        const result = await response.json();\n        return result; // Might return a success message or deleted user data\n    } catch (error) {\n        console.error(\"Error deleting user:\", error);\n        throw new Error(error instanceof Error ? error.message : \"Failed to delete user\");\n    }\n}\nasync function getUserBy_id(_id) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/get-user-by-id/\").concat(_id), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching user:\", response.statusText);\n        throw new Error(\"Failed to fetch user data\");\n    }\n    const data = await response.json();\n    const user = {\n        _id: data._id,\n        user_id: data.user_id,\n        name: data.name,\n        email: data.email,\n        phone: data.phone,\n        role: data.role,\n        address: data.address,\n        school_ids: data.school_ids,\n        isVerified: data.isVerified,\n        description: data.description,\n        firebaseUid: data.firebaseUid,\n        createdAt: data.createdAt,\n        updatedAt: data.updatedAt\n    };\n    return user;\n}\nasync function forget_password(email) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/auth/forgot-password\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email: email\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to send reset password email: check your email\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error sending reset password email:\", error);\n        throw new Error(\"Failed to send reset password email\");\n    }\n}\nasync function verify_otp(code, email) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/auth/verify-code\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                code: code,\n                email: email\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to verify OTP\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error verifying OTP:\", error);\n        throw new Error(\"Failed to verify OTP\");\n    }\n}\nasync function resend_Code(email) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/auth/resend-code\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email: email\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to resend code\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error resending code:\", error);\n        throw new Error(\"Failed to resend code\");\n    }\n}\nasync function reset_password(newPassword, email, code) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/auth/reset-password\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email: email,\n                code: code,\n                newPassword: newPassword\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to reset password\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error resetting password:\", error);\n        throw new Error(\"Failed to reset password\");\n    }\n}\nasync function handleUserSearch(query) {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/search-users?query=\").concat(encodeURIComponent(query)), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error searching users:\", response.statusText);\n            throw new Error(\"Failed to search users\");\n        }\n        const results = await response.json();\n        const users = results.map((user)=>{\n            return {\n                _id: user._id,\n                user_id: user.user_id,\n                firebaseUid: user.firebaseUid,\n                name: user.name,\n                email: user.email,\n                phone: user.phone,\n                role: user.role,\n                avatar: user.avatar,\n                address: user.address,\n                school_ids: user.school_ids,\n                isVerified: user.isVerified,\n                verificationCode: user.verificationCode,\n                verificationCodeExpires: user.verificationCodeExpires,\n                lastLogin: user.lastLogin,\n                createdAt: user.createdAt,\n                updatedAt: user.updatedAt\n            };\n        });\n        return users;\n    } catch (error) {\n        console.error(\"Error searching users:\", error);\n        throw new Error(\"Failed to search users\");\n    }\n}\nasync function registerParent(parentData) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/register-parent\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n            },\n            body: JSON.stringify(parentData)\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to register parent\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            console.error(\"Error registering parent:\", errorMessage);\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data; // This includes user object and generatedPassword (if new)\n    } catch (error) {\n        console.error(\"Error in registerParent service:\", error);\n        throw new Error(error instanceof Error ? error.message : \"Failed to register parent\");\n    }\n}\nasync function deleteMultipleUsers(userIds) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/delete-users\"), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n        },\n        body: JSON.stringify({\n            ids: userIds\n        })\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting multiple users:\", response.statusText);\n        throw new Error(\"Failed to delete multiple users\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function deleteAllUsers() {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/delete-all-users\"), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting all users:\", response.statusText);\n        throw new Error(\"Failed to delete all users\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function getTotalUsers() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/total-users\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching total users:\", response.statusText);\n            throw new Error(\"Failed to fetch total users\");\n        }\n        const data = await response.json();\n        return data.totalUsers;\n    } catch (error) {\n        console.error(\"Error fetching total users:\", error);\n        throw error;\n    }\n}\nasync function getUserCountWithChange() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/users-count-change\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching user count change:\", response.statusText);\n            throw new Error(\"Failed to fetch user count change\");\n        }\n        const data = await response.json();\n        return {\n            totalUsersThisMonth: data.totalUsersThisMonth,\n            percentageChange: data.percentageChange\n        };\n    } catch (error) {\n        console.error(\"Error fetching user count change:\", error);\n        throw error;\n    }\n}\nasync function getMonthlyUserStarts() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/monthly-user-starts\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Failed to fetch monthly user stats:\", response.statusText);\n            throw new Error(\"Failed to fetch monthly user stats\");\n        }\n        const data = await response.json();\n        // data has type { [year: string]: MonthlyUserStat[] }\n        return data;\n    } catch (error) {\n        console.error(\"Error fetching monthly user stats:\", error);\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/services/UserServices.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Dashboard/Layouts/SchoolLayout.tsx":
/*!***********************************************************!*\
  !*** ./src/components/Dashboard/Layouts/SchoolLayout.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,DollarSign,GraduationCap,LayoutDashboard,Presentation,School,Settings,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,DollarSign,GraduationCap,LayoutDashboard,Presentation,School,Settings,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/school.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,DollarSign,GraduationCap,LayoutDashboard,Presentation,School,Settings,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/presentation.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,DollarSign,GraduationCap,LayoutDashboard,Presentation,School,Settings,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,DollarSign,GraduationCap,LayoutDashboard,Presentation,School,Settings,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,DollarSign,GraduationCap,LayoutDashboard,Presentation,School,Settings,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,DollarSign,GraduationCap,LayoutDashboard,Presentation,School,Settings,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,DollarSign,GraduationCap,LayoutDashboard,Presentation,School,Settings,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,DollarSign,GraduationCap,LayoutDashboard,Presentation,School,Settings,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _widgets_Divider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../widgets/Divider */ \"(app-pages-browser)/./src/components/widgets/Divider.tsx\");\n/* harmony import */ var _widgets_SearchBox__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../widgets/SearchBox */ \"(app-pages-browser)/./src/components/widgets/SearchBox.tsx\");\n/* harmony import */ var _SideNavButton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../SideNavButton */ \"(app-pages-browser)/./src/components/Dashboard/SideNavButton.tsx\");\n/* harmony import */ var _Avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../Avatar */ \"(app-pages-browser)/./src/components/Dashboard/Avatar.tsx\");\n/* harmony import */ var _widgets_Logo__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../widgets/Logo */ \"(app-pages-browser)/./src/components/widgets/Logo.tsx\");\n/* harmony import */ var _GoPro__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../GoPro */ \"(app-pages-browser)/./src/components/Dashboard/GoPro.tsx\");\n/* harmony import */ var _NavigationBar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../NavigationBar */ \"(app-pages-browser)/./src/components/Dashboard/NavigationBar.tsx\");\n/* harmony import */ var _barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var _components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/utils/ProtectedRoute */ \"(app-pages-browser)/./src/components/utils/ProtectedRoute.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst SchoolLayout = (param)=>{\n    let { navigation, showGoPro = true, onLogout, children } = param;\n    _s();\n    const { user } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_9__[\"default\"])();\n    const [isSidebarOpen, setIsSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const avatar = {\n        avatarUrl: (user === null || user === void 0 ? void 0 : user.avatar) || \"https://img.freepik.com/free-psd/3d-illustration-person-with-sunglasses_23-2149436188.jpg\",\n        name: (user === null || user === void 0 ? void 0 : user.name) || \"School Admin\",\n        role: (user === null || user === void 0 ? void 0 : user.role) || \"admin\"\n    };\n    const BASE_URL = \"/school-admin\";\n    const sidebarNav = [\n        {\n            icon: _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            name: \"Dashboard\",\n            href: \"\".concat(BASE_URL, \"/dashboard\")\n        },\n        {\n            icon: _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            name: \"School\",\n            href: \"\".concat(BASE_URL, \"/school\")\n        },\n        {\n            icon: _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            name: \"Classes\",\n            href: \"\".concat(BASE_URL, \"/classes\")\n        },\n        {\n            icon: _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            name: \"Students\",\n            href: \"\".concat(BASE_URL, \"/students\")\n        },\n        {\n            icon: _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            name: \"Teachers\",\n            href: \"\".concat(BASE_URL, \"/teachers\")\n        },\n        {\n            icon: _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            name: \"Resources\",\n            href: \"\".concat(BASE_URL, \"/resources\")\n        },\n        {\n            icon: _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            name: \"Fees\",\n            href: \"\".concat(BASE_URL, \"/fees\")\n        },\n        {\n            icon: _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            name: \"Parents\",\n            href: \"\".concat(BASE_URL, \"/parents\")\n        }\n    ];\n    const settingsLink = {\n        icon: _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n        name: \"Settings\",\n        href: \"\".concat(BASE_URL, \"/settings\")\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen overflow-hidden sm:p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"md:hidden p-2 bg-foreground text-background rounded-lg fixed top-4 left-4 z-50\",\n                    onClick: ()=>setIsSidebarOpen(!isSidebarOpen),\n                    children: isSidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 28\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 46\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex w-[290px] flex-col h-full border border-stroke p-2 rounded-lg fixed inset-y-0 left-0 z-40 bg-background transition-transform md:relative md:translate-x-0 \".concat(isSidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-3 overflow-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center gap-2 my-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_Logo__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_Divider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_SearchBox__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col gap-1\",\n                                    children: sidebarNav.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SideNavButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            icon: item.icon,\n                                            name: item.name,\n                                            href: item.href\n                                        }, item.name, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-auto flex flex-col gap-3\",\n                            children: [\n                                showGoPro && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GoPro__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    visible: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 27\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SideNavButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    icon: settingsLink.icon,\n                                    name: settingsLink.name,\n                                    href: settingsLink.href\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_Divider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Avatar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    avatarUrl: avatar.avatarUrl,\n                                    name: avatar.name,\n                                    role: avatar.role,\n                                    onLogout: onLogout\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-2 w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NavigationBar__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            icon: navigation.icon,\n                            baseHref: navigation.baseHref,\n                            title: navigation.title,\n                            onLogout: onLogout\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SchoolLayout, \"kzOdFYiF9c/5ccJ6E6DU+A4WC6E=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    ];\n});\n_c = SchoolLayout;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SchoolLayout);\nvar _c;\n$RefreshReg$(_c, \"SchoolLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Dashboard/Layouts/SchoolLayout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Dashboard/NavigationBar.tsx":
/*!****************************************************!*\
  !*** ./src/components/Dashboard/NavigationBar.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NavigationBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _widgets_SearchBox__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../widgets/SearchBox */ \"(app-pages-browser)/./src/components/widgets/SearchBox.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _BreadCrums__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./BreadCrums */ \"(app-pages-browser)/./src/components/Dashboard/BreadCrums.tsx\");\n/* harmony import */ var _widgets_UserMenuModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../widgets/UserMenuModal */ \"(app-pages-browser)/./src/components/widgets/UserMenuModal.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction NavigationBar(param) {\n    let { icon: Icon, baseHref, title, toggleSidebar, isSidebarOpen, onLogout } = param;\n    _s();\n    const [isDarkMode, setIsDarkMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user, logout } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    // Vérifier le thème au chargement\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NavigationBar.useEffect\": ()=>{\n            const prefersDark = window.matchMedia(\"(prefers-color-scheme: dark)\").matches;\n            setIsDarkMode(prefersDark);\n            if (prefersDark) {\n                document.documentElement.classList.add(\"dark\");\n            }\n        }\n    }[\"NavigationBar.useEffect\"], []);\n    // Basculer entre mode clair et sombre\n    const toggleTheme = ()=>{\n        setIsDarkMode(!isDarkMode);\n        if (isDarkMode) {\n            document.documentElement.classList.remove(\"dark\");\n        } else {\n            document.documentElement.classList.add(\"dark\");\n        }\n    };\n    // Gérer la déconnexion\n    const handleSignOut = ()=>{\n        onLogout();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full flex items-center justify-between p-4 bg-glassy\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                id: \"mobile-sidebar-toggle\",\n                className: \"lg:hidden p-2  text-foreground rounded-lg  top-4 left-4 z-30\",\n                onClick: ()=>toggleSidebar && toggleSidebar(),\n                children: isSidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 26\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 44\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:flex flex-col gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BreadCrums__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        baseHref: baseHref,\n                        icon: Icon\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-2xl font-semibold text-foreground\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_SearchBox__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"hidden lg:flex p-2 text-gray-600 dark:text-gray-300 hover:text-foreground transition\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"w-6 h-6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_UserMenuModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        avatarUrl: (user === null || user === void 0 ? void 0 : user.avatar) || \"https://img.freepik.com/free-psd/3d-illustration-person-with-sunglasses_23-2149436188.jpg\",\n                        userName: (user === null || user === void 0 ? void 0 : user.name) || \"\",\n                        onSignOut: logout,\n                        onToggleTheme: toggleTheme,\n                        isDarkMode: isDarkMode\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n_s(NavigationBar, \"24OZTdcMQjo2aNvu0Uo7vpQ5B80=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c = NavigationBar;\nvar _c;\n$RefreshReg$(_c, \"NavigationBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Dashboard/NavigationBar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Dashboard/SideNavButton.tsx":
/*!****************************************************!*\
  !*** ./src/components/Dashboard/SideNavButton.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst SidebarButton = (param)=>{\n    let { icon: Icon, name, href } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const isActive = pathname.startsWith(href);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n        href: href,\n        className: \"text-sm flex items-center w-full px-4 py-2 rounded-lg transition-all \\n                 hover:text-background hover:bg-foreground hover:shadow-lg\\n                 \".concat(isActive ? \"bg-foreground bg- text-background shadow-lg\" : \"text-foreground\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                className: \"w-5 h-5 mr-2\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\SideNavButton.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: name\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\SideNavButton.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\SideNavButton.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SidebarButton, \"xbyQPtUVMO7MNj7WjJlpdWqRcTo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = SidebarButton;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SidebarButton);\nvar _c;\n$RefreshReg$(_c, \"SidebarButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Dashboard/SideNavButton.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/utils/ProtectedRoute.tsx":
/*!*************************************************!*\
  !*** ./src/components/utils/ProtectedRoute.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var _widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../widgets/CircularLoader */ \"(app-pages-browser)/./src/components/widgets/CircularLoader.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst ProtectedRoute = (param)=>{\n    let { children } = param;\n    _s();\n    const { isAuthenticated, loading, setRedirectAfterLogin } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProtectedRoute.useEffect\": ()=>{\n            if (!loading && !isAuthenticated) {\n                // Stocker l'URL actuelle pour rediriger après la connexion\n                setRedirectAfterLogin(pathname);\n                router.push(\"/login\");\n            }\n        }\n    }[\"ProtectedRoute.useEffect\"], [\n        loading,\n        isAuthenticated,\n        router,\n        pathname,\n        setRedirectAfterLogin\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-screen w-full absolute top-0 left-0 z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: 40,\n                color: \"teal\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\ProtectedRoute.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\ProtectedRoute.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!isAuthenticated) {\n        return null; // La redirection est gérée par useEffect\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n};\n_s(ProtectedRoute, \"ID4lDsz3ESTPiaN6oNQ7fqj9lxE=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = ProtectedRoute;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProtectedRoute);\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/utils/ProtectedRoute.tsx\n"));

/***/ })

});