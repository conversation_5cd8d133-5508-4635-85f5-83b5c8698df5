"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/period/page",{

/***/ "(app-pages-browser)/./src/components/Dashboard/NavigationBar.tsx":
/*!****************************************************!*\
  !*** ./src/components/Dashboard/NavigationBar.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NavigationBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _widgets_SearchBox__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../widgets/SearchBox */ \"(app-pages-browser)/./src/components/widgets/SearchBox.tsx\");\n/* harmony import */ var _barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _BreadCrums__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./BreadCrums */ \"(app-pages-browser)/./src/components/Dashboard/BreadCrums.tsx\");\n/* harmony import */ var _widgets_UserMenuModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../widgets/UserMenuModal */ \"(app-pages-browser)/./src/components/widgets/UserMenuModal.tsx\");\n/* harmony import */ var _widgets_NotificationCenter__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../widgets/NotificationCenter */ \"(app-pages-browser)/./src/components/widgets/NotificationCenter.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction NavigationBar(param) {\n    let { icon: Icon, baseHref, title, toggleSidebar, isSidebarOpen, onLogout } = param;\n    _s();\n    const [isDarkMode, setIsDarkMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user, logout } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n    // Vérifier le thème au chargement\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NavigationBar.useEffect\": ()=>{\n            const prefersDark = window.matchMedia(\"(prefers-color-scheme: dark)\").matches;\n            setIsDarkMode(prefersDark);\n            if (prefersDark) {\n                document.documentElement.classList.add(\"dark\");\n            }\n        }\n    }[\"NavigationBar.useEffect\"], []);\n    // Basculer entre mode clair et sombre\n    const toggleTheme = ()=>{\n        setIsDarkMode(!isDarkMode);\n        if (isDarkMode) {\n            document.documentElement.classList.remove(\"dark\");\n        } else {\n            document.documentElement.classList.add(\"dark\");\n        }\n    };\n    // Gérer la déconnexion\n    const handleSignOut = ()=>{\n        onLogout();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full flex items-center justify-between p-4 bg-glassy shadow-md border md:border-none md:shadow-none border-gray-300 darK:border dark:border-gray-800\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                id: \"mobile-sidebar-toggle\",\n                className: \"lg:hidden p-2  text-foreground rounded-lg  top-4 left-4 z-30\",\n                onClick: ()=>toggleSidebar && toggleSidebar(),\n                children: isSidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 26\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 44\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:flex flex-col gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BreadCrums__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        baseHref: baseHref,\n                        icon: Icon\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-2xl font-semibold text-foreground\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_SearchBox__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_NotificationCenter__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"hidden lg:block\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_UserMenuModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        avatarUrl: (user === null || user === void 0 ? void 0 : user.avatar) || \"https://img.freepik.com/free-psd/3d-illustration-person-with-sunglasses_23-2149436188.jpg\",\n                        userName: (user === null || user === void 0 ? void 0 : user.name) || \"\",\n                        onSignOut: logout,\n                        onToggleTheme: toggleTheme,\n                        isDarkMode: isDarkMode\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n_s(NavigationBar, \"24OZTdcMQjo2aNvu0Uo7vpQ5B80=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n});\n_c = NavigationBar;\nvar _c;\n$RefreshReg$(_c, \"NavigationBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Dashboard/NavigationBar.tsx\n"));

/***/ })

});