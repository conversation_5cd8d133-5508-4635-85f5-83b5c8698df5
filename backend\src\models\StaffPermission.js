const mongoose = require('mongoose');

// Define the schema for Staff Permissions
const staffPermissionSchema = new mongoose.Schema({
  permission_id: {
    type: String,
    required: true,
    unique: true
  },
  
  user_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  school_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'School',
    required: true
  },
  
  role_template: {
    type: String,
    required: true,
    enum: ['school_admin', 'teacher', 'bursar', 'dean_of_studies', 'custom'],
    default: 'custom'
  },
  
  // Granular permissions grouped by modules
  permissions: {
    // Students module permissions
    students: {
      view_all_students: { type: Boolean, default: false },
      add_edit_delete_students: { type: Boolean, default: false },
      generate_id_cards: { type: Boolean, default: false },
      generate_report_cards: { type: Boolean, default: false }
    },
    
    // Academic Records module permissions
    academic_records: {
      view_grades_assigned_classes: { type: Boolean, default: false },
      enter_edit_grades_assigned_classes: { type: Boolean, default: false },
      view_all_school_grades: { type: Boolean, default: false },
      take_attendance_assigned_classes: { type: Boolean, default: false },
      view_all_attendance: { type: Boolean, default: false }
    },
    
    // Financials module permissions
    financials: {
      view_student_fee_balances: { type: Boolean, default: false },
      record_fee_payments: { type: Boolean, default: false },
      manage_school_credit_balance: { type: Boolean, default: false },
      view_financial_reports: { type: Boolean, default: false }
    },
    
    // Staff module permissions
    staff: {
      view_staff_list: { type: Boolean, default: false },
      add_edit_delete_staff: { type: Boolean, default: false },
      manage_staff_permissions: { type: Boolean, default: false },
      reset_staff_passwords: { type: Boolean, default: false }
    },
    
    // Classes module permissions
    classes: {
      view_all_classes: { type: Boolean, default: false },
      add_edit_delete_classes: { type: Boolean, default: false },
      manage_class_schedules: { type: Boolean, default: false },
      assign_teachers_to_classes: { type: Boolean, default: false }
    },
    
    // Announcements module permissions
    announcements: {
      view_announcements: { type: Boolean, default: false },
      create_edit_announcements: { type: Boolean, default: false },
      delete_announcements: { type: Boolean, default: false },
      publish_announcements: { type: Boolean, default: false }
    },
    
    // Resources module permissions
    resources: {
      view_resources: { type: Boolean, default: false },
      add_edit_delete_resources: { type: Boolean, default: false },
      manage_resource_categories: { type: Boolean, default: false }
    },
    
    // Reports module permissions
    reports: {
      generate_student_reports: { type: Boolean, default: false },
      generate_financial_reports: { type: Boolean, default: false },
      generate_attendance_reports: { type: Boolean, default: false },
      export_data: { type: Boolean, default: false }
    }
  },
  
  // Class assignments for teachers
  assigned_classes: [{
    class_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Class'
    },
    subjects: [String], // Array of subject names
    periods: [String]   // Array of period times
  }],
  
  // Status and metadata
  is_active: {
    type: Boolean,
    default: true
  },
  
  granted_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  granted_at: {
    type: Date,
    default: Date.now
  },
  
  last_modified_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  last_modified_at: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Indexes for better performance
staffPermissionSchema.index({ user_id: 1, school_id: 1 }, { unique: true });
staffPermissionSchema.index({ school_id: 1 });
staffPermissionSchema.index({ role_template: 1 });

// Static method to get default permissions for role templates
staffPermissionSchema.statics.getDefaultPermissions = function(roleTemplate) {
  const defaultPermissions = {
    school_admin: {
      students: {
        view_all_students: true,
        add_edit_delete_students: true,
        generate_id_cards: true,
        generate_report_cards: true
      },
      academic_records: {
        view_grades_assigned_classes: true,
        enter_edit_grades_assigned_classes: true,
        view_all_school_grades: true,
        take_attendance_assigned_classes: true,
        view_all_attendance: true
      },
      financials: {
        view_student_fee_balances: true,
        record_fee_payments: true,
        manage_school_credit_balance: true,
        view_financial_reports: true
      },
      staff: {
        view_staff_list: true,
        add_edit_delete_staff: true,
        manage_staff_permissions: true,
        reset_staff_passwords: true
      },
      classes: {
        view_all_classes: true,
        add_edit_delete_classes: true,
        manage_class_schedules: true,
        assign_teachers_to_classes: true
      },
      announcements: {
        view_announcements: true,
        create_edit_announcements: true,
        delete_announcements: true,
        publish_announcements: true
      },
      resources: {
        view_resources: true,
        add_edit_delete_resources: true,
        manage_resource_categories: true
      },
      reports: {
        generate_student_reports: true,
        generate_financial_reports: true,
        generate_attendance_reports: true,
        export_data: true
      }
    },
    
    teacher: {
      students: {
        view_all_students: true,
        add_edit_delete_students: false,
        generate_id_cards: false,
        generate_report_cards: true
      },
      academic_records: {
        view_grades_assigned_classes: true,
        enter_edit_grades_assigned_classes: true,
        view_all_school_grades: false,
        take_attendance_assigned_classes: true,
        view_all_attendance: false
      },
      financials: {
        view_student_fee_balances: false,
        record_fee_payments: false,
        manage_school_credit_balance: false,
        view_financial_reports: false
      },
      staff: {
        view_staff_list: true,
        add_edit_delete_staff: false,
        manage_staff_permissions: false,
        reset_staff_passwords: false
      },
      classes: {
        view_all_classes: true,
        add_edit_delete_classes: false,
        manage_class_schedules: false,
        assign_teachers_to_classes: false
      },
      announcements: {
        view_announcements: true,
        create_edit_announcements: false,
        delete_announcements: false,
        publish_announcements: false
      },
      resources: {
        view_resources: true,
        add_edit_delete_resources: true,
        manage_resource_categories: false
      },
      reports: {
        generate_student_reports: true,
        generate_financial_reports: false,
        generate_attendance_reports: true,
        export_data: false
      }
    },
    
    bursar: {
      students: {
        view_all_students: true,
        add_edit_delete_students: false,
        generate_id_cards: true,
        generate_report_cards: false
      },
      academic_records: {
        view_grades_assigned_classes: false,
        enter_edit_grades_assigned_classes: false,
        view_all_school_grades: false,
        take_attendance_assigned_classes: false,
        view_all_attendance: false
      },
      financials: {
        view_student_fee_balances: true,
        record_fee_payments: true,
        manage_school_credit_balance: true,
        view_financial_reports: true
      },
      staff: {
        view_staff_list: true,
        add_edit_delete_staff: false,
        manage_staff_permissions: false,
        reset_staff_passwords: false
      },
      classes: {
        view_all_classes: true,
        add_edit_delete_classes: false,
        manage_class_schedules: false,
        assign_teachers_to_classes: false
      },
      announcements: {
        view_announcements: true,
        create_edit_announcements: false,
        delete_announcements: false,
        publish_announcements: false
      },
      resources: {
        view_resources: true,
        add_edit_delete_resources: false,
        manage_resource_categories: false
      },
      reports: {
        generate_student_reports: false,
        generate_financial_reports: true,
        generate_attendance_reports: false,
        export_data: true
      }
    },
    
    dean_of_studies: {
      students: {
        view_all_students: true,
        add_edit_delete_students: true,
        generate_id_cards: true,
        generate_report_cards: true
      },
      academic_records: {
        view_grades_assigned_classes: true,
        enter_edit_grades_assigned_classes: true,
        view_all_school_grades: true,
        take_attendance_assigned_classes: true,
        view_all_attendance: true
      },
      financials: {
        view_student_fee_balances: false,
        record_fee_payments: false,
        manage_school_credit_balance: false,
        view_financial_reports: false
      },
      staff: {
        view_staff_list: true,
        add_edit_delete_staff: false,
        manage_staff_permissions: false,
        reset_staff_passwords: false
      },
      classes: {
        view_all_classes: true,
        add_edit_delete_classes: true,
        manage_class_schedules: true,
        assign_teachers_to_classes: true
      },
      announcements: {
        view_announcements: true,
        create_edit_announcements: true,
        delete_announcements: false,
        publish_announcements: true
      },
      resources: {
        view_resources: true,
        add_edit_delete_resources: true,
        manage_resource_categories: true
      },
      reports: {
        generate_student_reports: true,
        generate_financial_reports: false,
        generate_attendance_reports: true,
        export_data: true
      }
    }
  };
  
  return defaultPermissions[roleTemplate] || {};
};

// Use the model if it's already defined, or create a new one
const StaffPermission = mongoose.models.StaffPermission || mongoose.model('StaffPermission', staffPermissionSchema);

module.exports = StaffPermission;
