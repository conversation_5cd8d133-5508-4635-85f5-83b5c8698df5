"use client";

import React, { useEffect, useState } from "react";
import { X } from "lucide-react";
import { motion } from "framer-motion";
import CustomInput from "@/components/inputs/CustomInput";
import CircularLoader from "@/components/widgets/CircularLoader";
import SubmissionFeedback from "@/components/widgets/SubmissionFeedback";
import { StudentSchema } from "@/app/models/StudentModel";
import { ClassSchema } from "@/app/models/ClassModel";
import CustomTextarea from "@/components/inputs/CustomTextarea";
import CustomPhoneInput from "@/components/inputs/CustomPhoneInput";
import CustomDateInput from "@/components/inputs/CustomDateInput";
import CustomCheckboxInput from "@/components/inputs/CustomCheckBoxInput";
import CustomSelect from "@/components/inputs/CustomSelect";
import { ClassLevelSchema } from "@/app/models/ClassLevel";
import { getClassLevelsBySchoolId } from "@/app/services/ClassLevels";
import SignalLoader from "@/components/widgets/SignalLoader";

interface UpdateStudentModalProps {
  onClose: () => void;
  onSave: (data: StudentSchema) => Promise<void>;
  initialData?: StudentSchema;
  submitStatus: "success" | "failure" | null;
  isSubmitting: boolean;
}

const UpdateStudentModal: React.FC<UpdateStudentModalProps> = ({
  onClose,
  onSave,
  initialData,
  submitStatus,
  isSubmitting,
}) => {
  const [formData, setFormData] = useState<StudentSchema>({
    _id: "",
    student_id: "",
    school_id: "",
    first_name: "",
    last_name: "",
    middle_name: "",
    date_of_birth: new Date(),
    nationality: "",
    gender: undefined,
    place_of_birth: "",
    address: "",
    student_phone: "",
    guardian_phone: "",
    student_country_code: "",
    guardian_country_code: "",
    emergency_contact_country_code: "",
    doctor_country_code: "",
    guardian_name: "",
    guardian_address: "",
    guardian_occupation: "",
    guardian_email: "",
    guardian_relationship: undefined,
    previous_school: "",
    class_level: "",
    guardian_agreed_to_terms: false,
    transcript_reportcard: false,
    emergency_contact_name: "",
    emergency_contact_phone: "",
    emergency_contact_relationship: undefined,
    health_condition: "",
    doctors_name: "",
    doctors_phone: "",
    registered: true,
    selectedFees: [],
    selectedResources: [],
    paymentMode: "full",
    installments: 1,
    installmentDates: [new Date().toISOString().split("T")[0]],
    applyScholarship: false,
    scholarshipAmount: 0,
    scholarshipPercentage: 0,
    status: "not enrolled",
    name: "",
    fees: 0,
    enrollement_date: "",
  });
  const [classLevels, setClassLevels] = useState<ClassLevelSchema[]>([])
  const [classLoading, setClassLoading] = useState(true);
  const fetchClassLevel = async () => {
    try {
      setClassLoading(true);
      if (!formData.school_id) {
        throw new Error("School ID is required to fetch class levels.");
      }
      const levels = await getClassLevelsBySchoolId(formData.school_id);
      setClassLevels(levels);
    } catch (error) {
      console.error("Error fetching class levels:", error);
    } finally {
      setClassLoading(false);
    }
  };

  useEffect(() => {
    if (initialData) {
      setFormData((prev) => ({
        ...prev,
        ...initialData,
        gender: initialData.gender?.toLowerCase() === "male"
          ? "Male"
          : initialData.gender?.toLowerCase() === "female"
            ? "Female"
            : initialData.gender?.toLowerCase() === "other"
              ? "Other"
              : undefined,
        date_of_birth: initialData.date_of_birth
          ? new Date(initialData.date_of_birth)
          : new Date(),
      }));
    }
  }, [initialData]);

  useEffect(() => {
    if (formData.school_id) {
      fetchClassLevel();
    }
  }, [formData.school_id]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = e.target;
    const parsedValue =
      type === "number" ? (value === "" ? "" : parseFloat(value)) : value;
    setFormData((prev) => ({ ...prev, [name]: parsedValue }));
  };

  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  const classOptions = classLevels.map((level) => ({
    label: level.name,
    value: level._id,
  }));

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-2xl max-h-[90vh] p-6 relative overflow-y-auto custom-scrollbar">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-foreground">Update Student</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X size={20} />
          </button>
        </div>

        {submitStatus ? (
          <SubmissionFeedback
            status={submitStatus}
            message={
              submitStatus === "success"
                ? "Student updated successfully!"
                : "Error updating student. Please try again or contact support."
            }
          />
        ) : (
          <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <CustomInput readOnly={true} label="Student ID" name="student_id" value={formData.student_id} onChange={handleChange} required id="student_id" />

            <CustomInput label="First Name" name="first_name" value={formData.first_name} onChange={handleChange} required id="first_name" />

            <CustomInput label="Last Name" name="last_name" value={formData.last_name} onChange={handleChange} required id="last_name" />

            <div>
              <label className="block text-sm mb-1">Gender</label>
              <select
                name="gender"
                value={formData.gender || ""}
                onChange={handleChange}
                className="w-full px-3 py-2 border rounded-md text-sm dark:bg-gray-700"
              >
                <option value="">Select Gender</option>
                <option value="Male">Male</option>
                <option value="Female">Female</option>
                <option value="Other">Other</option>
              </select>
            </div>


            <CustomDateInput
              label="Date of Birth"
              id="date_of_birth"
              name="date_of_birth"
              value={
                formData.date_of_birth
                  ? typeof formData.date_of_birth === "string"
                    ? formData.date_of_birth
                    : formData.date_of_birth.toISOString().split("T")[0]
                  : ""
              }
              onChange={handleChange}
            />

            <CustomInput label="Address" name="address" value={formData.address || ""} onChange={handleChange} id="address" />

            <CustomInput label="Place of Birth" name="place_of_birth" value={formData.place_of_birth || ""} onChange={handleChange} id="place_of_birth" />

            <CustomPhoneInput
              label="Student Phone Number (Optional)"
              id="student_phone"
              name="student_phone"
              value={typeof formData.student_phone === "string" ? formData.student_phone : ""}
              onChange={handleChange}
              countryCode={typeof formData.student_country_code === "string" ? formData.student_country_code : ""}
              onCountryCodeChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  student_country_code: e.target.value,
                }))
              }
            />


            {classLoading ? (
              <div className="col-span-2">
                <SignalLoader />
              </div>
            ) : (
              <CustomSelect
                label="Select Class"
                id="class_level"
                name="class_level"
                value={formData.class_level || ""}
                onChange={handleChange}
                required
                options={classOptions}
                placeholder="Select Class"
              />
            )}

            <CustomCheckboxInput
              label="Check the box if copies of transcripts or report cards were provided"
              id="transcript_reportcard"
              name="transcript_reportcard"
              checked={!!formData.transcript_reportcard}
              onChange={handleChange}
            />
            <CustomInput label="Previous School" name="previous_school" value={formData.previous_school || ""} onChange={handleChange} id="previous_school" />

            <CustomTextarea label="Health Information" name="health_condition" value={formData.health_condition || ""} onChange={handleTextareaChange} id="health_condition" />
            <CustomInput label="Doctor's Name" name="doctors_name" value={formData.doctors_name || ""} onChange={handleChange} id="doctors_name" />
            <CustomPhoneInput
              label="Doctor's Phone Number"
              id="doctors_phone"
              name="doctors_phone"
              value={typeof formData.doctors_phone === "string" ? formData.doctors_phone : ""}
              onChange={handleChange}
              countryCode={typeof formData.doctor_country_code === "string" ? formData.doctor_country_code : ""}
              onCountryCodeChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  doctor_country_code: e.target.value,
                }))
              } />

            <CustomPhoneInput
              label="Emergency Contact Phone"
              id="emergency_contact_phone"
              name="emergency_contact_phone"
              value={formData.emergency_contact_phone || ''}
              onChange={handleChange}
              countryCode={typeof formData.emergency_contact_country_code === "string" ? formData.emergency_contact_country_code : ""}
              onCountryCodeChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  emergency_contact_country_code: e.target.value,
                }))
              }
            /> 


            <div className="flex justify-end space-x-2 pt-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                type="button"
                onClick={onClose}
                className="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500"
              >
                Cancel
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 flex items-center gap-2"
              >
                {isSubmitting ? (
                  <>
                    <CircularLoader size={18} color="teal-500" />
                    Updating...
                  </>
                ) : (
                  "Update"
                )}
              </motion.button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};
export default UpdateStudentModal;


