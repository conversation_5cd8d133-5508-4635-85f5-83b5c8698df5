import { getTokenFromCookie } from "./UserServices";

const BASE_API_URL = process.env.BASE_API_URL || "https://scolarify.onrender.com/api";

export interface ExamType {
  _id: string;
  type: string;
  description?: string;
  createdAt?: string;
  updatedAt?: string;
}

// Get all exam types
export async function getExamTypes(): Promise<ExamType[]> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/exam/exam-types`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching exam types:", response.statusText);
      throw new Error("Failed to fetch exam types");
    }

    const data = await response.json();
    return data as ExamType[];
  } catch (error) {
    console.error("Fetch exam types error:", error);
    throw new Error("Failed to fetch exam types");
  }
}
