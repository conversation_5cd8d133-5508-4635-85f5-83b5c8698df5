import { getTokenFromCookie } from "@/app/services/UserServices";
import { BASE_API_URL } from "./AuthContext";

export interface AnnouncementSchema extends Record<string, unknown> {
  _id: string;
  announcement_id: string;
  title: string;
  content: string;
  school_id: string;
  author_id: string;
  target_audience: 'all' | 'teachers' | 'parents' | 'students';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  is_published: boolean;
  published_at?: Date;
  expires_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface AnnouncementCreateSchema {
  title: string;
  content: string;
  school_id: string;
  target_audience: 'all' | 'teachers' | 'parents' | 'students';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  is_published: boolean;
  expires_at?: Date;
}

// Get all announcements
export async function getAnnouncements(): Promise<AnnouncementSchema[]> {
  const response = await fetch(`${BASE_API_URL}/announcement/get-announcements`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
    },
  });

  if (!response.ok) {
    console.error("Error fetching announcements:", response.statusText);
    throw new Error("Failed to fetch announcements");
  }

  const data = await response.json();
  return data;
}

// Get announcement by ID
export async function getAnnouncementById(announcementId: string): Promise<AnnouncementSchema> {
  const response = await fetch(`${BASE_API_URL}/announcement/get-announcement/${announcementId}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
    },
  });

  if (!response.ok) {
    console.error("Error fetching announcement:", response.statusText);
    throw new Error("Failed to fetch announcement");
  }

  const data = await response.json();
  return data;
}

// Create a new announcement
export async function createAnnouncement(announcementData: AnnouncementCreateSchema): Promise<AnnouncementSchema> {
  const response = await fetch(`${BASE_API_URL}/announcement/create-announcement`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
    },
    body: JSON.stringify(announcementData),
  });

  if (!response.ok) {
    console.error("Error creating announcement:", response.statusText);
    throw new Error("Failed to create announcement");
  }

  const data = await response.json();
  return data;
}

// Update an announcement
export async function updateAnnouncement(announcementId: string, announcementData: Partial<AnnouncementCreateSchema>): Promise<AnnouncementSchema> {
  const response = await fetch(`${BASE_API_URL}/announcement/update-announcement/${announcementId}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
    },
    body: JSON.stringify(announcementData),
  });

  if (!response.ok) {
    console.error("Error updating announcement:", response.statusText);
    throw new Error("Failed to update announcement");
  }

  const data = await response.json();
  return data;
}

// Delete an announcement
export async function deleteAnnouncement(announcementId: string): Promise<{ message: string }> {
  const response = await fetch(`${BASE_API_URL}/announcement/delete-announcement/${announcementId}`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
    },
  });

  if (!response.ok) {
    console.error("Error deleting announcement:", response.statusText);
    throw new Error("Failed to delete announcement");
  }

  const data = await response.json();
  return data;
}

// Delete multiple announcements
export async function deleteMultipleAnnouncements(announcementIds: string[]): Promise<{ message: string }> {
  const response = await fetch(`${BASE_API_URL}/announcement/delete-announcements`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
    },
    body: JSON.stringify({ ids: announcementIds }),
  });

  if (!response.ok) {
    console.error("Error deleting multiple announcements:", response.statusText);
    throw new Error("Failed to delete multiple announcements");
  }

  const data = await response.json();
  return data;
}

// Delete all announcements
export async function deleteAllAnnouncements(): Promise<{ message: string; deletedCount: number }> {
  const response = await fetch(`${BASE_API_URL}/announcement/delete-all-announcements`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
    },
  });

  if (!response.ok) {
    console.error("Error deleting all announcements:", response.statusText);
    throw new Error("Failed to delete all announcements");
  }

  const data = await response.json();
  return data;
}

// Get announcements by school
export async function getAnnouncementsBySchool(schoolId: string): Promise<AnnouncementSchema[]> {
  const response = await fetch(`${BASE_API_URL}/announcement/get-announcements-by-school/${schoolId}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
    },
  });

  if (!response.ok) {
    console.error("Error fetching announcements by school:", response.statusText);
    throw new Error("Failed to fetch announcements by school");
  }

  const data = await response.json();
  return data;
}

// Publish/unpublish announcement
export async function toggleAnnouncementPublication(announcementId: string, isPublished: boolean): Promise<AnnouncementSchema> {
  const response = await fetch(`${BASE_API_URL}/announcement/toggle-publication/${announcementId}`, {
    method: "PATCH",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
    },
    body: JSON.stringify({ is_published: isPublished }),
  });

  if (!response.ok) {
    console.error("Error toggling announcement publication:", response.statusText);
    throw new Error("Failed to toggle announcement publication");
  }

  const data = await response.json();
  return data;
}

// Get recent announcements by school (last 5 published announcements)
export async function getRecentAnnouncementsBySchool(schoolId: string): Promise<AnnouncementSchema[]> {
  try {
    const allAnnouncements = await getAnnouncementsBySchool(schoolId);

    // Filter published announcements and sort by creation date (most recent first)
    const recentAnnouncements = allAnnouncements
      .filter(announcement => announcement.is_published)
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      .slice(0, 5); // Get only the 5 most recent

    return recentAnnouncements;
  } catch (error) {
    console.error("Error fetching recent announcements by school:", error);
    throw new Error("Failed to fetch recent announcements by school");
  }
}
