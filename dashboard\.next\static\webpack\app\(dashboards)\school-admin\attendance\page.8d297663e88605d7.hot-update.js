"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/attendance/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/user-check.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ UserCheck)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.485.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\",\n            key: \"1yyitq\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"9\",\n            cy: \"7\",\n            r: \"4\",\n            key: \"nufk8\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"16 11 18 13 22 9\",\n            key: \"1pwet4\"\n        }\n    ]\n];\nconst UserCheck = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"user-check\", __iconNode);\n //# sourceMappingURL=user-check.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Dashboard/Layouts/SchoolLayout.tsx":
/*!***********************************************************!*\
  !*** ./src/components/Dashboard/Layouts/SchoolLayout.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/school.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-cog.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/percent.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock-4.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-no-axes-gantt.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/notebook-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/presentation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/milestone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/megaphone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _widgets_Divider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../widgets/Divider */ \"(app-pages-browser)/./src/components/widgets/Divider.tsx\");\n/* harmony import */ var _widgets_SearchBox__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../widgets/SearchBox */ \"(app-pages-browser)/./src/components/widgets/SearchBox.tsx\");\n/* harmony import */ var _SideNavButton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../SideNavButton */ \"(app-pages-browser)/./src/components/Dashboard/SideNavButton.tsx\");\n/* harmony import */ var _SidebarGroup__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../SidebarGroup */ \"(app-pages-browser)/./src/components/Dashboard/SidebarGroup.tsx\");\n/* harmony import */ var _Avatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../Avatar */ \"(app-pages-browser)/./src/components/Dashboard/Avatar.tsx\");\n/* harmony import */ var _widgets_Logo__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../widgets/Logo */ \"(app-pages-browser)/./src/components/widgets/Logo.tsx\");\n/* harmony import */ var _GoPro__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../GoPro */ \"(app-pages-browser)/./src/components/Dashboard/GoPro.tsx\");\n/* harmony import */ var _NavigationBar__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../NavigationBar */ \"(app-pages-browser)/./src/components/Dashboard/NavigationBar.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var _components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/utils/ProtectedRoute */ \"(app-pages-browser)/./src/components/utils/ProtectedRoute.tsx\");\n/* harmony import */ var _BreadCrums__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../BreadCrums */ \"(app-pages-browser)/./src/components/Dashboard/BreadCrums.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst SchoolLayout = (param)=>{\n    let { navigation, showGoPro = true, onLogout, children } = param;\n    _s();\n    const { user } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_10__[\"default\"])();\n    const [isSidebarOpen, setIsSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const avatar = {\n        avatarUrl: (user === null || user === void 0 ? void 0 : user.avatar) || \"https://img.freepik.com/free-psd/3d-illustration-person-with-sunglasses_23-2149436188.jpg\",\n        name: (user === null || user === void 0 ? void 0 : user.name) || \"School Admin\",\n        role: (user === null || user === void 0 ? void 0 : user.role) || \"admin\"\n    };\n    const BASE_URL = \"/school-admin\";\n    // Individual navigation items (not in groups)\n    const individualNavItems = [\n        {\n            icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            name: \"Dashboard\",\n            href: \"\".concat(BASE_URL, \"/dashboard\")\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            name: \"School\",\n            href: \"\".concat(BASE_URL, \"/school\")\n        }\n    ];\n    // Grouped navigation items\n    const navigationGroups = [\n        {\n            title: \"People Management\",\n            icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            items: [\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                    name: \"Staff\",\n                    href: \"\".concat(BASE_URL, \"/staff\")\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                    name: \"Teachers\",\n                    href: \"\".concat(BASE_URL, \"/teachers\")\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                    name: \"Students\",\n                    href: \"\".concat(BASE_URL, \"/students\")\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                    name: \"Parents\",\n                    href: \"\".concat(BASE_URL, \"/parents\")\n                }\n            ]\n        },\n        {\n            title: \"Academic Records\",\n            icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n            items: [\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                    name: \"Grades\",\n                    href: \"\".concat(BASE_URL, \"/grades\")\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                    name: \"Time Table\",\n                    href: \"\".concat(BASE_URL, \"/timetable\")\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n                    name: \"Teacher Assignment\",\n                    href: \"\".concat(BASE_URL, \"/teacher-assignment\")\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                    name: \"Periods\",\n                    href: \"\".concat(BASE_URL, \"/period\")\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n                    name: \"Attendance\",\n                    href: \"\".concat(BASE_URL, \"/attendance\")\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n                    name: \"Subjects\",\n                    href: \"\".concat(BASE_URL, \"/subjects\")\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n                    name: \"Classes\",\n                    href: \"\".concat(BASE_URL, \"/classes\")\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n                    name: \"Discipline\",\n                    href: \"\".concat(BASE_URL, \"/discipline\")\n                }\n            ]\n        },\n        {\n            title: \"Communications\",\n            icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n            items: [\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n                    name: \"Announcements\",\n                    href: \"\".concat(BASE_URL, \"/announcements\")\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                    name: \"Resources\",\n                    href: \"\".concat(BASE_URL, \"/resources\")\n                }\n            ]\n        },\n        {\n            title: \"Financial\",\n            icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"],\n            items: [\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"],\n                    name: \"Fee Types\",\n                    href: \"\".concat(BASE_URL, \"/fees\")\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"],\n                    name: \"Fee Transactions\",\n                    href: \"\".concat(BASE_URL, \"/transaction\")\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"],\n                    name: \"Buy Credit\",\n                    href: \"\".concat(BASE_URL, \"/buy-credit\")\n                }\n            ]\n        }\n    ];\n    const settingsLink = {\n        icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"],\n        name: \"Settings\",\n        href: \"\".concat(BASE_URL, \"/settings\")\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen overflow-hidden sm:p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"md:hidden p-2 bg-foreground text-background rounded-lg fixed top-4 left-4 z-50\",\n                    onClick: ()=>setIsSidebarOpen(!isSidebarOpen),\n                    children: isSidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 28\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 46\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex w-[290px] flex-col border border-gray-300 darK:border dark:border-gray-800 h-full shadow-lg p-2 rounded-lg fixed inset-y-0 left-0 z-40 bg-widget transition-transform lg:relative lg:translate-x-0 \".concat(isSidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-3 overflow-auto subtle-scrollbar\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center gap-2 my-4 \",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_Logo__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_Divider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_SearchBox__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col gap-1\",\n                                    children: [\n                                        individualNavItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SideNavButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                icon: item.icon,\n                                                name: item.name,\n                                                href: item.href\n                                            }, item.name, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 17\n                                            }, undefined)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"my-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_Divider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        navigationGroups.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SidebarGroup__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                title: group.title,\n                                                icon: group.icon,\n                                                items: group.items,\n                                                defaultExpanded: group.title === \"Academic Records\"\n                                            }, group.title, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-auto flex flex-col gap-3\",\n                            children: [\n                                showGoPro && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GoPro__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    visible: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 27\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SideNavButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    icon: settingsLink.icon,\n                                    name: settingsLink.name,\n                                    href: settingsLink.href\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_Divider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Avatar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    avatarUrl: avatar.avatarUrl,\n                                    name: avatar.name,\n                                    role: avatar.role,\n                                    onLogout: onLogout\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"sm:px-6 px-2 py-2 w-full flex flex-col gap-4 lg:w-[80%] overflow-auto custom-scrollbar\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sticky top-0 z-20 flex items-center justify-between   \",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NavigationBar__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                icon: navigation.icon,\n                                baseHref: navigation.baseHref,\n                                title: navigation.title,\n                                isSidebarOpen: isSidebarOpen,\n                                toggleSidebar: ()=>setIsSidebarOpen(!isSidebarOpen),\n                                onLogout: onLogout\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex lg:hidden flex-col gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BreadCrums__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    baseHref: navigation.baseHref,\n                                    icon: navigation.icon\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-semibold text-foreground\",\n                                    children: navigation.title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n            lineNumber: 105,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SchoolLayout, \"kzOdFYiF9c/5ccJ6E6DU+A4WC6E=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    ];\n});\n_c = SchoolLayout;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SchoolLayout);\nvar _c;\n$RefreshReg$(_c, \"SchoolLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0Rhc2hib2FyZC9MYXlvdXRzL1NjaG9vbExheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUN3QztBQU1sQjtBQUNzQjtBQUNJO0FBQ0g7QUFDRjtBQUNaO0FBQ087QUFDVDtBQUNnQjtBQUNIO0FBQ3FCO0FBQ3ZCO0FBYXhDLE1BQU1vQyxlQUErQztRQUFDLEVBQ3BEQyxVQUFVLEVBQ1ZDLFlBQVksSUFBSSxFQUNoQkMsUUFBUSxFQUNSQyxRQUFRLEVBQ1Q7O0lBQ0MsTUFBTSxFQUFFQyxJQUFJLEVBQUUsR0FBR1IsK0RBQU9BO0lBQ3hCLE1BQU0sQ0FBQ1MsZUFBZUMsaUJBQWlCLEdBQUcxQywrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNMkMsU0FBUztRQUNiQyxXQUFXSixDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1HLE1BQU0sS0FBSTtRQUMzQkUsTUFBTUwsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNSyxJQUFJLEtBQUk7UUFDcEJDLE1BQU1OLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTU0sSUFBSSxLQUFJO0lBQ3RCO0lBQ0EsTUFBTUMsV0FBVztJQUVqQiw4Q0FBOEM7SUFDOUMsTUFBTUMscUJBQXFCO1FBQ3pCO1lBQUVDLE1BQU03Qyw4U0FBZUE7WUFBRXlDLE1BQU07WUFBYUssTUFBTSxHQUFZLE9BQVRILFVBQVM7UUFBWTtRQUMxRTtZQUFFRSxNQUFNL0MsOFNBQU1BO1lBQUUyQyxNQUFNO1lBQVVLLE1BQU0sR0FBWSxPQUFUSCxVQUFTO1FBQVM7S0FDNUQ7SUFFRCwyQkFBMkI7SUFDM0IsTUFBTUksbUJBQW1CO1FBQ3ZCO1lBQ0VDLE9BQU87WUFDUEgsTUFBTTlDLDhTQUFLQTtZQUNYa0QsT0FBTztnQkFDTDtvQkFBRUosTUFBTS9CLDhTQUFPQTtvQkFBRTJCLE1BQU07b0JBQVNLLE1BQU0sR0FBWSxPQUFUSCxVQUFTO2dCQUFRO2dCQUMxRDtvQkFBRUUsTUFBTTlDLDhTQUFLQTtvQkFBRTBDLE1BQU07b0JBQVlLLE1BQU0sR0FBWSxPQUFUSCxVQUFTO2dCQUFXO2dCQUM5RDtvQkFBRUUsTUFBTXJDLDhTQUFhQTtvQkFBRWlDLE1BQU07b0JBQVlLLE1BQU0sR0FBWSxPQUFUSCxVQUFTO2dCQUFXO2dCQUN0RTtvQkFBRUUsTUFBTWpDLDhTQUFRQTtvQkFBRTZCLE1BQU07b0JBQVdLLE1BQU0sR0FBWSxPQUFUSCxVQUFTO2dCQUFVO2FBQ2hFO1FBQ0g7UUFDQTtZQUNFSyxPQUFPO1lBQ1BILE1BQU1uQyw4U0FBUUE7WUFDZHVDLE9BQU87Z0JBQ0w7b0JBQUVKLE1BQU16Qyw4U0FBT0E7b0JBQUVxQyxNQUFNO29CQUFVSyxNQUFNLEdBQVksT0FBVEgsVUFBUztnQkFBUztnQkFDNUQ7b0JBQUVFLE1BQU0zQyw4U0FBTUE7b0JBQUV1QyxNQUFNO29CQUFjSyxNQUFNLEdBQVksT0FBVEgsVUFBUztnQkFBWTtnQkFDbEU7b0JBQUVFLE1BQU0xQiw4U0FBU0E7b0JBQUVzQixNQUFNO29CQUFzQkssTUFBTSxHQUFZLE9BQVRILFVBQVM7Z0JBQXFCO2dCQUN0RjtvQkFBRUUsTUFBTTVDLDhTQUFnQkE7b0JBQUV3QyxNQUFNO29CQUFXSyxNQUFNLEdBQVksT0FBVEgsVUFBUztnQkFBUztnQkFDdEU7b0JBQUVFLE1BQU1oRCw4U0FBVUE7b0JBQUU0QyxNQUFNO29CQUFjSyxNQUFNLEdBQVksT0FBVEgsVUFBUztnQkFBYTtnQkFDdkU7b0JBQUVFLE1BQU10Qyw4U0FBV0E7b0JBQUVrQyxNQUFNO29CQUFZSyxNQUFNLEdBQVksT0FBVEgsVUFBUztnQkFBVztnQkFDcEU7b0JBQUVFLE1BQU12Qyw4U0FBWUE7b0JBQUVtQyxNQUFNO29CQUFXSyxNQUFNLEdBQVksT0FBVEgsVUFBUztnQkFBVTtnQkFDbkU7b0JBQUVFLE1BQU0zQiw4U0FBU0E7b0JBQUV1QixNQUFNO29CQUFjSyxNQUFNLEdBQVksT0FBVEgsVUFBUztnQkFBYTthQUN2RTtRQUNIO1FBQ0E7WUFDRUssT0FBTztZQUNQSCxNQUFNaEMsOFNBQVNBO1lBQ2ZvQyxPQUFPO2dCQUNMO29CQUFFSixNQUFNaEMsOFNBQVNBO29CQUFFNEIsTUFBTTtvQkFBaUJLLE1BQU0sR0FBWSxPQUFUSCxVQUFTO2dCQUFnQjtnQkFDNUU7b0JBQUVFLE1BQU1uQyw4U0FBUUE7b0JBQUUrQixNQUFNO29CQUFhSyxNQUFNLEdBQVksT0FBVEgsVUFBUztnQkFBWTthQUNwRTtRQUNIO1FBQ0E7WUFDRUssT0FBTztZQUNQSCxNQUFNNUIsOFNBQVVBO1lBQ2hCZ0MsT0FBTztnQkFDTDtvQkFBRUosTUFBTWxDLDhTQUFVQTtvQkFBRThCLE1BQU07b0JBQWFLLE1BQU0sR0FBWSxPQUFUSCxVQUFTO2dCQUFPO2dCQUNoRTtvQkFBRUUsTUFBTTFDLDhTQUFjQTtvQkFBRXNDLE1BQU07b0JBQW9CSyxNQUFNLEdBQVksT0FBVEgsVUFBUztnQkFBYztnQkFDbEY7b0JBQUVFLE1BQU14Qyw4U0FBS0E7b0JBQUVvQyxNQUFNO29CQUFjSyxNQUFNLEdBQVksT0FBVEgsVUFBUztnQkFBYTthQUNuRTtRQUNIO0tBQ0Q7SUFDQyxNQUFNTyxlQUFlO1FBQ3JCTCxNQUFNcEMsOFNBQVFBO1FBQ2RnQyxNQUFNO1FBQ05LLE1BQU0sR0FBWSxPQUFUSCxVQUFTO0lBQ2xCO0lBRUYscUJBQ0UsOERBQUNkLHlFQUFjQTtrQkFDYiw0RUFBQ3NCO1lBQUlDLFdBQVU7OzhCQUViLDhEQUFDQztvQkFDQ0QsV0FBVTtvQkFDVkUsU0FBUyxJQUFNaEIsaUJBQWlCLENBQUNEOzhCQUVoQ0EsOEJBQWdCLDhEQUFDckIsOFNBQUNBO3dCQUFDdUMsTUFBTTs7Ozs7a0RBQVMsOERBQUN4Qyw4U0FBSUE7d0JBQUN3QyxNQUFNOzs7Ozs7Ozs7Ozs4QkFJakQsOERBQUNKO29CQUNDQyxXQUFXLDJNQUVWLE9BRENmLGdCQUFnQixrQkFBa0I7O3NDQUdwQyw4REFBQ2M7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUMzQixxREFBSUE7Ozs7O3NEQUNMLDhEQUFDTCx3REFBT0E7Ozs7Ozs7Ozs7OzhDQUdWLDhEQUFDQywwREFBU0E7Ozs7OzhDQUVWLDhEQUFDOEI7b0NBQUlDLFdBQVU7O3dDQUVaUixtQkFBbUJZLEdBQUcsQ0FBQyxDQUFDQyxxQkFDdkIsOERBQUNuQyxzREFBYUE7Z0RBRVp1QixNQUFNWSxLQUFLWixJQUFJO2dEQUNmSixNQUFNZ0IsS0FBS2hCLElBQUk7Z0RBQ2ZLLE1BQU1XLEtBQUtYLElBQUk7K0NBSFZXLEtBQUtoQixJQUFJOzs7OztzREFRbEIsOERBQUNVOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDaEMsd0RBQU9BOzs7Ozs7Ozs7O3dDQUlUMkIsaUJBQWlCUyxHQUFHLENBQUMsQ0FBQ0Usc0JBQ3JCLDhEQUFDbkMscURBQVlBO2dEQUVYeUIsT0FBT1UsTUFBTVYsS0FBSztnREFDbEJILE1BQU1hLE1BQU1iLElBQUk7Z0RBQ2hCSSxPQUFPUyxNQUFNVCxLQUFLO2dEQUNsQlUsaUJBQWlCRCxNQUFNVixLQUFLLEtBQUs7K0NBSjVCVSxNQUFNVixLQUFLOzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FXeEIsOERBQUNHOzRCQUFJQyxXQUFVOztnQ0FDWm5CLDJCQUFhLDhEQUFDUCw4Q0FBS0E7b0NBQUNrQyxPQUFPOzs7Ozs7OENBQzVCLDhEQUFDdEMsc0RBQWFBO29DQUNadUIsTUFBTUssYUFBYUwsSUFBSTtvQ0FDdkJKLE1BQU1TLGFBQWFULElBQUk7b0NBQ3ZCSyxNQUFNSSxhQUFhSixJQUFJOzs7Ozs7OENBRXpCLDhEQUFDMUIsd0RBQU9BOzs7Ozs4Q0FDUiw4REFBQ0ksK0NBQU1BO29DQUNMZ0IsV0FBV0QsT0FBT0MsU0FBUztvQ0FDM0JDLE1BQU1GLE9BQU9FLElBQUk7b0NBQ2pCQyxNQUFNSCxPQUFPRyxJQUFJO29DQUNqQlIsVUFBVUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFNbEIsOERBQUNpQjtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDekIsc0RBQWFBO2dDQUNaa0IsTUFBTWIsV0FBV2EsSUFBSTtnQ0FDckJnQixVQUFVN0IsV0FBVzZCLFFBQVE7Z0NBQzdCYixPQUFPaEIsV0FBV2dCLEtBQUs7Z0NBQ3ZCWCxlQUFlQTtnQ0FDZnlCLGVBQWUsSUFBTXhCLGlCQUFpQixDQUFDRDtnQ0FDdkNILFVBQVVBOzs7Ozs7Ozs7OztzQ0FHZCw4REFBQ2lCOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ3RCLG9EQUFXQTtvQ0FBQytCLFVBQVU3QixXQUFXNkIsUUFBUTtvQ0FBRWhCLE1BQU1iLFdBQVdhLElBQUk7Ozs7Ozs4Q0FDakUsOERBQUNrQjtvQ0FBRVgsV0FBVTs4Q0FBMENwQixXQUFXZ0IsS0FBSzs7Ozs7Ozs7Ozs7O3NDQUV6RSw4REFBQ0c7NEJBQUlDLFdBQVU7c0NBQUlqQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLM0I7R0FwS01KOztRQU1hSCwyREFBT0E7OztLQU5wQkc7QUFzS04saUVBQWVBLFlBQVlBLEVBQUMiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZXRcXHNjaG9sYXJpZnlcXGRhc2hib2FyZFxcc3JjXFxjb21wb25lbnRzXFxEYXNoYm9hcmRcXExheW91dHNcXFNjaG9vbExheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQge1xyXG4gIEZpbGVDaGVjazIsIFNjaG9vbCwgVXNlcnMsIExheW91dERhc2hib2FyZCwgQ2hhcnROb0F4ZXNHYW50dCwgQ2xvY2s0LFxyXG4gIEFycm93TGVmdFJpZ2h0LCBQZXJjZW50LCBDb2lucywgUHJlc2VudGF0aW9uLCBOb3RlYm9va1BlbiwgR3JhZHVhdGlvbkNhcCxcclxuICBTZXR0aW5ncywgQm9va09wZW4sIERvbGxhclNpZ24sIFVzZXJQbHVzLCBNZWdhcGhvbmUsIFVzZXJDb2csIE1lbnUsIFgsXHJcbiAgQ3JlZGl0Q2FyZCwgTWlsZXN0b25lLCBVc2VyQ2hlY2tcclxufSBmcm9tIFwibHVjaWRlLXJlYWN0XCI7XHJcbmltcG9ydCBEaXZpZGVyIGZyb20gXCIuLi8uLi93aWRnZXRzL0RpdmlkZXJcIjtcclxuaW1wb3J0IFNlYXJjaEJveCBmcm9tIFwiLi4vLi4vd2lkZ2V0cy9TZWFyY2hCb3hcIjtcclxuaW1wb3J0IFNpZGViYXJCdXR0b24gZnJvbSBcIi4uL1NpZGVOYXZCdXR0b25cIjtcclxuaW1wb3J0IFNpZGViYXJHcm91cCBmcm9tIFwiLi4vU2lkZWJhckdyb3VwXCI7XHJcbmltcG9ydCBBdmF0YXIgZnJvbSBcIi4uL0F2YXRhclwiO1xyXG5pbXBvcnQgTG9nbyBmcm9tIFwiLi4vLi4vd2lkZ2V0cy9Mb2dvXCI7XHJcbmltcG9ydCBHb1BybyBmcm9tIFwiLi4vR29Qcm9cIjtcclxuaW1wb3J0IE5hdmlnYXRpb25CYXIgZnJvbSBcIi4uL05hdmlnYXRpb25CYXJcIjtcclxuaW1wb3J0IHVzZUF1dGggZnJvbSBcIkAvYXBwL2hvb2tzL3VzZUF1dGhcIjtcclxuaW1wb3J0IFByb3RlY3RlZFJvdXRlIGZyb20gXCJAL2NvbXBvbmVudHMvdXRpbHMvUHJvdGVjdGVkUm91dGVcIjtcclxuaW1wb3J0IEJyZWFkY3J1bWJzIGZyb20gXCIuLi9CcmVhZENydW1zXCI7XHJcblxyXG5pbnRlcmZhY2UgRGFzaGJvYXJkTGF5b3V0UHJvcHMge1xyXG4gIG5hdmlnYXRpb246IHtcclxuICAgIGljb246IFJlYWN0LkVsZW1lbnRUeXBlO1xyXG4gICAgYmFzZUhyZWY6IHN0cmluZztcclxuICAgIHRpdGxlOiBzdHJpbmc7XHJcbiAgfTtcclxuICBzaG93R29Qcm8/OiBib29sZWFuO1xyXG4gIG9uTG9nb3V0OiAoKSA9PiB2b2lkO1xyXG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XHJcbn1cclxuXHJcbmNvbnN0IFNjaG9vbExheW91dDogUmVhY3QuRkM8RGFzaGJvYXJkTGF5b3V0UHJvcHM+ID0gKHtcclxuICBuYXZpZ2F0aW9uLFxyXG4gIHNob3dHb1BybyA9IHRydWUsXHJcbiAgb25Mb2dvdXQsXHJcbiAgY2hpbGRyZW4sXHJcbn0pID0+IHtcclxuICBjb25zdCB7IHVzZXIgfSA9IHVzZUF1dGgoKTtcclxuICBjb25zdCBbaXNTaWRlYmFyT3Blbiwgc2V0SXNTaWRlYmFyT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgYXZhdGFyID0ge1xyXG4gICAgYXZhdGFyVXJsOiB1c2VyPy5hdmF0YXIgfHwgXCJodHRwczovL2ltZy5mcmVlcGlrLmNvbS9mcmVlLXBzZC8zZC1pbGx1c3RyYXRpb24tcGVyc29uLXdpdGgtc3VuZ2xhc3Nlc18yMy0yMTQ5NDM2MTg4LmpwZ1wiLFxyXG4gICAgbmFtZTogdXNlcj8ubmFtZSB8fCBcIlNjaG9vbCBBZG1pblwiLFxyXG4gICAgcm9sZTogdXNlcj8ucm9sZSB8fCBcImFkbWluXCIsXHJcbiAgfTtcclxuICBjb25zdCBCQVNFX1VSTCA9IFwiL3NjaG9vbC1hZG1pblwiO1xyXG5cclxuICAvLyBJbmRpdmlkdWFsIG5hdmlnYXRpb24gaXRlbXMgKG5vdCBpbiBncm91cHMpXHJcbiAgY29uc3QgaW5kaXZpZHVhbE5hdkl0ZW1zID0gW1xyXG4gICAgeyBpY29uOiBMYXlvdXREYXNoYm9hcmQsIG5hbWU6IFwiRGFzaGJvYXJkXCIsIGhyZWY6IGAke0JBU0VfVVJMfS9kYXNoYm9hcmRgIH0sXHJcbiAgICB7IGljb246IFNjaG9vbCwgbmFtZTogXCJTY2hvb2xcIiwgaHJlZjogYCR7QkFTRV9VUkx9L3NjaG9vbGAgfSxcclxuICBdO1xyXG5cclxuICAvLyBHcm91cGVkIG5hdmlnYXRpb24gaXRlbXNcclxuICBjb25zdCBuYXZpZ2F0aW9uR3JvdXBzID0gW1xyXG4gICAge1xyXG4gICAgICB0aXRsZTogXCJQZW9wbGUgTWFuYWdlbWVudFwiLFxyXG4gICAgICBpY29uOiBVc2VycyxcclxuICAgICAgaXRlbXM6IFtcclxuICAgICAgICB7IGljb246IFVzZXJDb2csIG5hbWU6IFwiU3RhZmZcIiwgaHJlZjogYCR7QkFTRV9VUkx9L3N0YWZmYCB9LFxyXG4gICAgICAgIHsgaWNvbjogVXNlcnMsIG5hbWU6IFwiVGVhY2hlcnNcIiwgaHJlZjogYCR7QkFTRV9VUkx9L3RlYWNoZXJzYCB9LFxyXG4gICAgICAgIHsgaWNvbjogR3JhZHVhdGlvbkNhcCwgbmFtZTogXCJTdHVkZW50c1wiLCBocmVmOiBgJHtCQVNFX1VSTH0vc3R1ZGVudHNgIH0sXHJcbiAgICAgICAgeyBpY29uOiBVc2VyUGx1cywgbmFtZTogXCJQYXJlbnRzXCIsIGhyZWY6IGAke0JBU0VfVVJMfS9wYXJlbnRzYCB9LFxyXG4gICAgICBdXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICB0aXRsZTogXCJBY2FkZW1pYyBSZWNvcmRzXCIsXHJcbiAgICAgIGljb246IEJvb2tPcGVuLFxyXG4gICAgICBpdGVtczogW1xyXG4gICAgICAgIHsgaWNvbjogUGVyY2VudCwgbmFtZTogXCJHcmFkZXNcIiwgaHJlZjogYCR7QkFTRV9VUkx9L2dyYWRlc2AgfSxcclxuICAgICAgICB7IGljb246IENsb2NrNCwgbmFtZTogXCJUaW1lIFRhYmxlXCIsIGhyZWY6IGAke0JBU0VfVVJMfS90aW1ldGFibGVgIH0sXHJcbiAgICAgICAgeyBpY29uOiBVc2VyQ2hlY2ssIG5hbWU6IFwiVGVhY2hlciBBc3NpZ25tZW50XCIsIGhyZWY6IGAke0JBU0VfVVJMfS90ZWFjaGVyLWFzc2lnbm1lbnRgIH0sXHJcbiAgICAgICAgeyBpY29uOiBDaGFydE5vQXhlc0dhbnR0LCBuYW1lOiBcIlBlcmlvZHNcIiwgaHJlZjogYCR7QkFTRV9VUkx9L3BlcmlvZGAgfSxcclxuICAgICAgICB7IGljb246IEZpbGVDaGVjazIsIG5hbWU6IFwiQXR0ZW5kYW5jZVwiLCBocmVmOiBgJHtCQVNFX1VSTH0vYXR0ZW5kYW5jZWAgfSxcclxuICAgICAgICB7IGljb246IE5vdGVib29rUGVuLCBuYW1lOiBcIlN1YmplY3RzXCIsIGhyZWY6IGAke0JBU0VfVVJMfS9zdWJqZWN0c2AgfSxcclxuICAgICAgICB7IGljb246IFByZXNlbnRhdGlvbiwgbmFtZTogXCJDbGFzc2VzXCIsIGhyZWY6IGAke0JBU0VfVVJMfS9jbGFzc2VzYCB9LFxyXG4gICAgICAgIHsgaWNvbjogTWlsZXN0b25lLCBuYW1lOiBcIkRpc2NpcGxpbmVcIiwgaHJlZjogYCR7QkFTRV9VUkx9L2Rpc2NpcGxpbmVgIH0sXHJcbiAgICAgIF1cclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIHRpdGxlOiBcIkNvbW11bmljYXRpb25zXCIsXHJcbiAgICAgIGljb246IE1lZ2FwaG9uZSxcclxuICAgICAgaXRlbXM6IFtcclxuICAgICAgICB7IGljb246IE1lZ2FwaG9uZSwgbmFtZTogXCJBbm5vdW5jZW1lbnRzXCIsIGhyZWY6IGAke0JBU0VfVVJMfS9hbm5vdW5jZW1lbnRzYCB9LFxyXG4gICAgICAgIHsgaWNvbjogQm9va09wZW4sIG5hbWU6IFwiUmVzb3VyY2VzXCIsIGhyZWY6IGAke0JBU0VfVVJMfS9yZXNvdXJjZXNgIH0sXHJcbiAgICAgIF1cclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIHRpdGxlOiBcIkZpbmFuY2lhbFwiLFxyXG4gICAgICBpY29uOiBDcmVkaXRDYXJkLFxyXG4gICAgICBpdGVtczogW1xyXG4gICAgICAgIHsgaWNvbjogRG9sbGFyU2lnbiwgbmFtZTogXCJGZWUgVHlwZXNcIiwgaHJlZjogYCR7QkFTRV9VUkx9L2ZlZXNgIH0sXHJcbiAgICAgICAgeyBpY29uOiBBcnJvd0xlZnRSaWdodCwgbmFtZTogXCJGZWUgVHJhbnNhY3Rpb25zXCIsIGhyZWY6IGAke0JBU0VfVVJMfS90cmFuc2FjdGlvbmAgfSxcclxuICAgICAgICB7IGljb246IENvaW5zLCBuYW1lOiBcIkJ1eSBDcmVkaXRcIiwgaHJlZjogYCR7QkFTRV9VUkx9L2J1eS1jcmVkaXRgIH0sXHJcbiAgICAgIF1cclxuICAgIH1cclxuICBdO1xyXG4gICAgY29uc3Qgc2V0dGluZ3NMaW5rID0ge1xyXG4gICAgaWNvbjogU2V0dGluZ3MsXHJcbiAgICBuYW1lOiBcIlNldHRpbmdzXCIsXHJcbiAgICBocmVmOiBgJHtCQVNFX1VSTH0vc2V0dGluZ3NgXHJcbiAgICB9O1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPFByb3RlY3RlZFJvdXRlPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaC1zY3JlZW4gb3ZlcmZsb3ctaGlkZGVuIHNtOnAtNFwiPlxyXG4gICAgICAgIHsvKiBNb2JpbGUgU2lkZWJhciBUb2dnbGUgKi99XHJcbiAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgY2xhc3NOYW1lPVwibWQ6aGlkZGVuIHAtMiBiZy1mb3JlZ3JvdW5kIHRleHQtYmFja2dyb3VuZCByb3VuZGVkLWxnIGZpeGVkIHRvcC00IGxlZnQtNCB6LTUwXCJcclxuICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzU2lkZWJhck9wZW4oIWlzU2lkZWJhck9wZW4pfVxyXG4gICAgICAgID5cclxuICAgICAgICAgIHtpc1NpZGViYXJPcGVuID8gPFggc2l6ZT17MjR9IC8+IDogPE1lbnUgc2l6ZT17MjR9IC8+fVxyXG4gICAgICAgIDwvYnV0dG9uPlxyXG5cclxuICAgICAgICB7LyogU2lkZWJhciAqL31cclxuICAgICAgICA8ZGl2XHJcbiAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IHctWzI5MHB4XSBmbGV4LWNvbCBib3JkZXIgYm9yZGVyLWdyYXktMzAwIGRhcks6Ym9yZGVyIGRhcms6Ym9yZGVyLWdyYXktODAwIGgtZnVsbCBzaGFkb3ctbGcgcC0yIHJvdW5kZWQtbGcgZml4ZWQgaW5zZXQteS0wIGxlZnQtMCB6LTQwIGJnLXdpZGdldCB0cmFuc2l0aW9uLXRyYW5zZm9ybSBsZzpyZWxhdGl2ZSBsZzp0cmFuc2xhdGUteC0wICR7XHJcbiAgICAgICAgICAgIGlzU2lkZWJhck9wZW4gPyBcInRyYW5zbGF0ZS14LTBcIiA6IFwiLXRyYW5zbGF0ZS14LWZ1bGxcIlxyXG4gICAgICAgICAgfWB9XHJcbiAgICAgICAgPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGdhcC0zIG92ZXJmbG93LWF1dG8gc3VidGxlLXNjcm9sbGJhclwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIGdhcC0yIG15LTQgXCI+XHJcbiAgICAgICAgICAgICAgPExvZ28gLz5cclxuICAgICAgICAgICAgICA8RGl2aWRlciAvPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgIDxTZWFyY2hCb3ggLz5cclxuXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBnYXAtMVwiPlxyXG4gICAgICAgICAgICAgIHsvKiBJbmRpdmlkdWFsIE5hdmlnYXRpb24gSXRlbXMgKi99XHJcbiAgICAgICAgICAgICAge2luZGl2aWR1YWxOYXZJdGVtcy5tYXAoKGl0ZW0pID0+IChcclxuICAgICAgICAgICAgICAgIDxTaWRlYmFyQnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgIGtleT17aXRlbS5uYW1lfVxyXG4gICAgICAgICAgICAgICAgICBpY29uPXtpdGVtLmljb259XHJcbiAgICAgICAgICAgICAgICAgIG5hbWU9e2l0ZW0ubmFtZX1cclxuICAgICAgICAgICAgICAgICAgaHJlZj17aXRlbS5ocmVmfVxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICApKX1cclxuXHJcbiAgICAgICAgICAgICAgey8qIERpdmlkZXIgKi99XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJteS0yXCI+XHJcbiAgICAgICAgICAgICAgICA8RGl2aWRlciAvPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICB7LyogR3JvdXBlZCBOYXZpZ2F0aW9uIEl0ZW1zICovfVxyXG4gICAgICAgICAgICAgIHtuYXZpZ2F0aW9uR3JvdXBzLm1hcCgoZ3JvdXApID0+IChcclxuICAgICAgICAgICAgICAgIDxTaWRlYmFyR3JvdXBcclxuICAgICAgICAgICAgICAgICAga2V5PXtncm91cC50aXRsZX1cclxuICAgICAgICAgICAgICAgICAgdGl0bGU9e2dyb3VwLnRpdGxlfVxyXG4gICAgICAgICAgICAgICAgICBpY29uPXtncm91cC5pY29ufVxyXG4gICAgICAgICAgICAgICAgICBpdGVtcz17Z3JvdXAuaXRlbXN9XHJcbiAgICAgICAgICAgICAgICAgIGRlZmF1bHRFeHBhbmRlZD17Z3JvdXAudGl0bGUgPT09IFwiQWNhZGVtaWMgUmVjb3Jkc1wifSAvLyBFeHBhbmQgQWNhZGVtaWMgUmVjb3JkcyBieSBkZWZhdWx0XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIHsvKiBGb290ZXIgKi99XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LWF1dG8gZmxleCBmbGV4LWNvbCBnYXAtM1wiPlxyXG4gICAgICAgICAgICB7c2hvd0dvUHJvICYmIDxHb1BybyB2aXNpYmxlIC8+fVxyXG4gICAgICAgICAgICA8U2lkZWJhckJ1dHRvblxyXG4gICAgICAgICAgICAgIGljb249e3NldHRpbmdzTGluay5pY29ufVxyXG4gICAgICAgICAgICAgIG5hbWU9e3NldHRpbmdzTGluay5uYW1lfVxyXG4gICAgICAgICAgICAgIGhyZWY9e3NldHRpbmdzTGluay5ocmVmfVxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8RGl2aWRlciAvPlxyXG4gICAgICAgICAgICA8QXZhdGFyXHJcbiAgICAgICAgICAgICAgYXZhdGFyVXJsPXthdmF0YXIuYXZhdGFyVXJsfVxyXG4gICAgICAgICAgICAgIG5hbWU9e2F2YXRhci5uYW1lfVxyXG4gICAgICAgICAgICAgIHJvbGU9e2F2YXRhci5yb2xlfVxyXG4gICAgICAgICAgICAgIG9uTG9nb3V0PXtvbkxvZ291dH1cclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICB7LyogTWFpbiBDb250ZW50ICovfVxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNtOnB4LTYgcHgtMiBweS0yIHctZnVsbCBmbGV4IGZsZXgtY29sIGdhcC00IGxnOnctWzgwJV0gb3ZlcmZsb3ctYXV0byBjdXN0b20tc2Nyb2xsYmFyXCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzdGlja3kgdG9wLTAgei0yMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gICBcIj5cclxuICAgICAgICAgIDxOYXZpZ2F0aW9uQmFyXHJcbiAgICAgICAgICAgIGljb249e25hdmlnYXRpb24uaWNvbn1cclxuICAgICAgICAgICAgYmFzZUhyZWY9e25hdmlnYXRpb24uYmFzZUhyZWZ9XHJcbiAgICAgICAgICAgIHRpdGxlPXtuYXZpZ2F0aW9uLnRpdGxlfVxyXG4gICAgICAgICAgICBpc1NpZGViYXJPcGVuPXtpc1NpZGViYXJPcGVufVxyXG4gICAgICAgICAgICB0b2dnbGVTaWRlYmFyPXsoKSA9PiBzZXRJc1NpZGViYXJPcGVuKCFpc1NpZGViYXJPcGVuKX1cclxuICAgICAgICAgICAgb25Mb2dvdXQ9e29uTG9nb3V0fVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggbGc6aGlkZGVuIGZsZXgtY29sIGdhcC0yXCI+XHJcbiAgICAgICAgICA8QnJlYWRjcnVtYnMgYmFzZUhyZWY9e25hdmlnYXRpb24uYmFzZUhyZWZ9IGljb249e25hdmlnYXRpb24uaWNvbn0gLz5cclxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtc2VtaWJvbGQgdGV4dC1mb3JlZ3JvdW5kXCI+e25hdmlnYXRpb24udGl0bGV9PC9wPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiXCI+e2NoaWxkcmVufTwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L1Byb3RlY3RlZFJvdXRlPlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBTY2hvb2xMYXlvdXQ7XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwiRmlsZUNoZWNrMiIsIlNjaG9vbCIsIlVzZXJzIiwiTGF5b3V0RGFzaGJvYXJkIiwiQ2hhcnROb0F4ZXNHYW50dCIsIkNsb2NrNCIsIkFycm93TGVmdFJpZ2h0IiwiUGVyY2VudCIsIkNvaW5zIiwiUHJlc2VudGF0aW9uIiwiTm90ZWJvb2tQZW4iLCJHcmFkdWF0aW9uQ2FwIiwiU2V0dGluZ3MiLCJCb29rT3BlbiIsIkRvbGxhclNpZ24iLCJVc2VyUGx1cyIsIk1lZ2FwaG9uZSIsIlVzZXJDb2ciLCJNZW51IiwiWCIsIkNyZWRpdENhcmQiLCJNaWxlc3RvbmUiLCJVc2VyQ2hlY2siLCJEaXZpZGVyIiwiU2VhcmNoQm94IiwiU2lkZWJhckJ1dHRvbiIsIlNpZGViYXJHcm91cCIsIkF2YXRhciIsIkxvZ28iLCJHb1BybyIsIk5hdmlnYXRpb25CYXIiLCJ1c2VBdXRoIiwiUHJvdGVjdGVkUm91dGUiLCJCcmVhZGNydW1icyIsIlNjaG9vbExheW91dCIsIm5hdmlnYXRpb24iLCJzaG93R29Qcm8iLCJvbkxvZ291dCIsImNoaWxkcmVuIiwidXNlciIsImlzU2lkZWJhck9wZW4iLCJzZXRJc1NpZGViYXJPcGVuIiwiYXZhdGFyIiwiYXZhdGFyVXJsIiwibmFtZSIsInJvbGUiLCJCQVNFX1VSTCIsImluZGl2aWR1YWxOYXZJdGVtcyIsImljb24iLCJocmVmIiwibmF2aWdhdGlvbkdyb3VwcyIsInRpdGxlIiwiaXRlbXMiLCJzZXR0aW5nc0xpbmsiLCJkaXYiLCJjbGFzc05hbWUiLCJidXR0b24iLCJvbkNsaWNrIiwic2l6ZSIsIm1hcCIsIml0ZW0iLCJncm91cCIsImRlZmF1bHRFeHBhbmRlZCIsInZpc2libGUiLCJiYXNlSHJlZiIsInRvZ2dsZVNpZGViYXIiLCJwIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Dashboard/Layouts/SchoolLayout.tsx\n"));

/***/ })

});