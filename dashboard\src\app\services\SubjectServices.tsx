import { BASE_API_URL } from "./AuthContext";
import { getTokenFromCookie } from "./UserServices";
import { SubjectSchema } from "../models/Subject";
import { SubjectUpdateSchema } from "../models/Subject";

// Get all subjects
export async function getAllSubjects(): Promise<SubjectSchema[]> {
  const token = getTokenFromCookie("idToken");
  const res = await fetch(`${BASE_API_URL}/subject/get-subjects`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  });
  if (!res.ok) throw new Error("Failed to fetch subjects");
  const data = await res.json();
  return data.map((subject: any) => ({
    _id: subject._id,
    subject_id: subject.subject_id,
    subject_code: subject.subject_code,
    compulsory: subject.compulsory,
    school_id: subject.school_id,
    class_id: subject.class_id,
    name: subject.name,
    description: subject.description,
    coefficient: subject.coefficient,
    department: subject.department || "",
    createdAt: subject.createdAt,
    updatedAt: subject.updatedAt,
  }));
}

// Get subject by ID
export async function getSubjectById(id: string): Promise<SubjectSchema> {
  const token = getTokenFromCookie("idToken");
  const res = await fetch(`${BASE_API_URL}/subject/get-subject/${id}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  });
  if (!res.ok) throw new Error("Failed to fetch subject");
  const subject = await res.json();
  return {
    _id: subject._id,
    subject_id: subject.subject_id,
    subject_code: subject.subject_code,
    compulsory: subject.compulsory,
    school_id: subject.school_id,
    class_id: subject.class_id,
    name: subject.name,
    description: subject.description,
    coefficient: subject.coefficient,
    department: subject.department || "",
    createdAt: subject.createdAt,
    updatedAt: subject.updatedAt,
  };
}

// Create a new subject
export async function createSubject(data: SubjectUpdateSchema) {
  const token = getTokenFromCookie("idToken");
  const res = await fetch(`${BASE_API_URL}/subject/create-subject`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(data),
  });
  if (!res.ok) {
    let msg = "Failed to create subject";
    try {
      const err = await res.json();
      msg = err.message || msg;
    } catch {}
    throw new Error(msg);
  }
  return await res.json();
}

// Update subject by ID
export async function updateSubject(id: string, data: Partial<SubjectUpdateSchema>) {
  const token = getTokenFromCookie("idToken");
  const res = await fetch(`${BASE_API_URL}/subject/update-subject/${id}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(data),
  });
  if (!res.ok) {
    let msg = "Failed to update subject";
    try {
      const err = await res.json();
      msg = err.message || msg;
    } catch {}
    throw new Error(msg);
  }
  return await res.json();
}

// Delete subject by ID
export async function deleteSubject(id: string) {
  const token = getTokenFromCookie("idToken");
  const res = await fetch(`${BASE_API_URL}/subject/delete-subject/${id}`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  });
  if (!res.ok) {
    let msg = "Failed to delete subject";
    try {
      const err = await res.json();
      msg = err.message || msg;
    } catch {}
    throw new Error(msg);
  }
  return await res.json();
}

// Delete multiple subjects by IDs
export async function deleteMultipleSubjects(ids: string[]) {
  const token = getTokenFromCookie("idToken");
  const res = await fetch(`${BASE_API_URL}/subject/delete-subjects`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify({ ids }),
  });
  if (!res.ok) {
    let msg = "Failed to delete subjects";
    try {
      const err = await res.json();
      msg = err.message || msg;
    } catch {}
    throw new Error(msg);
  }
  return await res.json();
}

// Delete all subjects
export async function deleteAllSubjects() {
  const token = getTokenFromCookie("idToken");
  const res = await fetch(`${BASE_API_URL}/subject/delete-all-subjects`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  });
  if (!res.ok) {
    throw new Error("Failed to delete all subjects");
  }
  return await res.json();
}

// Get subjects by school ID
export async function getSubjectsBySchoolId(schoolId: string): Promise<SubjectSchema[]> {
  const token = getTokenFromCookie("idToken");
  const res = await fetch(`${BASE_API_URL}/subject/get-subject-by-school/${schoolId}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  });
  if (!res.ok) throw new Error("Failed to fetch subjects by school ID");
  const data = await res.json();
  return data.map((subject: any) => ({
    _id: subject._id,
    subject_id: subject.subject_id,
    subject_code: subject.subject_code,
    compulsory: subject.compulsory,
    school_id: subject.school_id,
    class_id: subject.class_id,
    name: subject.name,
    description: subject.description,
    coefficient: subject.coefficient,
    department: subject.department || "",
    createdAt: subject.createdAt,
    updatedAt: subject.updatedAt,
  }));
}

// Get subjects by class ID
export async function getSubjectsByClassId(classId: string): Promise<SubjectSchema[]> {
  const token = getTokenFromCookie("idToken");
  const res = await fetch(`${BASE_API_URL}/subject/get-subject-by-class/${classId}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  });
  if (!res.ok) throw new Error("Failed to fetch subjects by class ID");
  const data = await res.json();
  return data.map((subject: any) => ({
    _id: subject._id,
    subject_id: subject.subject_id,
    subject_code: subject.subject_code,
    compulsory: subject.compulsory,
    school_id: subject.school_id,
    class_id: subject.class_id,
    name: subject.name,
    description: subject.description,
    coefficient: subject.coefficient,
    department: subject.department || "",
    createdAt: subject.createdAt,
    updatedAt: subject.updatedAt,
  }));
}

