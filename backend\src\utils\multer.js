const multer = require('multer');
const { CloudinaryStorage } = require('multer-storage-cloudinary');
const cloudinary = require('../utils/cloudinary');

const storage = new CloudinaryStorage({
  cloudinary,
  params: async (req) => {
    const studentId = req.params.id || 'unknown';
    return {
      folder: `students/${studentId}`,
      allowed_formats: ['jpg', 'jpeg', 'png'],
      public_id: 'avatar',
    };
  },
});

const upload = multer({ storage });

module.exports = upload;
