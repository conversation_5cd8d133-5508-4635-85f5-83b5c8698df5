"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/timetable/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js":
/*!*******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/trending-down.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ TrendingDown)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.485.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"polyline\",\n        {\n            points: \"22 17 13.5 8.5 8.5 13.5 2 7\",\n            key: \"1r2t7k\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"16 17 22 17 22 11\",\n            key: \"11uiuu\"\n        }\n    ]\n];\nconst TrendingDown = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"trending-down\", __iconNode);\n //# sourceMappingURL=trending-down.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/trending-up.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ TrendingUp)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.485.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"polyline\",\n        {\n            points: \"22 7 13.5 15.5 8.5 10.5 2 17\",\n            key: \"126l90\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"16 7 22 7 22 13\",\n            key: \"kwv8wd\"\n        }\n    ]\n];\nconst TrendingUp = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"trending-up\", __iconNode);\n //# sourceMappingURL=trending-up.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/services/SchoolServices.tsx":
/*!*********************************************!*\
  !*** ./src/app/services/SchoolServices.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSchool: () => (/* binding */ createSchool),\n/* harmony export */   deleteAllSchools: () => (/* binding */ deleteAllSchools),\n/* harmony export */   deleteMultipleSchools: () => (/* binding */ deleteMultipleSchools),\n/* harmony export */   deleteSchool: () => (/* binding */ deleteSchool),\n/* harmony export */   getSchoolById: () => (/* binding */ getSchoolById),\n/* harmony export */   getSchoolBy_id: () => (/* binding */ getSchoolBy_id),\n/* harmony export */   getSchoolCountChange: () => (/* binding */ getSchoolCountChange),\n/* harmony export */   getSchoolCredits: () => (/* binding */ getSchoolCredits),\n/* harmony export */   getSchoolPerformance: () => (/* binding */ getSchoolPerformance),\n/* harmony export */   getSchools: () => (/* binding */ getSchools),\n/* harmony export */   getTotalSchools: () => (/* binding */ getTotalSchools),\n/* harmony export */   updateSchool: () => (/* binding */ updateSchool)\n/* harmony export */ });\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./AuthContext */ \"(app-pages-browser)/./src/app/services/AuthContext.tsx\");\n/* harmony import */ var _UserServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./UserServices */ \"(app-pages-browser)/./src/app/services/UserServices.tsx\");\n\n\nasync function getSchools() {\n    try {\n        const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/get-schools\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching schools:\", response.statusText);\n            throw new Error(\"Failed to fetch schools data\");\n        }\n        const schoolsList = await response.json();\n        const schools = schoolsList.map((school)=>{\n            return {\n                _id: school._id,\n                school_id: school.school_id,\n                name: school.name,\n                email: school.email,\n                address: school.address,\n                website: school.website,\n                phone_number: school.phone_number,\n                principal_name: school.principal_name,\n                established_year: school.established_year,\n                description: school.description\n            };\n        });\n        return schools;\n    } catch (error) {\n        console.error(\"Error fetching schools:\", error);\n        throw new Error(\"Failed to fetch schools data\");\n    }\n}\nasync function getSchoolById(schoolId) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/get-school/\").concat(schoolId), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat((0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching school:\", response.statusText);\n        throw new Error(\"Failed to fetch school data\");\n    }\n    const data = await response.json();\n    const school = {\n        school_id: data.school_id,\n        credit: data.credit,\n        name: data.name,\n        email: data.email,\n        address: data.address,\n        website: data.website,\n        phone_number: data.phone_number,\n        principal_name: data.principal_name,\n        established_year: data.established_year,\n        description: data.description\n    };\n    return school;\n}\nasync function getSchoolBy_id(schoolId) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/get-school_id/\").concat(schoolId), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat((0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching school:\", response.statusText);\n        throw new Error(\"Failed to fetch school data\");\n    }\n    const data = await response.json();\n    const school = {\n        _id: data._id,\n        school_id: data.school_id,\n        credit: data.credit,\n        name: data.name,\n        email: data.email,\n        address: data.address,\n        website: data.website,\n        phone_number: data.phone_number,\n        principal_name: data.principal_name,\n        established_year: data.established_year,\n        description: data.description\n    };\n    return school;\n}\nasync function createSchool(schoolData) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/create-school\"), {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat((0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\"))\n        },\n        body: JSON.stringify(schoolData)\n    });\n    if (!response.ok) {\n        console.error(\"Error creating school:\", response.statusText);\n        throw new Error(\"Failed to create school data\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function updateSchool(schoolId, schoolData) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/update-school/\").concat(schoolId), {\n        method: \"PUT\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat((0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\"))\n        },\n        body: JSON.stringify(schoolData)\n    });\n    if (!response.ok) {\n        console.error(\"Error updating school:\", response.statusText);\n        throw new Error(\"Failed to update school data\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function deleteSchool(schoolId) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/delete-school/\").concat(schoolId), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat((0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting school:\", response.statusText);\n        throw new Error(\"Failed to delete school data\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function deleteMultipleSchools(schoolIds) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/delete-schools\"), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat((0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\"))\n        },\n        body: JSON.stringify({\n            ids: schoolIds\n        })\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting multiple schools:\", response.statusText);\n        throw new Error(\"Failed to delete multiple schools\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function deleteAllSchools() {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/delete-all-schools\"), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat((0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting all schools:\", response.statusText);\n        throw new Error(\"Failed to delete all schools\");\n    }\n    const data = await response.json();\n    return data;\n}\n// Get total number of schools\nasync function getTotalSchools() {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/total-schools\"), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching total schools:\", response.statusText);\n        throw new Error(\"Failed to fetch total schools\");\n    }\n    const data = await response.json();\n    return data.totalSchools;\n}\n// Get schools created this month and percentage change from last month\nasync function getSchoolCountChange() {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/schools-count-change\"), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching school count change:\", response.statusText);\n        throw new Error(\"Failed to fetch school count change\");\n    }\n    const data = await response.json();\n    return {\n        totalThisMonth: data.totalSchoolsThisMonth,\n        percentageChange: data.percentageChange\n    };\n}\nasync function getSchoolPerformance() {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/performance\"), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching performance metrics:\", response.statusText);\n        throw new Error(\"Failed to fetch school performance metrics\");\n    }\n    const data = await response.json();\n    return data;\n}\n// Get school credits/points\nasync function getSchoolCredits(schoolId) {\n    var _data_school;\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/get-school/\").concat(schoolId), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching school credits:\", response.statusText);\n        throw new Error(\"Failed to fetch school credits\");\n    }\n    const data = await response.json();\n    return ((_data_school = data.school) === null || _data_school === void 0 ? void 0 : _data_school.credit) || 0;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/services/SchoolServices.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Dashboard/NavigationBar.tsx":
/*!****************************************************!*\
  !*** ./src/components/Dashboard/NavigationBar.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NavigationBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _widgets_SearchBox__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../widgets/SearchBox */ \"(app-pages-browser)/./src/components/widgets/SearchBox.tsx\");\n/* harmony import */ var _barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _BreadCrums__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./BreadCrums */ \"(app-pages-browser)/./src/components/Dashboard/BreadCrums.tsx\");\n/* harmony import */ var _widgets_UserMenuModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../widgets/UserMenuModal */ \"(app-pages-browser)/./src/components/widgets/UserMenuModal.tsx\");\n/* harmony import */ var _widgets_NotificationCenter__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../widgets/NotificationCenter */ \"(app-pages-browser)/./src/components/widgets/NotificationCenter.tsx\");\n/* harmony import */ var _widgets_SchoolPoints__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../widgets/SchoolPoints */ \"(app-pages-browser)/./src/components/widgets/SchoolPoints.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction NavigationBar(param) {\n    let { icon: Icon, baseHref, title, toggleSidebar, isSidebarOpen, onLogout } = param;\n    _s();\n    const [isDarkMode, setIsDarkMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user, logout } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_7__[\"default\"])();\n    // Vérifier le thème au chargement\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NavigationBar.useEffect\": ()=>{\n            const prefersDark = window.matchMedia(\"(prefers-color-scheme: dark)\").matches;\n            setIsDarkMode(prefersDark);\n            if (prefersDark) {\n                document.documentElement.classList.add(\"dark\");\n            }\n        }\n    }[\"NavigationBar.useEffect\"], []);\n    // Basculer entre mode clair et sombre\n    const toggleTheme = ()=>{\n        setIsDarkMode(!isDarkMode);\n        if (isDarkMode) {\n            document.documentElement.classList.remove(\"dark\");\n        } else {\n            document.documentElement.classList.add(\"dark\");\n        }\n    };\n    // Gérer la déconnexion\n    const handleSignOut = ()=>{\n        onLogout();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full flex items-center justify-between p-4 bg-glassy shadow-md border md:border-none md:shadow-none border-gray-300 darK:border dark:border-gray-800\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                id: \"mobile-sidebar-toggle\",\n                className: \"lg:hidden p-2  text-foreground rounded-lg  top-4 left-4 z-30\",\n                onClick: ()=>toggleSidebar && toggleSidebar(),\n                children: isSidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 26\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 44\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:flex flex-col gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BreadCrums__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        baseHref: baseHref,\n                        icon: Icon\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-2xl font-semibold text-foreground\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_SearchBox__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_SchoolPoints__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"hidden md:flex\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_NotificationCenter__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"hidden lg:block\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_UserMenuModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        avatarUrl: (user === null || user === void 0 ? void 0 : user.avatar) || \"https://img.freepik.com/free-psd/3d-illustration-person-with-sunglasses_23-2149436188.jpg\",\n                        userName: (user === null || user === void 0 ? void 0 : user.name) || \"\",\n                        onSignOut: logout,\n                        onToggleTheme: toggleTheme,\n                        isDarkMode: isDarkMode\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n_s(NavigationBar, \"24OZTdcMQjo2aNvu0Uo7vpQ5B80=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    ];\n});\n_c = NavigationBar;\nvar _c;\n$RefreshReg$(_c, \"NavigationBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Dashboard/NavigationBar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/widgets/SchoolPoints.tsx":
/*!*************************************************!*\
  !*** ./src/components/widgets/SchoolPoints.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SchoolPoints)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Coins,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Coins,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.js\");\n/* harmony import */ var _barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Coins,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_SchoolServices__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/services/SchoolServices */ \"(app-pages-browser)/./src/app/services/SchoolServices.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction SchoolPoints(param) {\n    let { className = \"\" } = param;\n    var _user_school_ids;\n    _s();\n    const { user } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    const [credits, setCredits] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Get school ID from user\n    const schoolId = (user === null || user === void 0 ? void 0 : (_user_school_ids = user.school_ids) === null || _user_school_ids === void 0 ? void 0 : _user_school_ids[0]) || (user === null || user === void 0 ? void 0 : user.school_id);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SchoolPoints.useEffect\": ()=>{\n            const fetchCredits = {\n                \"SchoolPoints.useEffect.fetchCredits\": async ()=>{\n                    if (!schoolId) {\n                        setLoading(false);\n                        return;\n                    }\n                    try {\n                        setLoading(true);\n                        const schoolCredits = await (0,_app_services_SchoolServices__WEBPACK_IMPORTED_MODULE_2__.getSchoolCredits)(schoolId);\n                        setCredits(schoolCredits);\n                        setError(null);\n                    } catch (err) {\n                        console.error(\"Error fetching school credits:\", err);\n                        setError(\"Failed to load credits\");\n                        setCredits(0);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"SchoolPoints.useEffect.fetchCredits\"];\n            fetchCredits();\n        }\n    }[\"SchoolPoints.useEffect\"], [\n        schoolId\n    ]);\n    // Format number with commas\n    const formatNumber = (num)=>{\n        return new Intl.NumberFormat('en-US').format(num);\n    };\n    // Get color based on credit amount\n    const getCreditColor = ()=>{\n        if (credits >= 1000) return \"text-green-600 dark:text-green-400\";\n        if (credits >= 500) return \"text-blue-600 dark:text-blue-400\";\n        if (credits >= 100) return \"text-yellow-600 dark:text-yellow-400\";\n        return \"text-red-600 dark:text-red-400\";\n    };\n    // Get icon based on credit amount\n    const getCreditIcon = ()=>{\n        if (credits >= 500) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n            lineNumber: 61,\n            columnNumber: 32\n        }, this);\n        if (credits >= 100) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n            lineNumber: 62,\n            columnNumber: 32\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n            lineNumber: 63,\n            columnNumber: 12\n        }, this);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-2 \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden sm:block\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-2 text-red-500 \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-5 w-5\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"hidden sm:block text-sm\",\n                    children: \"Error\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n        initial: {\n            opacity: 0,\n            scale: 0.95\n        },\n        animate: {\n            opacity: 1,\n            scale: 1\n        },\n        className: \"flex items-center space-x-2 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sm:hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    whileHover: {\n                        scale: 1.1\n                    },\n                    whileTap: {\n                        scale: 0.95\n                    },\n                    className: \"relative p-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg shadow-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-5 w-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n                            children: credits > 999 ? '999+' : credits\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden sm:flex items-center space-x-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    whileHover: {\n                        scale: 1.05\n                    },\n                    className: \"flex items-center space-x-2 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 px-3 py-2 rounded-lg border border-yellow-200 dark:border-yellow-800 shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-1.5 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                getCreditIcon()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-bold text-lg \".concat(getCreditColor()),\n                                            children: formatNumber(credits)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-foreground/60 font-medium\",\n                                            children: \"pts\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-foreground/50\",\n                                    children: \"School Credits\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden xs:flex sm:hidden items-center space-x-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    whileHover: {\n                        scale: 1.05\n                    },\n                    className: \"flex items-center space-x-2 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 px-2 py-1.5 rounded-lg border border-yellow-200 dark:border-yellow-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-1 bg-gradient-to-r from-yellow-400 to-orange-500 rounded\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-3 w-3 text-white\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-semibold text-sm \".concat(getCreditColor()),\n                            children: formatNumber(credits)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, this);\n}\n_s(SchoolPoints, \"tdA9imYFgZlUC09PQ1mXR0gK1TI=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    ];\n});\n_c = SchoolPoints;\nvar _c;\n$RefreshReg$(_c, \"SchoolPoints\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/widgets/SchoolPoints.tsx\n"));

/***/ })

});