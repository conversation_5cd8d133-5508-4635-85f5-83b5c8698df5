import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import useAuth from '@/app/hooks/useAuth';
import { isTokenExpired } from '@/app/utils/httpInterceptor';
import Cookies from 'js-cookie';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRoles?: string[];
  fallback?: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requiredRoles = [], 
  fallback = <div>Loading...</div> 
}) => {
  const { user, isAuthenticated, loading, forceLogout } = useAuth();
  const router = useRouter();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Vérifier si le token existe
        const token = Cookies.get("idToken");
        if (!token) {
          console.warn("ProtectedRoute: No token found");
          router.push('/login');
          return;
        }

        // Vérifier si le token est expiré
        if (isTokenExpired()) {
          console.warn("ProtectedRoute: Token expired");
          forceLogout();
          return;
        }

        // Si on a fini de charger et qu'on n'est pas authentifié
        if (!loading && !isAuthenticated) {
          console.warn("ProtectedRoute: User not authenticated");
          router.push('/login');
          return;
        }

        // Vérifier les rôles si spécifiés
        if (user && requiredRoles.length > 0) {
          const hasRequiredRole = requiredRoles.some(role => 
            user.role?.toLowerCase() === role.toLowerCase()
          );
          
          if (!hasRequiredRole) {
            console.warn("ProtectedRoute: Insufficient permissions");
            router.push('/unauthorized');
            return;
          }
        }

        setIsChecking(false);
      } catch (error) {
        console.error("ProtectedRoute: Auth check failed", error);
        router.push('/login');
      }
    };

    // Ne vérifier que si on a fini de charger
    if (!loading) {
      checkAuth();
    }
  }, [user, isAuthenticated, loading, router, requiredRoles, forceLogout]);

  // Afficher le fallback pendant le chargement
  if (loading || isChecking) {
    return <>{fallback}</>;
  }

  // Si pas authentifié, ne rien afficher (redirection en cours)
  if (!isAuthenticated || !user) {
    return <>{fallback}</>;
  }

  // Si rôles requis et utilisateur n'a pas les permissions
  if (requiredRoles.length > 0) {
    const hasRequiredRole = requiredRoles.some(role => 
      user.role?.toLowerCase() === role.toLowerCase()
    );
    
    if (!hasRequiredRole) {
      return <>{fallback}</>;
    }
  }

  // Tout est OK, afficher le contenu
  return <>{children}</>;
};

export default ProtectedRoute;
