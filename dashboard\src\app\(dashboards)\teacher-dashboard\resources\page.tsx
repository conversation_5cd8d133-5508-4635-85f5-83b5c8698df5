"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { BookOpen, Upload, Download, FileText, Image, Video, File, Plus, Search, Filter } from "lucide-react";
import { motion } from "framer-motion";
import TeacherLayout from "@/components/Dashboard/Layouts/TeacherLayout";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import CircularLoader from "@/components/widgets/CircularLoader";
import { useToast, ToastContainer } from "@/components/ui/Toast";

interface SelectedSchool {
  school_id: string;
  school_name: string;
  access_granted_at: string;
}

interface Resource {
  _id: string;
  title: string;
  description: string;
  file_url: string;
  file_type: string;
  file_size: number;
  subject: string;
  class_level: string;
  uploaded_by: string;
  uploaded_at: string;
  downloads: number;
}

const navigation = {
  icon: BookOpen,
  baseHref: "/teacher-dashboard/resources",
  title: "Teaching Resources"
};

export default function TeacherResourcesPage() {
  const { logout, user } = useAuth();
  const router = useRouter();
  const { toasts, removeToast, showSuccess, showError } = useToast();

  // State management
  const [selectedSchool, setSelectedSchool] = useState<SelectedSchool | null>(null);
  const [loadingSchool, setLoadingSchool] = useState(true);
  const [loadingData, setLoadingData] = useState(false);
  const [resources, setResources] = useState<Resource[]>([]);

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSubject, setSelectedSubject] = useState('all');
  const [selectedFileType, setSelectedFileType] = useState('all');

  // Modal states
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  // Get school ID from user context
  const schoolId = selectedSchool?.school_id;

  // Load selected school from localStorage
  useEffect(() => {
    const savedSchool = localStorage.getItem('teacher_selected_school');
    if (savedSchool) {
      setSelectedSchool(JSON.parse(savedSchool));
    }
    setLoadingSchool(false);
  }, []);

  // Mock data for now - replace with real API calls
  useEffect(() => {
    if (schoolId) {
      setLoadingData(true);
      // Simulate API call
      setTimeout(() => {
        setResources([
          {
            _id: "1",
            title: "Mathematics Worksheet - Algebra",
            description: "Practice problems for quadratic equations",
            file_url: "/files/math-worksheet.pdf",
            file_type: "pdf",
            file_size: 2048000,
            subject: "Mathematics",
            class_level: "Grade 10",
            uploaded_by: user?.name || "Teacher",
            uploaded_at: "2024-01-15",
            downloads: 25
          },
          {
            _id: "2",
            title: "Science Lab Instructions",
            description: "Step-by-step guide for chemistry experiments",
            file_url: "/files/lab-instructions.docx",
            file_type: "docx",
            file_size: 1024000,
            subject: "Chemistry",
            class_level: "Grade 11",
            uploaded_by: user?.name || "Teacher",
            uploaded_at: "2024-01-10",
            downloads: 18
          },
          {
            _id: "3",
            title: "History Timeline Video",
            description: "World War II timeline presentation",
            file_url: "/files/history-timeline.mp4",
            file_type: "mp4",
            file_size: 15728640,
            subject: "History",
            class_level: "Grade 9",
            uploaded_by: user?.name || "Teacher",
            uploaded_at: "2024-01-08",
            downloads: 42
          }
        ]);
        setLoadingData(false);
      }, 1000);
    }
  }, [schoolId, user?.name]);

  // Handle school change
  const handleSchoolChange = () => {
    router.push('/teacher-dashboard');
  };

  // Get file icon based on file type
  const getFileIcon = (fileType: string) => {
    switch (fileType.toLowerCase()) {
      case 'pdf':
        return <FileText className="h-8 w-8 text-red-500" />;
      case 'docx':
      case 'doc':
        return <FileText className="h-8 w-8 text-blue-500" />;
      case 'mp4':
      case 'avi':
      case 'mov':
        return <Video className="h-8 w-8 text-purple-500" />;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return <Image className="h-8 w-8 text-green-500" />;
      default:
        return <File className="h-8 w-8 text-gray-500" />;
    }
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Filter resources
  const filteredResources = resources.filter(resource => {
    const matchesSearch = resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         resource.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesSubject = selectedSubject === 'all' || resource.subject === selectedSubject;
    const matchesFileType = selectedFileType === 'all' || resource.file_type === selectedFileType;
    
    return matchesSearch && matchesSubject && matchesFileType;
  });

  // Handle file upload
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setUploadProgress(0);
    setIsUploadModalOpen(true);

    // Simulate upload progress
    const interval = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsUploadModalOpen(false);
          showSuccess("File Uploaded", "Your resource has been uploaded successfully.");
          return 100;
        }
        return prev + 10;
      });
    }, 200);
  };

  if (loadingSchool) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <CircularLoader />
      </div>
    );
  }

  if (!selectedSchool) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">No School Selected</h2>
          <button
            onClick={() => router.push('/teacher-dashboard')}
            className="px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600"
          >
            Select School
          </button>
        </div>
      </div>
    );
  }

  return (
    <ProtectedRoute allowedRoles={["teacher"]}>
      <TeacherLayout
        navigation={navigation}
        selectedSchool={{
          _id: selectedSchool.school_id,
          name: selectedSchool.school_name
        }}
        onSchoolChange={handleSchoolChange}
        onLogout={logout}
      >
        <div className="space-y-6">
          {/* Header */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                  <BookOpen className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-foreground">Teaching Resources</h1>
                  <p className="text-foreground/60">
                    Manage your teaching materials for {selectedSchool.school_name}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <input
                  type="file"
                  id="file-upload"
                  className="hidden"
                  onChange={handleFileUpload}
                  accept=".pdf,.doc,.docx,.ppt,.pptx,.jpg,.jpeg,.png,.mp4,.avi"
                />
                <label
                  htmlFor="file-upload"
                  className="flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 cursor-pointer"
                >
                  <Upload size={16} />
                  <span>Upload Resource</span>
                </label>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground/40 h-4 w-4" />
                  <input
                    type="text"
                    placeholder="Search resources..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal"
                  />
                </div>
              </div>
              
              <select
                value={selectedSubject}
                onChange={(e) => setSelectedSubject(e.target.value)}
                className="px-3 py-2 border border-stroke rounded-md"
              >
                <option value="all">All Subjects</option>
                <option value="Mathematics">Mathematics</option>
                <option value="Chemistry">Chemistry</option>
                <option value="History">History</option>
                <option value="English">English</option>
                <option value="Physics">Physics</option>
              </select>
              
              <select
                value={selectedFileType}
                onChange={(e) => setSelectedFileType(e.target.value)}
                className="px-3 py-2 border border-stroke rounded-md"
              >
                <option value="all">All File Types</option>
                <option value="pdf">PDF</option>
                <option value="docx">Word Document</option>
                <option value="mp4">Video</option>
                <option value="jpg">Image</option>
              </select>
            </div>
          </div>

          {/* Resources Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {loadingData ? (
              Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
                  <div className="h-16 bg-gray-200 rounded mb-4"></div>
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded mb-4"></div>
                  <div className="flex justify-between">
                    <div className="h-3 bg-gray-200 rounded w-16"></div>
                    <div className="h-3 bg-gray-200 rounded w-20"></div>
                  </div>
                </div>
              ))
            ) : filteredResources.length === 0 ? (
              <div className="col-span-full text-center py-12">
                <BookOpen className="h-16 w-16 text-foreground/30 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-foreground mb-2">No Resources Found</h3>
                <p className="text-foreground/60 mb-4">
                  {searchTerm || selectedSubject !== 'all' || selectedFileType !== 'all'
                    ? "Try adjusting your filters to find resources."
                    : "Upload your first teaching resource to get started."}
                </p>
                <label
                  htmlFor="file-upload"
                  className="inline-flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 cursor-pointer"
                >
                  <Upload size={16} />
                  <span>Upload Resource</span>
                </label>
              </div>
            ) : (
              filteredResources.map((resource) => (
                <motion.div
                  key={resource._id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-widget rounded-lg border border-stroke p-6 hover:shadow-lg transition-shadow"
                >
                  <div className="flex items-start space-x-4 mb-4">
                    {getFileIcon(resource.file_type)}
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-foreground truncate">{resource.title}</h3>
                      <p className="text-sm text-foreground/60 line-clamp-2">{resource.description}</p>
                    </div>
                  </div>
                  
                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between text-sm">
                      <span className="text-foreground/60">Subject:</span>
                      <span className="font-medium">{resource.subject}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-foreground/60">Class:</span>
                      <span className="font-medium">{resource.class_level}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-foreground/60">Size:</span>
                      <span className="font-medium">{formatFileSize(resource.file_size)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-foreground/60">Downloads:</span>
                      <span className="font-medium">{resource.downloads}</span>
                    </div>
                  </div>
                  
                  <div className="flex space-x-2">
                    <button className="flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-teal text-white rounded-md hover:bg-teal-600 transition-colors">
                      <Download size={14} />
                      <span>Download</span>
                    </button>
                    <button className="px-3 py-2 border border-stroke rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                      <FileText size={14} />
                    </button>
                  </div>
                </motion.div>
              ))
            )}
          </div>
        </div>

        {/* Upload Progress Modal */}
        {isUploadModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-widget rounded-lg p-6 w-full max-w-md">
              <h3 className="text-lg font-semibold mb-4">Uploading Resource</h3>
              <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
                <div
                  className="bg-teal h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
              <p className="text-sm text-foreground/60 text-center">{uploadProgress}% complete</p>
            </div>
          </div>
        )}

        {/* Toast Notifications */}
        <ToastContainer toasts={toasts} onClose={removeToast} />
      </TeacherLayout>
    </ProtectedRoute>
  );
}
