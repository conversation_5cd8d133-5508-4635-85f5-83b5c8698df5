/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/(dashboards)/teacher-dashboard/page";
exports.ids = ["app/(dashboards)/teacher-dashboard/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(dashboards)%2Fteacher-dashboard%2Fpage&page=%2F(dashboards)%2Fteacher-dashboard%2Fpage&appPaths=%2F(dashboards)%2Fteacher-dashboard%2Fpage&pagePath=private-next-app-dir%2F(dashboards)%2Fteacher-dashboard%2Fpage.tsx&appDir=D%3A%5CProjet%5Cscholarify%5Cdashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjet%5Cscholarify%5Cdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(dashboards)%2Fteacher-dashboard%2Fpage&page=%2F(dashboards)%2Fteacher-dashboard%2Fpage&appPaths=%2F(dashboards)%2Fteacher-dashboard%2Fpage&pagePath=private-next-app-dir%2F(dashboards)%2Fteacher-dashboard%2Fpage.tsx&appDir=D%3A%5CProjet%5Cscholarify%5Cdashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjet%5Cscholarify%5Cdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page7 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(dashboards)/teacher-dashboard/page.tsx */ \"(rsc)/./src/app/(dashboards)/teacher-dashboard/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(dashboards)',\n        {\n        children: [\n        'teacher-dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page7, \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'not-found': [module4, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module5, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module6, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/(dashboards)/teacher-dashboard/page\",\n        pathname: \"/teacher-dashboard\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(dashboards)%2Fteacher-dashboard%2Fpage&page=%2F(dashboards)%2Fteacher-dashboard%2Fpage&appPaths=%2F(dashboards)%2Fteacher-dashboard%2Fpage&pagePath=private-next-app-dir%2F(dashboards)%2Fteacher-dashboard%2Fpage.tsx&appDir=D%3A%5CProjet%5Cscholarify%5Cdashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjet%5Cscholarify%5Cdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5C(dashboards)%5C%5Cteacher-dashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5C(dashboards)%5C%5Cteacher-dashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(dashboards)/teacher-dashboard/page.tsx */ \"(rsc)/./src/app/(dashboards)/teacher-dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZXQlNUMlNUNzY2hvbGFyaWZ5JTVDJTVDZGFzaGJvYXJkJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDKGRhc2hib2FyZHMpJTVDJTVDdGVhY2hlci1kYXNoYm9hcmQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOE1BQTJIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxQcm9qZXRcXFxcc2Nob2xhcmlmeVxcXFxkYXNoYm9hcmRcXFxcc3JjXFxcXGFwcFxcXFwoZGFzaGJvYXJkcylcXFxcdGVhY2hlci1kYXNoYm9hcmRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5C(dashboards)%5C%5Cteacher-dashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Bricolage_Grotesque%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22bricolage%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cservices%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Cutils%5C%5CThemeInitializer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Bricolage_Grotesque%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22bricolage%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cservices%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Cutils%5C%5CThemeInitializer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/services/AuthContext.tsx */ \"(rsc)/./src/app/services/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/utils/ThemeInitializer.tsx */ \"(rsc)/./src/utils/ThemeInitializer.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Bricolage_Grotesque%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22bricolage%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cservices%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Cutils%5C%5CThemeInitializer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"32x32\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZXRcXHNjaG9sYXJpZnlcXGRhc2hib2FyZFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjMyeDMyXCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/(dashboards)/teacher-dashboard/page.tsx":
/*!*********************************************************!*\
  !*** ./src/app/(dashboards)/teacher-dashboard/page.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projet\\scholarify\\dashboard\\src\\app\\(dashboards)\\teacher-dashboard\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"223ead8c6b9a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcUHJvamV0XFxzY2hvbGFyaWZ5XFxkYXNoYm9hcmRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjIyM2VhZDhjNmI5YVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Bricolage_Grotesque_arguments_subsets_latin_weight_400_700_variableName_bricolage___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Bricolage_Grotesque\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"400\",\"700\"]}],\"variableName\":\"bricolage\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Bricolage_Grotesque\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"400\\\",\\\"700\\\"]}],\\\"variableName\\\":\\\"bricolage\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Bricolage_Grotesque_arguments_subsets_latin_weight_400_700_variableName_bricolage___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Bricolage_Grotesque_arguments_subsets_latin_weight_400_700_variableName_bricolage___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _utils_ThemeInitializer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/ThemeInitializer */ \"(rsc)/./src/utils/ThemeInitializer.tsx\");\n/* harmony import */ var _services_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./services/AuthContext */ \"(rsc)/./src/app/services/AuthContext.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"Scholarify Admin\",\n    description: \"Admin dashboard for Scholarify\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Bricolage_Grotesque_arguments_subsets_latin_weight_400_700_variableName_bricolage___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_ThemeInitializer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_services_AuthContext__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUN1QjtBQVNqQkE7QUFQa0Q7QUFDRjtBQUUvQyxNQUFNRyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUlhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFXWCxnTUFBbUI7OzhCQUNsQyw4REFBQ0MsK0RBQWdCQTs7Ozs7OEJBQ2pCLDhEQUFDQywrREFBWUE7OEJBQ1JLOzs7Ozs7Ozs7Ozs7Ozs7OztBQUtiIiwic291cmNlcyI6WyJEOlxcUHJvamV0XFxzY2hvbGFyaWZ5XFxkYXNoYm9hcmRcXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xyXG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XHJcbmltcG9ydCB7IEJyaWNvbGFnZV9Hcm90ZXNxdWUgfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xyXG5pbXBvcnQgVGhlbWVJbml0aWFsaXplciBmcm9tIFwiQC91dGlscy9UaGVtZUluaXRpYWxpemVyXCI7XHJcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gXCIuL3NlcnZpY2VzL0F1dGhDb250ZXh0XCI7XHJcblxyXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xyXG4gIHRpdGxlOiBcIlNjaG9sYXJpZnkgQWRtaW5cIixcclxuICBkZXNjcmlwdGlvbjogXCJBZG1pbiBkYXNoYm9hcmQgZm9yIFNjaG9sYXJpZnlcIixcclxufTtcclxuY29uc3QgYnJpY29sYWdlID0gQnJpY29sYWdlX0dyb3Rlc3F1ZSh7IHN1YnNldHM6IFtcImxhdGluXCJdLCB3ZWlnaHQ6IFtcIjQwMFwiLCBcIjcwMFwiXSB9KTtcclxuXHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcclxuICBjaGlsZHJlbixcclxufTogUmVhZG9ubHk8e1xyXG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XHJcbn0+KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxyXG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2JyaWNvbGFnZS5jbGFzc05hbWV9PlxyXG4gICAgICAgIDxUaGVtZUluaXRpYWxpemVyIC8+XHJcbiAgICAgICAgPEF1dGhQcm92aWRlcj5cclxuICAgICAgICAgICAge2NoaWxkcmVufVxyXG4gICAgICAgIDwvQXV0aFByb3ZpZGVyPlxyXG4gICAgICA8L2JvZHk+XHJcbiAgICA8L2h0bWw+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiYnJpY29sYWdlIiwiVGhlbWVJbml0aWFsaXplciIsIkF1dGhQcm92aWRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/services/AuthContext.tsx":
/*!******************************************!*\
  !*** ./src/app/services/AuthContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthContext: () => (/* binding */ AuthContext),
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   BASE_API_URL: () => (/* binding */ BASE_API_URL)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const BASE_API_URL = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call BASE_API_URL() from the server but BASE_API_URL is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projet\\scholarify\\dashboard\\src\\app\\services\\AuthContext.tsx",
"BASE_API_URL",
);const AuthContext = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthContext() from the server but AuthContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projet\\scholarify\\dashboard\\src\\app\\services\\AuthContext.tsx",
"AuthContext",
);const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projet\\scholarify\\dashboard\\src\\app\\services\\AuthContext.tsx",
"AuthProvider",
);

/***/ }),

/***/ "(rsc)/./src/utils/ThemeInitializer.tsx":
/*!****************************************!*\
  !*** ./src/utils/ThemeInitializer.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\utils\\\\ThemeInitializer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projet\\scholarify\\dashboard\\src\\utils\\ThemeInitializer.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5C(dashboards)%5C%5Cteacher-dashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5C(dashboards)%5C%5Cteacher-dashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(dashboards)/teacher-dashboard/page.tsx */ \"(ssr)/./src/app/(dashboards)/teacher-dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZXQlNUMlNUNzY2hvbGFyaWZ5JTVDJTVDZGFzaGJvYXJkJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDKGRhc2hib2FyZHMpJTVDJTVDdGVhY2hlci1kYXNoYm9hcmQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOE1BQTJIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxQcm9qZXRcXFxcc2Nob2xhcmlmeVxcXFxkYXNoYm9hcmRcXFxcc3JjXFxcXGFwcFxcXFwoZGFzaGJvYXJkcylcXFxcdGVhY2hlci1kYXNoYm9hcmRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5C(dashboards)%5C%5Cteacher-dashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Bricolage_Grotesque%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22bricolage%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cservices%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Cutils%5C%5CThemeInitializer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Bricolage_Grotesque%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22bricolage%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cservices%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Cutils%5C%5CThemeInitializer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/services/AuthContext.tsx */ \"(ssr)/./src/app/services/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/utils/ThemeInitializer.tsx */ \"(ssr)/./src/utils/ThemeInitializer.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Bricolage_Grotesque%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22bricolage%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cservices%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Cutils%5C%5CThemeInitializer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/(dashboards)/teacher-dashboard/page.tsx":
/*!*********************************************************!*\
  !*** ./src/app/(dashboards)/teacher-dashboard/page.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TeacherDashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Key_LogIn_School_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Key,LogIn,School!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/school.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Key_LogIn_School_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Key,LogIn,School!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Key_LogIn_School_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Key,LogIn,School!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Key_LogIn_School_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Key,LogIn,School!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(ssr)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var _components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/utils/ProtectedRoute */ \"(ssr)/./src/components/utils/ProtectedRoute.tsx\");\n/* harmony import */ var _components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/widgets/CircularLoader */ \"(ssr)/./src/components/widgets/CircularLoader.tsx\");\n/* harmony import */ var _components_NotificationCard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/NotificationCard */ \"(ssr)/./src/components/NotificationCard.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_widgets_Logo__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/widgets/Logo */ \"(ssr)/./src/components/widgets/Logo.tsx\");\n/* harmony import */ var _app_services_SchoolServices__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/services/SchoolServices */ \"(ssr)/./src/app/services/SchoolServices.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\nfunction TeacherDashboardPage() {\n    const { user, logout } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [schools, setSchools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedSchool, setSelectedSchool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [accessCode, setAccessCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showAccessCodeInput, setShowAccessCodeInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TeacherDashboardPage.useEffect\": ()=>{\n            const fetchSchools = {\n                \"TeacherDashboardPage.useEffect.fetchSchools\": async ()=>{\n                    if (user && user.role === \"teacher\") {\n                        // Extract schools from user's access_codes\n                        const userSchools = user.access_codes || [];\n                        if (userSchools.length > 0) {\n                            // Créer un nouveau tableau avec les noms d'école\n                            const schoolsWithNames = await Promise.all(userSchools.map({\n                                \"TeacherDashboardPage.useEffect.fetchSchools\": async (school)=>{\n                                    try {\n                                        const schoolData = await (0,_app_services_SchoolServices__WEBPACK_IMPORTED_MODULE_8__.getSchoolBy_id)(school.school_id);\n                                        return {\n                                            ...school,\n                                            school_name: schoolData.name\n                                        };\n                                    } catch (error) {\n                                        console.error(`Error fetching school ${school.school_id}:`, error);\n                                        return {\n                                            ...school,\n                                            school_name: `School ${school.school_id.slice(-6)}` // Fallback name\n                                        };\n                                    }\n                                }\n                            }[\"TeacherDashboardPage.useEffect.fetchSchools\"]));\n                            setSchools(schoolsWithNames);\n                        } else {\n                            setSchools([]);\n                        }\n                        setLoading(false);\n                    } else if (user && user.role !== \"teacher\") {\n                        // Redirect non-teachers\n                        router.push(\"/dashboard\");\n                    }\n                }\n            }[\"TeacherDashboardPage.useEffect.fetchSchools\"];\n            fetchSchools();\n        }\n    }[\"TeacherDashboardPage.useEffect\"], [\n        user,\n        router\n    ]);\n    const handleSchoolSelect = (schoolId)=>{\n        setSelectedSchool(schoolId);\n        setShowAccessCodeInput(true);\n        setError(null);\n    };\n    const handleAccessCodeSubmit = async (e)=>{\n        e.preventDefault();\n        if (!selectedSchool || !accessCode) {\n            setError(\"Please select a school and enter the access code\");\n            return;\n        }\n        setIsSubmitting(true);\n        setError(null);\n        try {\n            // Find the school access\n            const schoolAccess = schools.find((s)=>s.school_id === selectedSchool);\n            if (!schoolAccess) {\n                setError(\"School not found in your access list\");\n                setIsSubmitting(false);\n                return;\n            }\n            // Verify access code\n            if (schoolAccess.access_code.toLowerCase() !== accessCode.toLowerCase()) {\n                setError(\"Invalid access code\");\n                setIsSubmitting(false);\n                return;\n            }\n            if (!schoolAccess.is_active) {\n                setError(\"Your access to this school has been revoked\");\n                setIsSubmitting(false);\n                return;\n            }\n            // Store selected school in localStorage\n            localStorage.setItem(\"teacher_selected_school\", JSON.stringify({\n                school_id: selectedSchool,\n                school_name: schoolAccess.school_name || \"School\",\n                access_granted_at: new Date().toISOString()\n            }));\n            // Redirect to teacher dashboard\n            router.push(\"/teacher-dashboard/dashboard\");\n        } catch (error) {\n            console.error(\"Error verifying access code:\", error);\n            setError(\"Failed to verify access code. Please try again.\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleBack = ()=>{\n        setShowAccessCodeInput(false);\n        setSelectedSchool(\"\");\n        setAccessCode(\"\");\n        setError(null);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                size: 32,\n                color: \"teal\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n            lineNumber: 141,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        allowedRoles: [\n            \"teacher\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_Logo__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-foreground mb-2\",\n                                children: [\n                                    \"Welcome, \",\n                                    user?.first_name || user?.name,\n                                    \"!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-foreground/60\",\n                                children: \"Select a school to access your teaching dashboard\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NotificationCard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            message: error,\n                            type: \"error\",\n                            onClose: ()=>setError(null),\n                            isVisible: true\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 13\n                    }, this),\n                    !showAccessCodeInput ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"bg-widget rounded-lg border border-stroke p-6 shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Key_LogIn_School_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-5 w-5 text-teal\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-foreground\",\n                                        children: \"Your Schools\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 15\n                            }, this),\n                            schools.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Key_LogIn_School_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-12 w-12 text-foreground/30 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-foreground/60 mb-4\",\n                                        children: \"You don't have access to any schools yet.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-foreground/50\",\n                                        children: \"Contact your school administrator to get access codes.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: schools.map((school)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        onClick: ()=>handleSchoolSelect(school.school_id),\n                                        disabled: !school.is_active,\n                                        className: `w-full p-4 border-2 rounded-lg text-left transition-all ${school.is_active ? \"border-stroke hover:border-teal bg-widget hover:bg-teal/5\" : \"border-red-200 bg-red-50 dark:bg-red-900/20 opacity-60 cursor-not-allowed\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-foreground\",\n                                                            children: school.school_name || `School ${school.school_id.slice(-6)}`\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-foreground/60\",\n                                                            children: [\n                                                                \"Access granted: \",\n                                                                new Date(school.granted_at).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        school.is_active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 text-xs rounded-full\",\n                                                            children: \"Active\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 29\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300 text-xs rounded-full\",\n                                                            children: \"Revoked\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Key_LogIn_School_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-4 w-4 text-foreground/40\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, school.school_id, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 21\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 pt-4 border-t border-stroke\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: logout,\n                                    className: \"w-full px-4 py-2 text-foreground/70 hover:text-foreground hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors\",\n                                    children: \"Logout\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 13\n                    }, this) : /* Access Code Input */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"bg-widget rounded-lg border border-stroke p-6 shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Key_LogIn_School_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-5 w-5 text-teal\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-foreground\",\n                                        children: \"Enter Access Code\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-foreground/60 mb-6\",\n                                children: \"Please enter your access code for the selected school to continue.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleAccessCodeSubmit,\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-foreground mb-2\",\n                                                children: \"Access Code\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: accessCode,\n                                                onChange: (e)=>setAccessCode(e.target.value.toUpperCase()),\n                                                placeholder: \"Enter your access code\",\n                                                className: \"w-full px-3 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent bg-widget text-foreground font-mono text-center text-lg tracking-wider\",\n                                                maxLength: 16,\n                                                required: true,\n                                                autoFocus: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: handleBack,\n                                                className: \"flex-1 px-4 py-2 text-foreground/70 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\",\n                                                disabled: isSubmitting,\n                                                children: \"Back\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                                whileHover: {\n                                                    scale: 1.05\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                type: \"submit\",\n                                                disabled: isSubmitting || !accessCode,\n                                                className: \"flex-1 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2\",\n                                                children: [\n                                                    isSubmitting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        size: 16,\n                                                        color: \"white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 38\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Access Dashboard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n                lineNumber: 150,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\teacher-dashboard\\\\page.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(dashboards)/teacher-dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/hooks/useAuth.tsx":
/*!***********************************!*\
  !*** ./src/app/hooks/useAuth.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _services_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/AuthContext */ \"(ssr)/./src/app/services/AuthContext.tsx\");\n\n\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_services_AuthContext__WEBPACK_IMPORTED_MODULE_1__.AuthContext);\n    if (!context) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2hvb2tzL3VzZUF1dGgudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBbUM7QUFDbUI7QUFHdkMsU0FBU0U7SUFDcEIsTUFBTUMsVUFBVUgsaURBQVVBLENBQUNDLDhEQUFXQTtJQUN0QyxJQUFJLENBQUNFLFNBQVM7UUFDVixNQUFNLElBQUlDLE1BQU07SUFDcEI7SUFDQSxPQUFPRDtBQUNYIiwic291cmNlcyI6WyJEOlxcUHJvamV0XFxzY2hvbGFyaWZ5XFxkYXNoYm9hcmRcXHNyY1xcYXBwXFxob29rc1xcdXNlQXV0aC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlQ29udGV4dCB9IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgeyBBdXRoQ29udGV4dCB9IGZyb20gXCIuLi9zZXJ2aWNlcy9BdXRoQ29udGV4dFwiO1xyXG5cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHVzZUF1dGgoKXtcclxuICAgIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KEF1dGhDb250ZXh0KTtcclxuICAgIGlmICghY29udGV4dCkge1xyXG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcInVzZUF1dGggbXVzdCBiZSB1c2VkIHdpdGhpbiBhbiBBdXRoUHJvdmlkZXJcIik7XHJcbiAgICB9XHJcbiAgICByZXR1cm4gY29udGV4dDtcclxufSJdLCJuYW1lcyI6WyJ1c2VDb250ZXh0IiwiQXV0aENvbnRleHQiLCJ1c2VBdXRoIiwiY29udGV4dCIsIkVycm9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/hooks/useAuth.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/services/AuthContext.tsx":
/*!******************************************!*\
  !*** ./src/app/services/AuthContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthContext: () => (/* binding */ AuthContext),\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   BASE_API_URL: () => (/* binding */ BASE_API_URL)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _UserServices__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./UserServices */ \"(ssr)/./src/app/services/UserServices.tsx\");\n/* harmony import */ var _utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/httpInterceptor */ \"(ssr)/./src/app/utils/httpInterceptor.tsx\");\n/* __next_internal_client_entry_do_not_use__ BASE_API_URL,AuthContext,AuthProvider auto */ \n\n\n\n\nconst BASE_API_URL = process.env.BASE_API_URL || \"https://scolarify.onrender.com/api\";\n// Create a context for authentication\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)(undefined);\n// composant AuthProvider qui fournit le contexte d'authentification\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [redirectAfterLogin, setRedirectAfterLogin] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [authCheckInterval, setAuthCheckInterval] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Fonction pour forcer la déconnexion\n    const forceLogout = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"AuthProvider.useCallback[forceLogout]\": ()=>{\n            console.warn(\"Force logout triggered\");\n            setUser(null);\n            (0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__.clearSession)();\n            if (authCheckInterval) {\n                clearInterval(authCheckInterval);\n                setAuthCheckInterval(null);\n            }\n            if (false) {}\n        }\n    }[\"AuthProvider.useCallback[forceLogout]\"], [\n        authCheckInterval\n    ]);\n    // Fonction pour vérifier le statut d'authentification\n    const checkAuthStatus = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"AuthProvider.useCallback[checkAuthStatus]\": async ()=>{\n            try {\n                // Vérifier si le token existe\n                const token = js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(\"idToken\");\n                if (!token) {\n                    forceLogout();\n                    return;\n                }\n                // Vérifier si le token est expiré\n                if ((0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__.isTokenExpired)()) {\n                    console.warn(\"Token expired, logging out\");\n                    forceLogout();\n                    return;\n                }\n                // Vérifier avec le serveur\n                const currentUser = await (0,_UserServices__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)();\n                if (!currentUser) {\n                    forceLogout();\n                    return;\n                }\n                setUser(currentUser);\n            } catch (error) {\n                console.error(\"Auth check failed:\", error);\n                forceLogout();\n            }\n        }\n    }[\"AuthProvider.useCallback[checkAuthStatus]\"], [\n        forceLogout\n    ]);\n    // vérifier si un utilisateur est déja connecté\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const checkUserLoggedIn = {\n                \"AuthProvider.useEffect.checkUserLoggedIn\": async ()=>{\n                    try {\n                        // Vérifier d'abord si le token existe et n'est pas expiré\n                        const token = js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(\"idToken\");\n                        if (!token || (0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__.isTokenExpired)()) {\n                            setUser(null);\n                            setLoading(false);\n                            return;\n                        }\n                        const user = await (0,_UserServices__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)();\n                        if (user) {\n                            setUser(user);\n                            // Démarrer la vérification périodique\n                            const interval = setInterval(checkAuthStatus, 60000); // Vérifier toutes les minutes\n                            setAuthCheckInterval(interval);\n                        } else {\n                            js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"idToken\");\n                            setUser(null);\n                        }\n                    } catch (error) {\n                        console.error(\"Error verifying token:\", error);\n                        js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"idToken\");\n                        setUser(null);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.checkUserLoggedIn\"];\n            checkUserLoggedIn();\n            // Cleanup interval on unmount\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    if (authCheckInterval) {\n                        clearInterval(authCheckInterval);\n                    }\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        checkAuthStatus,\n        authCheckInterval\n    ]);\n    const login = async (email, password, rememberMe, redirectUrl)=>{\n        try {\n            const response = await fetch(`${BASE_API_URL}/auth/login`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email: email,\n                    password: password\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                console.error(\"Login error:\", data.message || \"Unknown error\");\n                throw new Error(data.message || \"Login failed\");\n            }\n            const { idToken } = data;\n            if (!idToken) {\n                throw new Error(\"No idToken received\");\n            }\n            // Stocker le token dans les cookies\n            js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].set(\"idToken\", idToken, {\n                expires: rememberMe ? 30 : 7\n            }); // Expire dans 7 jours\n            const user = await (0,_UserServices__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)(); // Vérifier si l'utilisateur est connecté à nouveau après la connexion réussie\n            if (user) {\n                setUser(user);\n            }\n            // Si une URL de redirection est fournie, stocke-la\n            if (redirectUrl) {\n                setRedirectAfterLogin(redirectUrl);\n            }\n        } catch (error) {\n            console.error(\"Login failed:\", error);\n            throw error;\n        }\n    };\n    const logout = async ()=>{\n        setUser(null);\n        js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"idToken\"); // Supprimer le token des cookies\n        setRedirectAfterLogin(null); // Réinitialiser l'URL de redirection\n        return Promise.resolve();\n    };\n    const isAuthentiacted = !!user; // Vérifier si l'utilisateur est authentifié\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            isAuthenticated: isAuthentiacted,\n            loading,\n            setRedirectAfterLogin,\n            redirectAfterLogin,\n            login,\n            logout,\n            checkAuthStatus,\n            forceLogout\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\services\\\\AuthContext.tsx\",\n        lineNumber: 184,\n        columnNumber: 9\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/services/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/services/SchoolServices.tsx":
/*!*********************************************!*\
  !*** ./src/app/services/SchoolServices.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSchool: () => (/* binding */ createSchool),\n/* harmony export */   deleteAllSchools: () => (/* binding */ deleteAllSchools),\n/* harmony export */   deleteMultipleSchools: () => (/* binding */ deleteMultipleSchools),\n/* harmony export */   deleteSchool: () => (/* binding */ deleteSchool),\n/* harmony export */   getSchoolById: () => (/* binding */ getSchoolById),\n/* harmony export */   getSchoolBy_id: () => (/* binding */ getSchoolBy_id),\n/* harmony export */   getSchoolCountChange: () => (/* binding */ getSchoolCountChange),\n/* harmony export */   getSchoolPerformance: () => (/* binding */ getSchoolPerformance),\n/* harmony export */   getSchools: () => (/* binding */ getSchools),\n/* harmony export */   getTotalSchools: () => (/* binding */ getTotalSchools),\n/* harmony export */   updateSchool: () => (/* binding */ updateSchool)\n/* harmony export */ });\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./AuthContext */ \"(ssr)/./src/app/services/AuthContext.tsx\");\n/* harmony import */ var _UserServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./UserServices */ \"(ssr)/./src/app/services/UserServices.tsx\");\n\n\nasync function getSchools() {\n    try {\n        const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL}/school/get-schools`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${token}`\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching schools:\", response.statusText);\n            throw new Error(\"Failed to fetch schools data\");\n        }\n        const schoolsList = await response.json();\n        const schools = schoolsList.map((school)=>{\n            return {\n                _id: school._id,\n                school_id: school.school_id,\n                name: school.name,\n                email: school.email,\n                address: school.address,\n                website: school.website,\n                phone_number: school.phone_number,\n                principal_name: school.principal_name,\n                established_year: school.established_year,\n                description: school.description\n            };\n        });\n        return schools;\n    } catch (error) {\n        console.error(\"Error fetching schools:\", error);\n        throw new Error(\"Failed to fetch schools data\");\n    }\n}\nasync function getSchoolById(schoolId) {\n    const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL}/school/get-school/${schoolId}`, {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${(0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\")}`\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching school:\", response.statusText);\n        throw new Error(\"Failed to fetch school data\");\n    }\n    const data = await response.json();\n    const school = {\n        school_id: data.school_id,\n        credit: data.credit,\n        name: data.name,\n        email: data.email,\n        address: data.address,\n        website: data.website,\n        phone_number: data.phone_number,\n        principal_name: data.principal_name,\n        established_year: data.established_year,\n        description: data.description\n    };\n    return school;\n}\nasync function getSchoolBy_id(schoolId) {\n    const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL}/school/get-school_id/${schoolId}`, {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${(0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\")}`\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching school:\", response.statusText);\n        throw new Error(\"Failed to fetch school data\");\n    }\n    const data = await response.json();\n    const school = {\n        _id: data._id,\n        school_id: data.school_id,\n        credit: data.credit,\n        name: data.name,\n        email: data.email,\n        address: data.address,\n        website: data.website,\n        phone_number: data.phone_number,\n        principal_name: data.principal_name,\n        established_year: data.established_year,\n        description: data.description\n    };\n    return school;\n}\nasync function createSchool(schoolData) {\n    const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL}/school/create-school`, {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${(0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\")}`\n        },\n        body: JSON.stringify(schoolData)\n    });\n    if (!response.ok) {\n        console.error(\"Error creating school:\", response.statusText);\n        throw new Error(\"Failed to create school data\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function updateSchool(schoolId, schoolData) {\n    const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL}/school/update-school/${schoolId}`, {\n        method: \"PUT\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${(0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\")}`\n        },\n        body: JSON.stringify(schoolData)\n    });\n    if (!response.ok) {\n        console.error(\"Error updating school:\", response.statusText);\n        throw new Error(\"Failed to update school data\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function deleteSchool(schoolId) {\n    const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL}/school/delete-school/${schoolId}`, {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${(0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\")}`\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting school:\", response.statusText);\n        throw new Error(\"Failed to delete school data\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function deleteMultipleSchools(schoolIds) {\n    const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL}/school/delete-schools`, {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${(0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\")}`\n        },\n        body: JSON.stringify({\n            ids: schoolIds\n        })\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting multiple schools:\", response.statusText);\n        throw new Error(\"Failed to delete multiple schools\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function deleteAllSchools() {\n    const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL}/school/delete-all-schools`, {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${(0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\")}`\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting all schools:\", response.statusText);\n        throw new Error(\"Failed to delete all schools\");\n    }\n    const data = await response.json();\n    return data;\n}\n// Get total number of schools\nasync function getTotalSchools() {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL}/school/total-schools`, {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${token}`\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching total schools:\", response.statusText);\n        throw new Error(\"Failed to fetch total schools\");\n    }\n    const data = await response.json();\n    return data.totalSchools;\n}\n// Get schools created this month and percentage change from last month\nasync function getSchoolCountChange() {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL}/school/schools-count-change`, {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${token}`\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching school count change:\", response.statusText);\n        throw new Error(\"Failed to fetch school count change\");\n    }\n    const data = await response.json();\n    return {\n        totalThisMonth: data.totalSchoolsThisMonth,\n        percentageChange: data.percentageChange\n    };\n}\nasync function getSchoolPerformance() {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL}/school/performance`, {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${token}`\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching performance metrics:\", response.statusText);\n        throw new Error(\"Failed to fetch school performance metrics\");\n    }\n    const data = await response.json();\n    return data;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/services/SchoolServices.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/services/UserServices.tsx":
/*!*******************************************!*\
  !*** ./src/app/services/UserServices.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   deleteAllUsers: () => (/* binding */ deleteAllUsers),\n/* harmony export */   deleteMultipleUsers: () => (/* binding */ deleteMultipleUsers),\n/* harmony export */   deleteUser: () => (/* binding */ deleteUser),\n/* harmony export */   forget_password: () => (/* binding */ forget_password),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getMonthlyUserStarts: () => (/* binding */ getMonthlyUserStarts),\n/* harmony export */   getParents: () => (/* binding */ getParents),\n/* harmony export */   getTokenFromCookie: () => (/* binding */ getTokenFromCookie),\n/* harmony export */   getTotalUsers: () => (/* binding */ getTotalUsers),\n/* harmony export */   getUserById: () => (/* binding */ getUserById),\n/* harmony export */   getUserBy_id: () => (/* binding */ getUserBy_id),\n/* harmony export */   getUserCountWithChange: () => (/* binding */ getUserCountWithChange),\n/* harmony export */   getUsers: () => (/* binding */ getUsers),\n/* harmony export */   handleUserSearch: () => (/* binding */ handleUserSearch),\n/* harmony export */   registerParent: () => (/* binding */ registerParent),\n/* harmony export */   resend_Code: () => (/* binding */ resend_Code),\n/* harmony export */   resetParentPasswordService: () => (/* binding */ resetParentPasswordService),\n/* harmony export */   reset_password: () => (/* binding */ reset_password),\n/* harmony export */   updateUser: () => (/* binding */ updateUser),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verify_otp: () => (/* binding */ verify_otp)\n/* harmony export */ });\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var jwt_decode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jwt-decode */ \"(ssr)/./node_modules/jwt-decode/build/esm/index.js\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"(ssr)/./src/app/services/AuthContext.tsx\");\n/* harmony import */ var _utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/httpInterceptor */ \"(ssr)/./src/app/utils/httpInterceptor.tsx\");\n\n\n\n\nfunction getTokenFromCookie(name) {\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(name);\n    return token;\n}\nasync function getCurrentUser() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        if (!token) {\n            return null;\n        }\n        const decodedUser = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_1__.jwtDecode)(token);\n        const email = decodedUser.email;\n        const response = await (0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_3__.authenticatedGet)(`/user/get-user-email/${email}`);\n        if (!response.ok) {\n            console.error(\"Error fetching user:\", response.statusText);\n            return null;\n        }\n        const user = await response.json();\n        return user;\n    } catch (error) {\n        console.error(\"Error fetching current user:\", error);\n        return null;\n    }\n}\nasync function getParents() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/get-parents`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${token}`\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching parents:\", response.statusText);\n            throw new Error(\"Failed to fetch parents data\");\n        }\n        const parentsList = await response.json();\n        const parents = parentsList.map((user)=>({\n                _id: user._id,\n                user_id: user.user_id,\n                firebaseUid: user.firebaseUid,\n                name: user.name,\n                email: user.email,\n                phone: user.phone,\n                role: user.role,\n                avatar: user.avatar,\n                address: user.address,\n                school_ids: user.school_ids,\n                student_ids: user.student_ids,\n                isVerified: user.isVerified,\n                verificationCode: user.verificationCode,\n                verificationCodeExpires: user.verificationCodeExpires,\n                lastLogin: user.lastLogin,\n                createdAt: user.createdAt,\n                updatedAt: user.updatedAt\n            }));\n        return parents;\n    } catch (error) {\n        console.error(\"Error fetching parents:\", error);\n        throw new Error(\"Failed to fetch parents data\");\n    }\n}\nasync function getUsers() {\n    try {\n        const token = getTokenFromCookie(\"idToken\"); // Assuming this function gets the token from cookies\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/get-users`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${token}`\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching users:\", response.statusText);\n            throw new Error(\"Failed to fetch users data\");\n        }\n        const usersList = await response.json();\n        const users = usersList.map((user)=>{\n            return {\n                _id: user._id,\n                user_id: user.user_id,\n                firebaseUid: user.firebaseUid,\n                name: user.name,\n                email: user.email,\n                phone: user.phone,\n                role: user.role,\n                avatar: user.avatar,\n                address: user.address,\n                school_ids: user.school_ids,\n                isVerified: user.isVerified,\n                verificationCode: user.verificationCode,\n                verificationCodeExpires: user.verificationCodeExpires,\n                lastLogin: user.lastLogin,\n                createdAt: user.createdAt,\n                updatedAt: user.updatedAt\n            };\n        });\n        return users;\n    } catch (error) {\n        console.error(\"Error fetching users:\", error);\n        throw new Error(\"Failed to fetch users data\");\n    }\n}\nasync function createUser(userData) {\n    try {\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/register-user`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${getTokenFromCookie(\"idToken\")}`\n            },\n            body: JSON.stringify(userData)\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to create user data\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = errorBody?.message || errorMessage;\n            } catch (parseError) {\n                // If parsing the error body fails, use default message\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            console.error(\"Error creating user:\", errorMessage);\n            throw new Error(errorMessage);\n        }\n        const data = await response.json(); // Parse the response as JSON\n        return data; // Return the response data (usually the created user object)\n    } catch (error) {\n        console.error(\"Error creating user:\", error);\n        throw new Error(error instanceof Error ? error.message : \"Failed to create user data\");\n    }\n}\nasync function updateUser(user_id, userData) {\n    try {\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/update-user/${user_id}`, {\n            method: \"PUT\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${getTokenFromCookie(\"idToken\")}`\n            },\n            body: JSON.stringify(userData)\n        });\n        if (!response.ok) {\n            console.error(\"Error updating user:\", response.statusText);\n            throw new Error(\"Failed to update user data\");\n        }\n        const data = await response.json(); // Parse the response as JSON\n        return data; // Return the updated user data (this could be the user object with updated fields)\n    } catch (error) {\n        console.error(\"Error updating user:\", error);\n        throw new Error(\"Failed to update user data\");\n    }\n}\nasync function getUserById(userId) {\n    const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/get-user/${userId}`, {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${getTokenFromCookie(\"idToken\")}`\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching user:\", response.statusText);\n        throw new Error(\"Failed to fetch user data\");\n    }\n    const data = await response.json();\n    const user = {\n        _id: data._id,\n        user_id: data.user_id,\n        name: data.name,\n        email: data.email,\n        phone: data.phone,\n        role: data.role,\n        address: data.address,\n        school_ids: data.school_ids,\n        isVerified: data.isVerified,\n        description: data.description,\n        firebaseUid: data.firebaseUid,\n        createdAt: data.createdAt,\n        updatedAt: data.updatedAt\n    };\n    return user;\n}\nasync function verifyPassword(password, email) {\n    const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/auth/verify-password`, {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n            email: email,\n            password: password\n        })\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching user:\", response.statusText);\n        return false;\n    }\n    return true;\n}\nasync function deleteUser(user_id) {\n    try {\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/delete-user/${user_id}`, {\n            method: \"DELETE\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${getTokenFromCookie(\"idToken\")}`\n            }\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to delete user\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = errorBody?.message || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            console.error(\"Error deleting user:\", errorMessage);\n            throw new Error(errorMessage);\n        }\n        const result = await response.json();\n        return result; // Might return a success message or deleted user data\n    } catch (error) {\n        console.error(\"Error deleting user:\", error);\n        throw new Error(error instanceof Error ? error.message : \"Failed to delete user\");\n    }\n}\nasync function getUserBy_id(_id) {\n    const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/get-user-by-id/${_id}`, {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${getTokenFromCookie(\"idToken\")}`\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching user:\", response.statusText);\n        throw new Error(\"Failed to fetch user data\");\n    }\n    const data = await response.json();\n    const user = {\n        _id: data._id,\n        user_id: data.user_id,\n        name: data.name,\n        email: data.email,\n        phone: data.phone,\n        role: data.role,\n        address: data.address,\n        school_ids: data.school_ids,\n        isVerified: data.isVerified,\n        description: data.description,\n        firebaseUid: data.firebaseUid,\n        createdAt: data.createdAt,\n        updatedAt: data.updatedAt\n    };\n    return user;\n}\nasync function forget_password(email) {\n    try {\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/auth/forgot-password`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email: email\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to send reset password email: check your email\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = errorBody?.message || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error sending reset password email:\", error);\n        throw new Error(\"Failed to send reset password email\");\n    }\n}\nasync function verify_otp(code, email) {\n    try {\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/auth/verify-code`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                code: code,\n                email: email\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to verify OTP\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = errorBody?.message || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error verifying OTP:\", error);\n        throw new Error(\"Failed to verify OTP\");\n    }\n}\nasync function resend_Code(email) {\n    try {\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/auth/resend-code`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email: email\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to resend code\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = errorBody?.message || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error resending code:\", error);\n        throw new Error(\"Failed to resend code\");\n    }\n}\nasync function reset_password(newPassword, email, code) {\n    try {\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/auth/reset-password`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email: email,\n                code: code,\n                newPassword: newPassword\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to reset password\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = errorBody?.message || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error resetting password:\", error);\n        throw new Error(\"Failed to reset password\");\n    }\n}\nasync function handleUserSearch(query) {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/search-users?query=${encodeURIComponent(query)}`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${token}`\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error searching users:\", response.statusText);\n            throw new Error(\"Failed to search users\");\n        }\n        const results = await response.json();\n        const users = results.map((user)=>{\n            return {\n                _id: user._id,\n                user_id: user.user_id,\n                firebaseUid: user.firebaseUid,\n                name: user.name,\n                email: user.email,\n                phone: user.phone,\n                role: user.role,\n                avatar: user.avatar,\n                address: user.address,\n                school_ids: user.school_ids,\n                isVerified: user.isVerified,\n                verificationCode: user.verificationCode,\n                verificationCodeExpires: user.verificationCodeExpires,\n                lastLogin: user.lastLogin,\n                createdAt: user.createdAt,\n                updatedAt: user.updatedAt\n            };\n        });\n        return users;\n    } catch (error) {\n        console.error(\"Error searching users:\", error);\n        throw new Error(\"Failed to search users\");\n    }\n}\nasync function registerParent(parentData) {\n    try {\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/register-parent`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${getTokenFromCookie(\"idToken\")}`\n            },\n            body: JSON.stringify(parentData)\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to register parent\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = errorBody?.message || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            console.error(\"Error registering parent:\", errorMessage);\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data; // This includes user object and generatedPassword (if new)\n    } catch (error) {\n        console.error(\"Error in registerParent service:\", error);\n        throw new Error(error instanceof Error ? error.message : \"Failed to register parent\");\n    }\n}\nasync function deleteMultipleUsers(userIds) {\n    const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/delete-users`, {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${getTokenFromCookie(\"idToken\")}`\n        },\n        body: JSON.stringify({\n            ids: userIds\n        })\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting multiple users:\", response.statusText);\n        throw new Error(\"Failed to delete multiple users\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function deleteAllUsers() {\n    const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/delete-all-users`, {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${getTokenFromCookie(\"idToken\")}`\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting all users:\", response.statusText);\n        throw new Error(\"Failed to delete all users\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function getTotalUsers() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/total-users`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${token}`\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching total users:\", response.statusText);\n            throw new Error(\"Failed to fetch total users\");\n        }\n        const data = await response.json();\n        return data.totalUsers;\n    } catch (error) {\n        console.error(\"Error fetching total users:\", error);\n        throw error;\n    }\n}\nasync function getUserCountWithChange() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/users-count-change`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${token}`\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching user count change:\", response.statusText);\n            throw new Error(\"Failed to fetch user count change\");\n        }\n        const data = await response.json();\n        return {\n            totalUsersThisMonth: data.totalUsersThisMonth,\n            percentageChange: data.percentageChange\n        };\n    } catch (error) {\n        console.error(\"Error fetching user count change:\", error);\n        throw error;\n    }\n}\nasync function getMonthlyUserStarts() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/monthly-user-starts`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${token}`\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Failed to fetch monthly user stats:\", response.statusText);\n            throw new Error(\"Failed to fetch monthly user stats\");\n        }\n        const data = await response.json();\n        // data has type { [year: string]: MonthlyUserStat[] }\n        return data;\n    } catch (error) {\n        console.error(\"Error fetching monthly user stats:\", error);\n        throw error;\n    }\n}\nasync function resetParentPasswordService({ email, phone }) {\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"idToken\");\n    if (!token) {\n        throw new Error(\"You must be logged in to perform this action.\");\n    }\n    if (!email && !phone) {\n        throw new Error(\"Email or phone number is required to reset password.\");\n    }\n    const payload = {};\n    if (email) payload.email = email;\n    if (phone) payload.phone = phone;\n    const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/reset-parent-password`, {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${token}`\n        },\n        body: JSON.stringify(payload)\n    });\n    if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || \"Failed to reset password\");\n    }\n    const result = await response.json();\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/services/UserServices.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/utils/httpInterceptor.tsx":
/*!*******************************************!*\
  !*** ./src/app/utils/httpInterceptor.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticatedDelete: () => (/* binding */ authenticatedDelete),\n/* harmony export */   authenticatedFetch: () => (/* binding */ authenticatedFetch),\n/* harmony export */   authenticatedGet: () => (/* binding */ authenticatedGet),\n/* harmony export */   authenticatedPost: () => (/* binding */ authenticatedPost),\n/* harmony export */   authenticatedPut: () => (/* binding */ authenticatedPut),\n/* harmony export */   checkAuthStatus: () => (/* binding */ checkAuthStatus),\n/* harmony export */   clearSession: () => (/* binding */ clearSession),\n/* harmony export */   isTokenExpired: () => (/* binding */ isTokenExpired)\n/* harmony export */ });\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _services_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/AuthContext */ \"(ssr)/./src/app/services/AuthContext.tsx\");\n\n\n// Fonction pour gérer la déconnexion automatique\nconst handleUnauthorized = ()=>{\n    // Supprimer le token\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"idToken\");\n    // Rediriger vers login\n    if (false) {}\n};\n// Fonction pour obtenir le token\nconst getAuthToken = ()=>{\n    return js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"idToken\") || null;\n};\n// Intercepteur HTTP personnalisé\nconst authenticatedFetch = async (url, options = {})=>{\n    const token = getAuthToken();\n    // Si pas de token, rediriger immédiatement\n    if (!token) {\n        handleUnauthorized();\n        throw new Error(\"No authentication token found\");\n    }\n    // Ajouter le token aux headers\n    const headers = {\n        'Content-Type': 'application/json',\n        ...options.headers,\n        'Authorization': `Bearer ${token}`\n    };\n    // Construire l'URL complète si nécessaire\n    const fullUrl = url.startsWith('http') ? url : `${_services_AuthContext__WEBPACK_IMPORTED_MODULE_1__.BASE_API_URL}${url}`;\n    try {\n        const response = await fetch(fullUrl, {\n            ...options,\n            headers\n        });\n        // Vérifier si la réponse indique une erreur d'authentification\n        if (response.status === 401) {\n            console.warn(\"Token expired or invalid, redirecting to login\");\n            handleUnauthorized();\n            throw new Error(\"Authentication failed\");\n        }\n        // Vérifier si la réponse indique un token expiré\n        if (response.status === 403) {\n            const errorData = await response.clone().json().catch(()=>({}));\n            if (errorData.message?.includes('token') || errorData.message?.includes('expired')) {\n                console.warn(\"Token expired, redirecting to login\");\n                handleUnauthorized();\n                throw new Error(\"Token expired\");\n            }\n        }\n        return response;\n    } catch (error) {\n        // Si erreur réseau ou autre, vérifier si c'est lié à l'auth\n        if (error instanceof TypeError && error.message.includes('fetch')) {\n            console.error(\"Network error during authenticated request:\", error);\n        }\n        throw error;\n    }\n};\n// Wrapper pour les requêtes GET\nconst authenticatedGet = async (url)=>{\n    return authenticatedFetch(url, {\n        method: 'GET'\n    });\n};\n// Wrapper pour les requêtes POST\nconst authenticatedPost = async (url, data)=>{\n    return authenticatedFetch(url, {\n        method: 'POST',\n        body: JSON.stringify(data)\n    });\n};\n// Wrapper pour les requêtes PUT\nconst authenticatedPut = async (url, data)=>{\n    return authenticatedFetch(url, {\n        method: 'PUT',\n        body: JSON.stringify(data)\n    });\n};\n// Wrapper pour les requêtes DELETE\nconst authenticatedDelete = async (url, data)=>{\n    const options = {\n        method: 'DELETE'\n    };\n    if (data) {\n        options.body = JSON.stringify(data);\n    }\n    return authenticatedFetch(url, options);\n};\n// Fonction pour vérifier si l'utilisateur est toujours authentifié\nconst checkAuthStatus = async ()=>{\n    const token = getAuthToken();\n    if (!token) {\n        return false;\n    }\n    try {\n        // Faire une requête simple pour vérifier le token\n        const response = await authenticatedGet('/user/check-auth');\n        return response.ok;\n    } catch (error) {\n        console.error(\"Auth check failed:\", error);\n        return false;\n    }\n};\n// Fonction pour décoder et vérifier l'expiration du token JWT\nconst isTokenExpired = ()=>{\n    const token = getAuthToken();\n    if (!token) {\n        return true;\n    }\n    try {\n        // Décoder le token JWT (partie payload)\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        const currentTime = Math.floor(Date.now() / 1000);\n        // Vérifier si le token est expiré\n        return payload.exp < currentTime;\n    } catch (error) {\n        console.error(\"Error decoding token:\", error);\n        return true;\n    }\n};\n// Fonction pour nettoyer la session\nconst clearSession = ()=>{\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"idToken\");\n    if (false) {}\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/utils/httpInterceptor.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/NotificationCard.tsx":
/*!*********************************************!*\
  !*** ./src/components/NotificationCard.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n// Classe pour gérer les configurations des notifications\nclass NotificationConfig {\n    static getTitle(type) {\n        const titles = {\n            success: 'Success',\n            error: 'Error',\n            info: 'Information',\n            warning: 'Warning'\n        };\n        return titles[type];\n    }\n    static getIcon(type) {\n        switch(type){\n            case 'success':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 20 20\",\n                    fill: \"none\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM8 15L3 10L4.41 8.59L8 12.17L15.59 4.58L17 6L8 15Z\",\n                        fill: \"#15803d\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 11\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 20 20\",\n                    fill: \"none\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M8.4375 6.5625C8.4375 6.31527 8.51081 6.0736 8.64817 5.86804C8.78552 5.66248 8.98074 5.50226 9.20915 5.40765C9.43756 5.31304 9.68889 5.28829 9.93137 5.33652C10.1738 5.38475 10.3966 5.5038 10.5714 5.67862C10.7462 5.85343 10.8653 6.07616 10.9135 6.31864C10.9617 6.56111 10.937 6.81245 10.8424 7.04085C10.7477 7.26926 10.5875 7.46448 10.382 7.60184C10.1764 7.73919 9.93473 7.8125 9.6875 7.8125C9.35598 7.8125 9.03804 7.6808 8.80362 7.44638C8.5692 7.21196 8.4375 6.89402 8.4375 6.5625ZM18.4375 10C18.4375 11.6688 17.9427 13.3001 17.0155 14.6876C16.0884 16.0752 14.7706 17.1566 13.2289 17.7952C11.6871 18.4338 9.99064 18.6009 8.35393 18.2754C6.71721 17.9498 5.2138 17.1462 4.03379 15.9662C2.85378 14.7862 2.05019 13.2828 1.72463 11.6461C1.39907 10.0094 1.56616 8.31286 2.20477 6.77111C2.84338 5.22936 3.92484 3.9116 5.31238 2.98448C6.69992 2.05735 8.33122 1.5625 10 1.5625C12.237 1.56498 14.3817 2.45473 15.9635 4.03653C17.5453 5.61833 18.435 7.763 18.4375 10ZM16.5625 10C16.5625 8.70206 16.1776 7.43327 15.4565 6.35407C14.7354 5.27487 13.7105 4.43374 12.5114 3.93704C11.3122 3.44034 9.99272 3.31038 8.71972 3.5636C7.44672 3.81681 6.2774 4.44183 5.35962 5.35961C4.44183 6.27739 3.81682 7.44672 3.5636 8.71972C3.31038 9.99272 3.44034 11.3122 3.93704 12.5114C4.43374 13.7105 5.27488 14.7354 6.35407 15.4565C7.43327 16.1776 8.70206 16.5625 10 16.5625C11.7399 16.5606 13.408 15.8686 14.6383 14.6383C15.8686 13.408 16.5606 11.7399 16.5625 10ZM10.9375 12.8656V10.3125C10.9375 9.8981 10.7729 9.50067 10.4799 9.20764C10.1868 8.91462 9.7894 8.75 9.375 8.75C9.1536 8.74967 8.93923 8.82771 8.76986 8.97029C8.60048 9.11287 8.48703 9.31079 8.4496 9.52901C8.41217 9.74722 8.45318 9.97164 8.56536 10.1625C8.67754 10.3534 8.85365 10.4984 9.0625 10.5719V13.125C9.0625 13.5394 9.22712 13.9368 9.52015 14.2299C9.81317 14.5229 10.2106 14.6875 10.625 14.6875C10.8464 14.6878 11.0608 14.6098 11.2301 14.4672C11.3995 14.3246 11.513 14.1267 11.5504 13.9085C11.5878 13.6903 11.5468 13.4659 11.4346 13.275C11.3225 13.0841 11.1464 12.9391 10.9375 12.8656Z\",\n                        fill: \"#F43F5E\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 11\n                }, this);\n            case 'info':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 20 20\",\n                    fill: \"none\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM11 15H9V9H11V15ZM11 7H9V5H11V7Z\",\n                        fill: \"#3b82f6\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 11\n                }, this);\n            case 'warning':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 20 20\",\n                    fill: \"none\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM11 15H9V13H11V15ZM11 11H9V5H11V11Z\",\n                        fill: \"#eab308\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 11\n                }, this);\n            default:\n                return null;\n        }\n    }\n    static getStyles(type) {\n        const styles = {\n            success: 'bg-green-100 border-green-500 text-green-700',\n            error: 'bg-red-100 border-red-500 text-red-700',\n            info: 'bg-blue-100 border-blue-500 text-blue-700',\n            warning: 'bg-yellow-100 border-yellow-500 text-yellow-700'\n        };\n        return styles[type];\n    }\n}\nfunction NotificationCard({ title, message, icon, type, isVisible, isFixed = false, autoClose = true, duration = 5000, onClose }) {\n    // Si la notification n'est pas visible, on ne rend rien\n    if (!isVisible) return null;\n    // Utiliser le titre par défaut si aucun n'est fourni\n    const displayTitle = title || NotificationConfig.getTitle(type);\n    // Utiliser l'icône par défaut si aucune n'est fournie\n    const displayIcon = icon || NotificationConfig.getIcon(type);\n    // Obtenir les styles en fonction du type\n    const typeStyle = NotificationConfig.getStyles(type);\n    // Fermer automatiquement la notification après la durée spécifiée\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationCard.useEffect\": ()=>{\n            let timer;\n            if (isVisible && autoClose) {\n                timer = setTimeout({\n                    \"NotificationCard.useEffect\": ()=>{\n                        onClose();\n                    }\n                }[\"NotificationCard.useEffect\"], duration);\n            }\n            return ({\n                \"NotificationCard.useEffect\": ()=>{\n                    if (timer) clearTimeout(timer);\n                }\n            })[\"NotificationCard.useEffect\"];\n        }\n    }[\"NotificationCard.useEffect\"], [\n        isVisible,\n        autoClose,\n        duration,\n        onClose\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `mb-4 top-4 right-4 w-full p-4 rounded-lg shadow-lg border-l-4 ${typeStyle} flex items-start space-x-3 transition-all duration-300 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-2'} ${isFixed ? 'fixed max-w-sm z-50' : ''}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0\",\n                children: displayIcon\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-semibold\",\n                        children: displayTitle\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm\",\n                        children: message\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onClose,\n                className: \"flex-shrink-0 text-gray-500 hover:text-gray-700 focus:outline-none\",\n                \"aria-label\": \"Close notification\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"h-5 w-5\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: \"2\",\n                        d: \"M6 18L18 6M6 6l12 12\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n        lineNumber: 112,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/NotificationCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/utils/ProtectedRoute.tsx":
/*!*************************************************!*\
  !*** ./src/components/utils/ProtectedRoute.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(ssr)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var _widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../widgets/CircularLoader */ \"(ssr)/./src/components/widgets/CircularLoader.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst ProtectedRoute = ({ children, allowedRoles = [] })=>{\n    const { isAuthenticated, loading, setRedirectAfterLogin, user } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProtectedRoute.useEffect\": ()=>{\n            if (!loading && !isAuthenticated) {\n                // Stocker l'URL actuelle pour rediriger après la connexion\n                setRedirectAfterLogin(pathname);\n                router.push(\"/login\");\n            }\n            if (user && allowedRoles.length > 0) {\n                const hasRequiredRole = allowedRoles.some({\n                    \"ProtectedRoute.useEffect.hasRequiredRole\": (role)=>user.role?.toLowerCase() === role.toLowerCase()\n                }[\"ProtectedRoute.useEffect.hasRequiredRole\"]);\n                if (!hasRequiredRole) {\n                    router.push('/unauthorized');\n                }\n            }\n        }\n    }[\"ProtectedRoute.useEffect\"], [\n        loading,\n        isAuthenticated,\n        router,\n        pathname,\n        setRedirectAfterLogin,\n        user,\n        allowedRoles\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-screen w-full absolute top-0 left-0 z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: 40,\n                color: \"teal\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\ProtectedRoute.tsx\",\n                lineNumber: 39,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\ProtectedRoute.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!isAuthenticated) {\n        return null; // La redirection est gérée par useEffect\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProtectedRoute);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/utils/ProtectedRoute.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/widgets/CircularLoader.tsx":
/*!***************************************************!*\
  !*** ./src/components/widgets/CircularLoader.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst CircularLoader = ({ size = 32, color = \"teal\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex justify-center items-center h-full w-full `,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `animate-spin rounded-full border-t-2 border-r-2 border-${color}`,\n            style: {\n                width: `${size}px`,\n                height: `${size}px`,\n                borderColor: \"transparent\",\n                borderTopColor: `var(--${color})`,\n                borderRightColor: `var(--${color})`\n            }\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\CircularLoader.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\CircularLoader.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CircularLoader);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/widgets/CircularLoader.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/widgets/Logo.tsx":
/*!*****************************************!*\
  !*** ./src/components/widgets/Logo.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Logo = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"122\",\n        height: \"41\",\n        viewBox: \"0 0 132 51\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M1.99955 34.0951C18.9362 35.1545 21.0412 1.05936 37.9779 2.11872C54.9146 3.17808 53.8621 20.2257 71.8513 4.23744\",\n                stroke: \"#1E3D59\",\n                strokeWidth: \"3.71063\",\n                strokeLinecap: \"round\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\Logo.tsx\",\n                lineNumber: 6,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M2.66672 27.2071C19.6034 28.2664 20.8664 7.80936 37.8031 8.86872C54.7398 9.92808 53.6873 26.9757 71.6765 10.9874\",\n                stroke: \"#17B890\",\n                strokeWidth: \"3.71063\",\n                strokeLinecap: \"round\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\Logo.tsx\",\n                lineNumber: 7,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M23.7439 43.447C22.6315 43.447 21.639 43.3358 20.7662 43.1133C19.8934 42.9079 19.1489 42.5828 18.5328 42.1378C17.9339 41.6757 17.4718 41.1195 17.1466 40.4692C16.8215 39.8018 16.6503 39.0231 16.6332 38.1332L19.5083 37.2861C19.4912 38.1076 19.6623 38.7921 20.0217 39.3397C20.3811 39.8703 20.8945 40.2639 21.5619 40.5206C22.2465 40.7773 23.0252 40.9056 23.8979 40.9056C24.7365 40.9056 25.4382 40.8029 26.0029 40.5976C26.5848 40.3922 27.0212 40.1184 27.3121 39.7761C27.603 39.4167 27.7485 39.0146 27.7485 38.5696C27.7485 38.0733 27.5688 37.6797 27.2094 37.3888C26.85 37.0807 26.3623 36.8326 25.7462 36.6443C25.1472 36.439 24.4627 36.2507 23.6926 36.0796C22.854 35.8913 22.0326 35.686 21.2282 35.4635C20.441 35.241 19.7222 34.9415 19.0719 34.565C18.4216 34.1885 17.8996 33.7094 17.506 33.1275C17.1295 32.5285 16.9413 31.7755 16.9413 30.8685C16.9413 29.8417 17.198 28.9603 17.7114 28.2245C18.2248 27.4715 18.9692 26.8896 19.9447 26.4789C20.9373 26.051 22.1181 25.8371 23.4872 25.8371C24.8392 25.8371 26.02 26.0425 27.0297 26.4532C28.0394 26.8468 28.8267 27.4287 29.3914 28.1988C29.9562 28.9689 30.2556 29.8759 30.2899 30.9198L27.3378 31.7413C27.3378 31.1765 27.2437 30.6888 27.0554 30.2781C26.8843 29.8502 26.6276 29.4994 26.2853 29.2256C25.943 28.9347 25.5323 28.7207 25.0531 28.5838C24.591 28.4469 24.0605 28.3785 23.4615 28.3785C22.7599 28.3785 22.1438 28.4726 21.6133 28.6608C21.0999 28.8491 20.7063 29.1144 20.4324 29.4566C20.1586 29.7818 20.0217 30.1668 20.0217 30.6118C20.0217 31.1594 20.2271 31.5958 20.6378 31.921C21.0485 32.2461 21.5876 32.5114 22.255 32.7168C22.9396 32.905 23.684 33.0933 24.4884 33.2815C25.2242 33.4355 25.9601 33.6238 26.696 33.8463C27.449 34.0687 28.1421 34.3597 28.7753 34.7191C29.4085 35.0784 29.9134 35.5662 30.2899 36.1823C30.6835 36.7813 30.8803 37.5428 30.8803 38.4669C30.8803 39.4595 30.615 40.3323 30.0845 41.0853C29.554 41.8383 28.7582 42.4202 27.6972 42.8309C26.6361 43.2416 25.3184 43.447 23.7439 43.447ZM38.6189 43.447C37.4552 43.447 36.4455 43.2759 35.5898 42.9336C34.7512 42.5913 34.0496 42.1121 33.4848 41.496C32.9372 40.8628 32.5265 40.127 32.2527 39.2884C31.9788 38.4498 31.8419 37.5428 31.8419 36.5673C31.8419 35.5234 31.9788 34.565 32.2527 33.6922C32.5436 32.8195 32.9714 32.0579 33.5362 31.4076C34.1009 30.7573 34.794 30.2524 35.6155 29.893C36.454 29.5336 37.4124 29.354 38.4906 29.354C39.5687 29.354 40.5014 29.5336 41.2886 29.893C42.093 30.2524 42.7433 30.7573 43.2396 31.4076C43.7359 32.0408 44.0696 32.7852 44.2407 33.6409L41.2116 34.5907C41.1774 34.0773 41.0405 33.6238 40.8009 33.2302C40.5613 32.8195 40.2361 32.5029 39.8254 32.2804C39.4147 32.0579 38.927 31.9467 38.3622 31.9467C37.8146 31.9467 37.3354 32.0579 36.9247 32.2804C36.5311 32.4857 36.1973 32.7852 35.9235 33.1788C35.6668 33.5725 35.4615 34.0516 35.3074 34.6164C35.1705 35.164 35.1021 35.7801 35.1021 36.4646C35.1021 37.4059 35.2304 38.2102 35.4871 38.8777C35.7438 39.5451 36.1374 40.0671 36.668 40.4436C37.1985 40.8029 37.8574 40.9826 38.6446 40.9826C39.3462 40.9826 39.911 40.8543 40.3388 40.5976C40.7838 40.3238 41.1175 39.9729 41.34 39.5451C41.5796 39.1173 41.725 38.6466 41.7764 38.1332L44.6515 38.775C44.5488 39.4766 44.352 40.1098 44.061 40.6746C43.7701 41.2393 43.3679 41.7356 42.8545 42.1635C42.3582 42.5742 41.7593 42.8908 41.0576 43.1133C40.3559 43.3358 39.543 43.447 38.6189 43.447ZM45.6672 43.1133V34.7704V24.8873H48.9274V29.0716C48.9274 29.431 48.9103 29.7989 48.876 30.1754C48.8589 30.5519 48.8247 30.937 48.7734 31.3306C48.722 31.7242 48.6621 32.1178 48.5937 32.5114C48.5423 32.905 48.4824 33.2986 48.414 33.6922H48.9531C49.1926 32.7681 49.5007 31.9894 49.8772 31.3562C50.2537 30.7059 50.7329 30.2096 51.3147 29.8674C51.9137 29.5251 52.641 29.354 53.4967 29.354C55.054 29.354 56.2178 29.9016 56.9879 30.9969C57.758 32.075 58.1431 33.7179 58.1431 35.9256V43.1133H54.8829V36.3876C54.8829 34.9159 54.6604 33.8292 54.2155 33.1275C53.7876 32.4258 53.1373 32.075 52.2645 32.075C51.5458 32.075 50.9468 32.2975 50.4676 32.7424C49.9884 33.1703 49.6205 33.7436 49.3638 34.4624C49.1071 35.1811 48.9531 35.994 48.9017 36.901V43.1133H45.6672ZM66.2328 43.447C64.9151 43.447 63.7514 43.1817 62.7417 42.6512C61.732 42.1036 60.9362 41.3078 60.3543 40.2639C59.7896 39.2028 59.5072 37.9022 59.5072 36.362C59.5072 34.7875 59.7981 33.4869 60.38 32.4601C60.979 31.4161 61.7833 30.6375 62.793 30.1241C63.8027 29.6107 64.9408 29.354 66.2072 29.354C67.5249 29.354 68.6886 29.6192 69.6983 30.1497C70.708 30.6803 71.5038 31.476 72.0857 32.5371C72.6675 33.581 72.9585 34.8816 72.9585 36.439C72.9585 38.0134 72.6675 39.3226 72.0857 40.3665C71.5038 41.4105 70.6995 42.1891 69.6727 42.7026C68.663 43.1988 67.5164 43.447 66.2328 43.447ZM66.3612 40.9826C67.08 40.9826 67.6875 40.8201 68.1838 40.4949C68.6801 40.1526 69.0566 39.6478 69.3133 38.9803C69.5871 38.3129 69.724 37.5171 69.724 36.593C69.724 35.6175 69.5785 34.7875 69.2876 34.103C69.0138 33.4013 68.6116 32.8622 68.0811 32.4857C67.5677 32.1092 66.9174 31.921 66.1302 31.921C65.4456 31.921 64.8466 32.0921 64.3332 32.4344C63.8198 32.7596 63.4262 33.2473 63.1524 33.8976C62.8957 34.5479 62.7673 35.3523 62.7673 36.3106C62.7673 37.8166 63.0839 38.9718 63.7171 39.7761C64.3675 40.5805 65.2488 40.9826 66.3612 40.9826ZM74.5865 43.1133V24.8873H77.7697V43.1133H74.5865ZM83.2278 43.447C82.492 43.447 81.8331 43.3101 81.2512 43.0363C80.6694 42.7453 80.2073 42.3175 79.865 41.7528C79.5399 41.188 79.3773 40.4863 79.3773 39.6478C79.3773 38.9461 79.5056 38.3471 79.7623 37.8509C80.0362 37.3546 80.4383 36.9524 80.9688 36.6443C81.4994 36.3192 82.1582 36.0539 82.9455 35.8486C83.7327 35.6261 84.6568 35.4378 85.7179 35.2838C86.2997 35.1982 86.7704 35.1127 87.1297 35.0271C87.5062 34.9415 87.7801 34.8132 87.9512 34.6421C88.1223 34.4538 88.2079 34.18 88.2079 33.8206C88.2079 33.2901 88.0196 32.8366 87.6431 32.4601C87.2838 32.0665 86.6933 31.8696 85.8719 31.8696C85.3756 31.8696 84.8964 31.9552 84.4343 32.1264C83.9894 32.2975 83.6043 32.5713 83.2792 32.9478C82.954 33.3072 82.7315 33.7864 82.6117 34.3853L79.711 33.4869C79.8992 32.8023 80.1731 32.2119 80.5324 31.7156C80.8918 31.2022 81.3368 30.7658 81.8673 30.4064C82.3978 30.0471 83.0054 29.7818 83.6899 29.6107C84.3744 29.4395 85.1274 29.354 85.9489 29.354C87.1982 29.354 88.225 29.5593 89.0293 29.97C89.8337 30.3637 90.4327 30.9883 90.8263 31.844C91.2199 32.6825 91.4167 33.7693 91.4167 35.1041V37.6198C91.4167 38.2017 91.4253 38.8092 91.4424 39.4424C91.4766 40.0585 91.5108 40.6832 91.545 41.3164C91.5964 41.9324 91.6563 42.5314 91.7247 43.1133H88.824C88.7555 42.6341 88.6956 42.1207 88.6443 41.5731C88.5929 41.0254 88.5502 40.4692 88.5159 39.9045H88.1052C87.8656 40.5548 87.5234 41.1538 87.0784 41.7014C86.6334 42.2319 86.0858 42.6598 85.4355 42.9849C84.8023 43.293 84.0664 43.447 83.2278 43.447ZM84.5114 41.0596C84.8536 41.0596 85.1959 40.9998 85.5382 40.88C85.8976 40.7602 86.2484 40.5805 86.5907 40.3409C86.9329 40.1013 87.2495 39.8104 87.5405 39.4681C87.8314 39.1087 88.0795 38.6894 88.2849 38.2102L88.2336 35.9256L88.8497 36.0283C88.5587 36.2678 88.1993 36.4561 87.7715 36.593C87.3437 36.7299 86.8901 36.8326 86.411 36.901C85.9489 36.9695 85.4783 37.0551 84.9991 37.1577C84.537 37.2433 84.1177 37.3631 83.7412 37.5171C83.3647 37.654 83.0653 37.8594 82.8428 38.1332C82.6203 38.3899 82.5091 38.7493 82.5091 39.2114C82.5091 39.7932 82.6973 40.2468 83.0738 40.5719C83.4503 40.8971 83.9295 41.0596 84.5114 41.0596ZM93.6288 43.1133V36.3876V29.6877H96.3498L96.2985 34.3083H96.7606C96.9146 33.2131 97.1542 32.306 97.4793 31.5873C97.8045 30.8514 98.258 30.3038 98.8399 29.9444C99.4217 29.5679 100.14 29.3796 100.996 29.3796C101.167 29.3796 101.347 29.3882 101.535 29.4053C101.741 29.4224 101.963 29.4652 102.203 29.5336L102.074 32.9478C101.801 32.8451 101.518 32.7767 101.227 32.7424C100.953 32.6911 100.697 32.6654 100.457 32.6654C99.7897 32.6654 99.2078 32.8366 98.7115 33.1788C98.2323 33.5211 97.8387 34.0088 97.5307 34.6421C97.2397 35.2581 97.0173 35.994 96.8632 36.8497V43.1133H93.6288Z\",\n                fill: \"#1E3D59\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\Logo.tsx\",\n                lineNumber: 8,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M102.944 43.1133V29.5593H107.077V43.1133H102.944ZM105.023 27.6597C104.236 27.6597 103.628 27.4971 103.201 27.172C102.79 26.8297 102.585 26.3505 102.585 25.7344C102.585 25.0841 102.79 24.5964 103.201 24.2712C103.628 23.9289 104.236 23.7578 105.023 23.7578C105.828 23.7578 106.435 23.9289 106.846 24.2712C107.274 24.5964 107.488 25.0756 107.488 25.7088C107.488 26.342 107.274 26.8297 106.846 27.172C106.435 27.4971 105.828 27.6597 105.023 27.6597ZM110.032 43.1133V34.7191H107.953V31.7156L111.65 31.8696V31.3819C110.999 31.2621 110.46 31.0482 110.032 30.7402C109.622 30.415 109.322 30.0385 109.134 29.6107C108.946 29.1828 108.851 28.7207 108.851 28.2245C108.851 27.4715 109.048 26.8297 109.442 26.2992C109.836 25.7515 110.392 25.3323 111.11 25.0413C111.829 24.7504 112.668 24.6049 113.626 24.6049C114.465 24.6049 115.226 24.7076 115.911 24.913C116.595 25.1012 117.143 25.3408 117.554 25.6317L117.323 29.354C116.895 29.063 116.407 28.8234 115.86 28.6352C115.329 28.4469 114.833 28.3528 114.371 28.3528C113.891 28.3528 113.489 28.4812 113.164 28.7379C112.839 28.9775 112.676 29.3796 112.676 29.9444C112.676 30.3893 112.771 30.723 112.959 30.9455C113.164 31.168 113.412 31.322 113.703 31.4076C113.994 31.476 114.285 31.5103 114.576 31.5103H117.554V34.7191H114.063V43.1133H110.032ZM123.412 47.8366C122.727 47.8366 122.026 47.7767 121.307 47.6569C120.588 47.5371 119.904 47.3575 119.253 47.1179C118.62 46.8954 118.072 46.5959 117.61 46.2194L118.611 42.5485C119.21 43.1133 119.929 43.5326 120.768 43.8064C121.623 44.0802 122.445 44.2171 123.232 44.2171C124.139 44.2171 124.884 44.0374 125.465 43.678C126.064 43.3358 126.518 42.8052 126.826 42.0865C127.151 41.3506 127.339 40.4436 127.391 39.3654L127.519 37.1321H126.903C126.749 38.2445 126.475 39.1515 126.082 39.8531C125.705 40.5548 125.217 41.0768 124.618 41.419C124.036 41.7442 123.343 41.9068 122.539 41.9068C121.546 41.9068 120.716 41.6586 120.049 41.1623C119.382 40.666 118.877 39.9216 118.534 38.929C118.209 37.9193 118.047 36.67 118.047 35.1811V29.5593H122.205V34.1543C122.205 35.7288 122.359 36.8583 122.667 37.5428C122.993 38.2102 123.497 38.544 124.182 38.544C124.541 38.544 124.866 38.4413 125.157 38.2359C125.448 38.0305 125.696 37.7396 125.902 37.3631C126.107 36.9866 126.261 36.5331 126.364 36.0026C126.484 35.4549 126.544 34.8474 126.544 34.18V29.5593H130.677V39.5194C130.677 40.5976 130.574 41.5816 130.369 42.4715C130.18 43.3614 129.889 44.1401 129.496 44.8075C129.119 45.475 128.64 46.0311 128.058 46.4761C127.476 46.9382 126.8 47.2804 126.03 47.5029C125.26 47.7254 124.387 47.8366 123.412 47.8366Z\",\n                fill: \"#17B890\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\Logo.tsx\",\n                lineNumber: 9,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M71.1029 5.15342C71.7118 5.69511 72.6417 5.63739 73.1798 5.0245C73.718 4.41161 73.6606 3.47563 73.0517 2.93394C72.4428 2.39225 71.5129 2.44997 70.9748 3.06286C70.4366 3.67576 70.494 4.61173 71.1029 5.15342Z\",\n                fill: \"#17B890\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\Logo.tsx\",\n                lineNumber: 10,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\Logo.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Logo);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/widgets/Logo.tsx\n");

/***/ }),

/***/ "(ssr)/./src/utils/ThemeInitializer.tsx":
/*!****************************************!*\
  !*** ./src/utils/ThemeInitializer.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeInitializer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction ThemeInitializer() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"ThemeInitializer.useEffect\": ()=>{\n            if (localStorage.getItem(\"theme\") === \"dark\") {\n                document.documentElement.classList.add(\"dark\");\n            } else {\n                document.documentElement.classList.remove(\"dark\");\n            }\n        }\n    }[\"ThemeInitializer.useEffect\"], []);\n    return null; // This component only runs once to apply theme\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdXRpbHMvVGhlbWVJbml0aWFsaXplci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OzZEQUNrQztBQUVuQixTQUFTQztJQUN0QkQsZ0RBQVNBO3NDQUFDO1lBQ1IsSUFBSUUsYUFBYUMsT0FBTyxDQUFDLGFBQWEsUUFBUTtnQkFDNUNDLFNBQVNDLGVBQWUsQ0FBQ0MsU0FBUyxDQUFDQyxHQUFHLENBQUM7WUFDekMsT0FBTztnQkFDTEgsU0FBU0MsZUFBZSxDQUFDQyxTQUFTLENBQUNFLE1BQU0sQ0FBQztZQUM1QztRQUNGO3FDQUFHLEVBQUU7SUFFTCxPQUFPLE1BQU0sK0NBQStDO0FBQzlEIiwic291cmNlcyI6WyJEOlxcUHJvamV0XFxzY2hvbGFyaWZ5XFxkYXNoYm9hcmRcXHNyY1xcdXRpbHNcXFRoZW1lSW5pdGlhbGl6ZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5pbXBvcnQgeyB1c2VFZmZlY3QgfSBmcm9tIFwicmVhY3RcIjtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFRoZW1lSW5pdGlhbGl6ZXIoKSB7XHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChsb2NhbFN0b3JhZ2UuZ2V0SXRlbShcInRoZW1lXCIpID09PSBcImRhcmtcIikge1xyXG4gICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuY2xhc3NMaXN0LmFkZChcImRhcmtcIik7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuY2xhc3NMaXN0LnJlbW92ZShcImRhcmtcIik7XHJcbiAgICB9XHJcbiAgfSwgW10pO1xyXG5cclxuICByZXR1cm4gbnVsbDsgLy8gVGhpcyBjb21wb25lbnQgb25seSBydW5zIG9uY2UgdG8gYXBwbHkgdGhlbWVcclxufVxyXG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwiVGhlbWVJbml0aWFsaXplciIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJkb2N1bWVudCIsImRvY3VtZW50RWxlbWVudCIsImNsYXNzTGlzdCIsImFkZCIsInJlbW92ZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/ThemeInitializer.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/js-cookie","vendor-chunks/jwt-decode","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/lucide-react","vendor-chunks/motion-utils"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(dashboards)%2Fteacher-dashboard%2Fpage&page=%2F(dashboards)%2Fteacher-dashboard%2Fpage&appPaths=%2F(dashboards)%2Fteacher-dashboard%2Fpage&pagePath=private-next-app-dir%2F(dashboards)%2Fteacher-dashboard%2Fpage.tsx&appDir=D%3A%5CProjet%5Cscholarify%5Cdashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjet%5Cscholarify%5Cdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();