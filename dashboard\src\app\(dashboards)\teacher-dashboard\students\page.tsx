"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { GraduationCap, Users, BookOpen } from "lucide-react";
import TeacherLayout from "@/components/Dashboard/Layouts/TeacherLayout";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import CircularLoader from "@/components/widgets/CircularLoader";

interface SelectedSchool {
  school_id: string;
  school_name: string;
  access_granted_at: string;
}

const navigation = {
  icon: GraduationCap,
  baseHref: "/teacher-dashboard/students",
  title: "My Students"
};

export default function TeacherStudentsPage() {
  const { user, logout } = useAuth();
  const router = useRouter();

  const [selectedSchool, setSelectedSchool] = useState<SelectedSchool | null>(null);
  const [loading, setLoading] = useState(true);
  const [students, setStudents] = useState<any[]>([]);
  const [stats, setStats] = useState({
    totalStudents: 0,
    activeClasses: 0,
    attendanceRate: 0
  });

  useEffect(() => {
    // Check if user is a teacher
    if (user && user.role !== "teacher") {
      router.push("/dashboard");
      return;
    }

    // Get selected school from localStorage
    const storedSchool = localStorage.getItem("teacher_selected_school");
    if (storedSchool) {
      try {
        const school = JSON.parse(storedSchool);
        setSelectedSchool(school);
        // Load students data when school is set
        loadStudentsData(school.school_id);
      } catch (error) {
        console.error("Error parsing stored school:", error);
        router.push("/teacher-dashboard");
      }
    } else {
      // No school selected, redirect to school selection
      router.push("/teacher-dashboard");
    }

    setLoading(false);
  }, [user, router]);

  const loadStudentsData = async (schoolId: string) => {
    try {
      // Import services dynamically to avoid SSR issues
      const { getTeacherStudents } = await import("@/app/services/TeacherAssignmentServices");
      const { getTeacherPermissions } = await import("@/app/services/TeacherPermissionServices");

      // Get students and teacher data
      const [studentsData, teacherData] = await Promise.all([
        getTeacherStudents(schoolId),
        getTeacherPermissions(schoolId)
      ]);

      setStudents(studentsData);

      // Calculate stats
      const totalStudents = studentsData.length;
      const activeClasses = teacherData.assigned_classes.length;

      // TODO: Calculate actual attendance rate from attendance records
      const attendanceRate = totalStudents > 0 ? 85 : 0; // Placeholder

      setStats({
        totalStudents,
        activeClasses,
        attendanceRate
      });

    } catch (error) {
      console.error("Error loading students data:", error);
      setStudents([]);
      setStats({ totalStudents: 0, activeClasses: 0, attendanceRate: 0 });
    }
  };

  const handleSchoolChange = () => {
    localStorage.removeItem("teacher_selected_school");
    router.push("/teacher-dashboard");
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <CircularLoader size={32} color="teal" />
      </div>
    );
  }

  return (
    <ProtectedRoute allowedRoles={["teacher"]}>
      <TeacherLayout
        navigation={navigation}
        selectedSchool={selectedSchool ? {
          _id: selectedSchool.school_id,
          name: selectedSchool.school_name
        } : null}
        onSchoolChange={handleSchoolChange}
        onLogout={logout}
      >
        <div className="space-y-6">
          {/* Header */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                <GraduationCap className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-foreground">My Students</h1>
                <p className="text-foreground/60">
                  Manage and view your students at {selectedSchool?.school_name}
                </p>
              </div>
            </div>
          </div>

          {/* Students Content */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            {students.length === 0 ? (
              <div className="text-center py-12">
                <Users className="h-16 w-16 text-foreground/30 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-foreground mb-2">No Students Found</h3>
                <p className="text-foreground/60 mb-6">
                  {loading ? "Loading your students..." : "No students are assigned to your classes yet."}
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-md mx-auto">
                  <button
                    onClick={() => router.push("/teacher-dashboard/classes")}
                    className="flex items-center justify-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 transition-colors"
                  >
                    <BookOpen size={16} />
                    <span>View Classes</span>
                  </button>

                  <button
                    onClick={() => router.push("/teacher-dashboard/dashboard")}
                    className="flex items-center justify-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-foreground rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                  >
                    <span>Back to Dashboard</span>
                  </button>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-foreground">Your Students</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {students.map((student) => (
                    <div key={student._id} className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                          <span className="text-white font-medium">
                            {student.first_name.charAt(0)}{student.last_name.charAt(0)}
                          </span>
                        </div>
                        <div>
                          <h4 className="font-medium text-foreground">
                            {student.first_name} {student.last_name}
                          </h4>
                          <p className="text-sm text-foreground/60">{student.class_name}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Total Students</p>
                  <p className="text-2xl font-bold text-foreground">{stats.totalStudents}</p>
                </div>
                <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                  <Users className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Active Classes</p>
                  <p className="text-2xl font-bold text-foreground">{stats.activeClasses}</p>
                </div>
                <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                  <BookOpen className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Attendance Rate</p>
                  <p className="text-2xl font-bold text-foreground">{stats.attendanceRate}%</p>
                </div>
                <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                  <GraduationCap className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </TeacherLayout>
    </ProtectedRoute>
  );
}
