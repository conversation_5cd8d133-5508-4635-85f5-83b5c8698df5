"use client";

import SuperLayout from '@/components/Dashboard/Layouts/SuperLayout';
import CircularLoader from '@/components/widgets/CircularLoader';
import React, { Suspense, useEffect, useState } from 'react';
import { Coins } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { CreditTransactionSchema } from '@/app/models/CreditTransactionModel';
import { getSchools } from '@/app/services/SchoolServices';
import { getCreditTransactions } from '@/app/services/CreditTransactionServices';
import { SchoolSchema } from '@/app/models/SchoolModel';
import Link from 'next/link';
import DataTableFix from '@/components/utils/TableFix';
import CreditTransactionModal from './components/CreditTransactionModal';

export default function Page() {
    const BASE_URL = "/super-admin";

    const navigation = {
        icon: Coins,
        baseHref: `${BASE_URL}/credit`,
        title: "Credit Transactions",
    };

    function Credit() {
        const router = useRouter();
        const [transactions, setTransactions] = useState<CreditTransactionSchema[]>([]);
        const [schools, setSchools] = useState<SchoolSchema[]>([]);
        const [loadingData, setLoadingData] = useState(false);


        useEffect(() => {
            const fetchSchools = async () => {
                setLoadingData(true);
                try {
                    const fetchedTransactions = await getCreditTransactions();
                    const fetchedSchools = await getSchools();
                    setTransactions(fetchedTransactions);
                    setSchools(fetchedSchools);
                } catch (error) {
                    console.error("Error fetching schools or credits:", error);
                } finally {
                    setLoadingData(false);
                }
            };
            fetchSchools();
        }, []);

        console.log("Credit transactions:", transactions);
        const getTransactionCount = (transId: string) => {
            return transactions.filter(tr => tr.school_id === transId).length;
        };
        const columns = [
            { header: "School ID", accessor: (row: SchoolSchema) => row.school_id },
            { header: "School Name", accessor: (row: SchoolSchema) => { return <Link href={`${BASE_URL}/schools/view?id=${row.school_id}`}>{row.name}</Link>; } },
            { header: "Address", accessor: (row: SchoolSchema) => row.address },
            { header: "Transactions", accessor: (row: SchoolSchema) => getTransactionCount(row._id) },
        ];

        const actions = [
            {
                label: "manage",
                onClick: (school: SchoolSchema) => {
                    // Navigate to the list of classes for the specific school
                    router.push(`${BASE_URL}/credit/manage?id=${school._id}`);
                },
            },
        ];
        return (
            <div className="">

                <DataTableFix
                    columns={columns}
                    data={schools}
                    actions={actions}
                    defaultItemsPerPage={5}
                    loading={loadingData}
                    onLoadingChange={setLoadingData}
                    showCheckbox={false}
                />
            </div>
        );
    }


    return (
        <Suspense fallback={
            <div className="flex justify-center items-center h-screen absolute top-0 left-0 z-50">
                <CircularLoader size={32} color="teal" />
            </div>
        }>
            <SuperLayout
                navigation={navigation}
                showGoPro={true}
                onLogout={() => console.log("Logged out")}
            >
                <Credit />
            </SuperLayout>
        </Suspense>
    );
}


