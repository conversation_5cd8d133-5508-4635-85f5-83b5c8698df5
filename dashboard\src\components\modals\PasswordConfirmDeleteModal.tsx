"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash2, <PERSON>, <PERSON>Off } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import CircularLoader from "@/components/widgets/CircularLoader";

interface PasswordConfirmDeleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (password: string) => Promise<void>;
  title: string;
  message: string;
  itemName?: string;
  itemCount?: number;
  type?: "single" | "multiple";
  loading?: boolean;
}

export default function PasswordConfirmDeleteModal({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  itemName,
  itemCount,
  type = "single",
  loading = false
}: PasswordConfirmDeleteModalProps) {
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);

  const handleFirstConfirm = () => {
    setShowConfirmation(true);
  };

  const handleFinalConfirm = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!password.trim()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onConfirm(password);
      handleClose();
    } catch (error) {
      console.error("Delete error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setPassword("");
    setShowPassword(false);
    setShowConfirmation(false);
    setIsSubmitting(false);
    onClose();
  };

  const getButtonText = () => {
    if (isSubmitting) return "Deleting...";
    
    switch (type) {
      case "multiple":
        return `Delete ${itemCount || 0} Items`;
      default:
        return "Delete";
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-widget rounded-lg shadow-xl w-full max-w-md"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-stroke">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center">
                <AlertTriangle className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-foreground">
                  {title}
                </h3>
                <p className="text-sm text-foreground/60">
                  This action cannot be undone
                </p>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="text-foreground/60 hover:text-foreground transition-colors"
              disabled={isSubmitting}
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* Content */}
          <div className="p-6">
            {!showConfirmation ? (
              // First confirmation step
              <div>
                <div className="mb-4">
                  <p className="text-foreground/80 mb-3">
                    {message}
                  </p>
                  
                  {itemName && (
                    <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-md border border-stroke">
                      <p className="text-sm font-medium text-foreground">
                        {type === "multiple" ? `${itemCount} items selected` : itemName}
                      </p>
                    </div>
                  )}
                </div>

                {/* Warning for multiple operations */}
                {type === "multiple" && (
                  <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                    <div className="flex items-center space-x-2">
                      <AlertTriangle className="h-4 w-4 text-red-500" />
                      <p className="text-sm text-red-700 dark:text-red-300">
                        This will delete multiple items permanently!
                      </p>
                    </div>
                  </div>
                )}

                {/* Actions */}
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={handleClose}
                    className="px-4 py-2 text-foreground/70 hover:text-foreground transition-colors"
                    disabled={isSubmitting}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleFirstConfirm}
                    className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
                    disabled={isSubmitting}
                  >
                    Continue
                  </button>
                </div>
              </div>
            ) : (
              // Password confirmation step
              <form onSubmit={handleFinalConfirm}>
                <div className="mb-6">
                  <p className="text-sm text-foreground/80 mb-4">
                    Please enter your password to confirm this deletion:
                  </p>

                  <div className="relative">
                    <input
                      type={showPassword ? "text" : "password"}
                      placeholder="Enter your password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="w-full px-3 py-2 pr-10 border border-red-300 dark:border-red-500 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 bg-widget text-foreground"
                      required
                      disabled={isSubmitting}
                      autoFocus
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-foreground/60 hover:text-foreground"
                      disabled={isSubmitting}
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setShowConfirmation(false)}
                    className="px-4 py-2 text-foreground/70 hover:text-foreground transition-colors"
                    disabled={isSubmitting}
                  >
                    Back
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting || !password.trim()}
                    className="flex items-center space-x-2 px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSubmitting && <CircularLoader size={16} color="white" />}
                    <Trash2 className="h-4 w-4" />
                    <span>{getButtonText()}</span>
                  </button>
                </div>
              </form>
            )}
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
}
