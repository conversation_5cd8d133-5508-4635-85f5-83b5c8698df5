"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/teacher-assignment/page",{

/***/ "(app-pages-browser)/./src/app/(dashboards)/school-admin/teacher-assignment/page.tsx":
/*!***********************************************************************!*\
  !*** ./src/app/(dashboards)/school-admin/teacher-assignment/page.tsx ***!
  \***********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TeacherAssignmentPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Filter,Plus,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Filter,Plus,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Filter,Plus,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Filter,Plus,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Filter,Plus,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Dashboard/Layouts/SchoolLayout */ \"(app-pages-browser)/./src/components/Dashboard/Layouts/SchoolLayout.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_utils_TableFix__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/utils/TableFix */ \"(app-pages-browser)/./src/components/utils/TableFix.tsx\");\n/* harmony import */ var _components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/widgets/CircularLoader */ \"(app-pages-browser)/./src/components/widgets/CircularLoader.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var _components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/utils/ProtectedRoute */ \"(app-pages-browser)/./src/components/utils/ProtectedRoute.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/services/TimetableServices */ \"(app-pages-browser)/./src/app/services/TimetableServices.tsx\");\n/* harmony import */ var _app_services_ClassServices__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/services/ClassServices */ \"(app-pages-browser)/./src/app/services/ClassServices.tsx\");\n/* harmony import */ var _app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/services/SubjectServices */ \"(app-pages-browser)/./src/app/services/SubjectServices.tsx\");\n/* harmony import */ var _app_services_TeacherServices__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/services/TeacherServices */ \"(app-pages-browser)/./src/app/services/TeacherServices.tsx\");\n/* harmony import */ var _app_services_PeriodServices__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/services/PeriodServices */ \"(app-pages-browser)/./src/app/services/PeriodServices.tsx\");\n/* harmony import */ var _components_modals_TeacherAssignmentModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/modals/TeacherAssignmentModal */ \"(app-pages-browser)/./src/components/modals/TeacherAssignmentModal.tsx\");\n/* harmony import */ var _components_modals_DeleteConfirmationModal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/modals/DeleteConfirmationModal */ \"(app-pages-browser)/./src/components/modals/DeleteConfirmationModal.tsx\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst navigation = {\n    icon: _barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n    baseHref: \"/school-admin/teacher-assignment\",\n    title: \"Teacher Assignment\"\n};\nconst DAYS = [\n    'Monday',\n    'Tuesday',\n    'Wednesday',\n    'Thursday',\n    'Friday'\n];\nfunction TeacherAssignmentPage() {\n    var _user_school_ids;\n    _s();\n    const { logout, user } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const { toasts, removeToast, showSuccess, showError } = (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_14__.useToast)();\n    // State management\n    const [assignments, setAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loadingData, setLoadingData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [selectedClass, setSelectedClass] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedTeacher, setSelectedTeacher] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedDay, setSelectedDay] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    // Modal states\n    const [isAssignmentModalOpen, setIsAssignmentModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isDeleteModalOpen, setIsDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [assignmentToEdit, setAssignmentToEdit] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [assignmentToDelete, setAssignmentToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [deleteType, setDeleteType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"single\");\n    const [selectedAssignments, setSelectedAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Additional data for forms\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [teachers, setTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [periods, setPeriods] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Loading states\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [clearSelection, setClearSelection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Get school ID from user\n    const schoolId = (user === null || user === void 0 ? void 0 : (_user_school_ids = user.school_ids) === null || _user_school_ids === void 0 ? void 0 : _user_school_ids[0]) || (user === null || user === void 0 ? void 0 : user.school_id);\n    // Fetch assignment data from API\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"TeacherAssignmentPage.useEffect\": ()=>{\n            const fetchAssignmentData = {\n                \"TeacherAssignmentPage.useEffect.fetchAssignmentData\": async ()=>{\n                    if (!schoolId) {\n                        showError(\"Error\", \"No school ID found\");\n                        setLoadingData(false);\n                        return;\n                    }\n                    try {\n                        setLoadingData(true);\n                        // Build filters\n                        const filters = {};\n                        if (selectedClass !== 'all') filters.class_id = selectedClass;\n                        if (selectedTeacher !== 'all') filters.teacher_id = selectedTeacher;\n                        if (selectedDay !== 'all') filters.day_of_week = selectedDay;\n                        // Fetch assignments (timetable entries)\n                        const response = await (0,_app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_7__.getTimetable)(schoolId, filters);\n                        setAssignments(response.schedule_records);\n                    } catch (error) {\n                        console.error(\"Error fetching assignment data:\", error);\n                        showError(\"Error\", \"Failed to load assignment data\");\n                    } finally{\n                        setLoadingData(false);\n                    }\n                }\n            }[\"TeacherAssignmentPage.useEffect.fetchAssignmentData\"];\n            fetchAssignmentData();\n        }\n    }[\"TeacherAssignmentPage.useEffect\"], [\n        schoolId,\n        selectedClass,\n        selectedTeacher,\n        selectedDay\n    ]);\n    // Fetch additional data for modals\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"TeacherAssignmentPage.useEffect\": ()=>{\n            const fetchAdditionalData = {\n                \"TeacherAssignmentPage.useEffect.fetchAdditionalData\": async ()=>{\n                    if (!schoolId) return;\n                    try {\n                        // Fetch data with individual error handling\n                        const results = await Promise.allSettled([\n                            (0,_app_services_ClassServices__WEBPACK_IMPORTED_MODULE_8__.getClassesBySchool)(schoolId),\n                            (0,_app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_9__.getSubjectsBySchoolId)(schoolId),\n                            (0,_app_services_TeacherServices__WEBPACK_IMPORTED_MODULE_10__.getTeachersBySchool)(schoolId),\n                            (0,_app_services_PeriodServices__WEBPACK_IMPORTED_MODULE_11__.getPeriodsBySchool)(schoolId)\n                        ]);\n                        // Handle each result individually\n                        if (results[0].status === 'fulfilled') {\n                            setClasses(results[0].value);\n                        } else {\n                            console.error(\"Failed to fetch classes:\", results[0].reason);\n                            setClasses([]);\n                        }\n                        if (results[1].status === 'fulfilled') {\n                            setSubjects(results[1].value);\n                        } else {\n                            console.error(\"Failed to fetch subjects:\", results[1].reason);\n                            setSubjects([]);\n                        }\n                        if (results[2].status === 'fulfilled') {\n                            setTeachers(results[2].value);\n                        } else {\n                            console.error(\"Failed to fetch teachers:\", results[2].reason);\n                            setTeachers([]);\n                        }\n                        if (results[3].status === 'fulfilled') {\n                            setPeriods(results[3].value);\n                        } else {\n                            console.error(\"Failed to fetch periods:\", results[3].reason);\n                            setPeriods([]);\n                        }\n                        // Show warning if any critical data failed to load\n                        const anyDataFailed = results.some({\n                            \"TeacherAssignmentPage.useEffect.fetchAdditionalData.anyDataFailed\": (result)=>result.status === 'rejected'\n                        }[\"TeacherAssignmentPage.useEffect.fetchAdditionalData.anyDataFailed\"]);\n                        if (anyDataFailed) {\n                            showError(\"Warning\", \"Some form data could not be loaded. Some features may be limited.\");\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching additional data:\", error);\n                        showError(\"Error\", \"Failed to load form data\");\n                    }\n                }\n            }[\"TeacherAssignmentPage.useEffect.fetchAdditionalData\"];\n            fetchAdditionalData();\n        }\n    }[\"TeacherAssignmentPage.useEffect\"], [\n        schoolId\n    ]);\n    // CRUD Functions\n    const handleCreateAssignment = ()=>{\n        setAssignmentToEdit(null);\n        setIsAssignmentModalOpen(true);\n    };\n    const handleEditAssignment = (assignment)=>{\n        setAssignmentToEdit(assignment);\n        setIsAssignmentModalOpen(true);\n    };\n    const handleDeleteAssignment = (assignment)=>{\n        setAssignmentToDelete(assignment);\n        setDeleteType(\"single\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleDeleteMultiple = ()=>{\n        setDeleteType(\"multiple\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleSelectionChange = (selectedRows)=>{\n        setSelectedAssignments(selectedRows);\n    };\n    // Modal submission function\n    const handleAssignmentSubmit = async (data)=>{\n        if (!schoolId) {\n            showError(\"Error\", \"No school ID found\");\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            // Create new assignment\n            await (0,_app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_7__.createScheduleEntry)(schoolId, data);\n            // Refresh assignments list\n            const filters = {};\n            if (selectedClass !== 'all') filters.class_id = selectedClass;\n            if (selectedTeacher !== 'all') filters.teacher_id = selectedTeacher;\n            if (selectedDay !== 'all') filters.day_of_week = selectedDay;\n            const response = await (0,_app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_7__.getTimetable)(schoolId, filters);\n            setAssignments(response.schedule_records);\n            setIsAssignmentModalOpen(false);\n            setAssignmentToEdit(null);\n            // Show success notification\n            showSuccess(\"Assignment Created\", \"Teacher assignment has been created successfully.\");\n        } catch (error) {\n            console.error(\"Error submitting assignment:\", error);\n            showError(\"Error\", \"Failed to save assignment. Please try again.\");\n            throw error;\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const getDayColor = (day)=>{\n        const colors = {\n            Monday: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',\n            Tuesday: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',\n            Wednesday: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300',\n            Thursday: 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',\n            Friday: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300',\n            Saturday: 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300',\n            Sunday: 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'\n        };\n        return colors[day] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';\n    };\n    // Table columns\n    const columns = [\n        {\n            header: \"Teacher\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-medium text-foreground\",\n                        children: row.teacher_name\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Class\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-medium\",\n                    children: row.class_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Subject\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.subject_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Day\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getDayColor(row.day_of_week)),\n                    children: row.day_of_week\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Period\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-semibold\",\n                            children: [\n                                \"Period \",\n                                row.period_number\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-foreground/60\",\n                            children: [\n                                row.start_time.slice(0, 5),\n                                \" - \",\n                                row.end_time.slice(0, 5)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                    lineNumber: 260,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Type\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.schedule_type\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    // Actions for the table\n    const actions = [\n        {\n            label: \"Edit\",\n            onClick: (assignment)=>{\n                handleEditAssignment(assignment);\n            }\n        },\n        {\n            label: \"Delete\",\n            onClick: (assignment)=>{\n                handleDeleteAssignment(assignment);\n            }\n        }\n    ];\n    // Filter data based on selections\n    const filteredAssignments = assignments.filter((assignment)=>{\n        if (selectedClass !== 'all') {\n            var _classes_find;\n            // Find class name from classes array\n            const selectedClassName = (_classes_find = classes.find((c)=>c._id === selectedClass)) === null || _classes_find === void 0 ? void 0 : _classes_find.name;\n            if (assignment.class_name !== selectedClassName) return false;\n        }\n        if (selectedTeacher !== 'all') {\n            // Find teacher name from teachers array\n            const selectedTeacherObj = teachers.find((t)=>t._id === selectedTeacher);\n            const selectedTeacherName = selectedTeacherObj ? \"\".concat(selectedTeacherObj.first_name, \" \").concat(selectedTeacherObj.last_name) : '';\n            if (assignment.teacher_name !== selectedTeacherName) return false;\n        }\n        if (selectedDay !== 'all' && assignment.day_of_week !== selectedDay) return false;\n        return true;\n    });\n    if (loadingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: 32,\n                color: \"teal\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                lineNumber: 313,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n            lineNumber: 312,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        allowedRoles: [\n            \"school_admin\",\n            \"admin\",\n            \"super\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            navigation: navigation,\n            onLogout: logout,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-teal rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: \"Teacher Assignment\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-foreground/60\",\n                                                        children: \"Assign teachers to classes for specific periods and subjects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-foreground\",\n                                                children: assignments.length\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-foreground/60\",\n                                                children: \"Total Assignments\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Total Teachers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: teachers.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Total Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: classes.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Active Assignments\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: assignments.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Subjects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: subjects.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4 text-foreground/60\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-foreground\",\n                                                children: \"Filters:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Class:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedClass,\n                                                onChange: (e)=>setSelectedClass(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    classes.map((classItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: classItem._id,\n                                                            children: classItem.name\n                                                        }, classItem._id, false, {\n                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Teacher:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedTeacher,\n                                                onChange: (e)=>setSelectedTeacher(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Teachers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    teachers.map((teacher)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: teacher._id,\n                                                            children: teacher.first_name && teacher.last_name ? \"\".concat(teacher.first_name, \" \").concat(teacher.last_name) : teacher.name || 'Unknown Teacher'\n                                                        }, teacher._id, false, {\n                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                            lineNumber: 428,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Day:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedDay,\n                                                onChange: (e)=>setSelectedDay(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Days\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    DAYS.map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: day,\n                                                            children: day\n                                                        }, day, false, {\n                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-foreground\",\n                                            children: [\n                                                \"Teacher Assignments (\",\n                                                filteredAssignments.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_19__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            onClick: handleCreateAssignment,\n                                            className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"New Assignment\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                    lineNumber: 469,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        size: 24,\n                                        color: \"teal\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 33\n                                    }, void 0),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_TableFix__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        data: filteredAssignments,\n                                        columns: columns,\n                                        actions: actions,\n                                        defaultItemsPerPage: 15,\n                                        onSelectionChange: handleSelectionChange,\n                                        handleDeleteMultiple: handleDeleteMultiple,\n                                        clearSelection: clearSelection,\n                                        onSelectionCleared: ()=>setClearSelection(false)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                            lineNumber: 456,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_TeacherAssignmentModal__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    isOpen: isAssignmentModalOpen,\n                    onClose: ()=>{\n                        setIsAssignmentModalOpen(false);\n                        setAssignmentToEdit(null);\n                    },\n                    onSubmit: handleAssignmentSubmit,\n                    assignment: assignmentToEdit,\n                    classes: classes,\n                    subjects: subjects,\n                    teachers: teachers,\n                    periods: periods,\n                    loading: isSubmitting\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                    lineNumber: 489,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_DeleteConfirmationModal__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    isOpen: isDeleteModalOpen,\n                    onClose: ()=>{\n                        setIsDeleteModalOpen(false);\n                        setAssignmentToDelete(null);\n                    },\n                    onConfirm: ()=>{},\n                    title: deleteType === \"single\" ? \"Delete Teacher Assignment\" : \"Delete Selected Assignments\",\n                    message: deleteType === \"single\" ? \"Are you sure you want to delete this teacher assignment? This action cannot be undone.\" : \"Are you sure you want to delete \".concat(selectedAssignments.length, \" selected assignments? This action cannot be undone.\"),\n                    itemName: deleteType === \"single\" && assignmentToDelete ? \"\".concat(assignmentToDelete.teacher_name, \" - \").concat(assignmentToDelete.class_name, \" (\").concat(assignmentToDelete.subject_name, \")\") : undefined,\n                    itemCount: deleteType === \"multiple\" ? selectedAssignments.length : undefined,\n                    type: deleteType\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                    lineNumber: 505,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Toast__WEBPACK_IMPORTED_MODULE_14__.ToastContainer, {\n                    toasts: toasts,\n                    onClose: removeToast\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                    lineNumber: 532,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n            lineNumber: 320,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n        lineNumber: 319,\n        columnNumber: 5\n    }, this);\n}\n_s(TeacherAssignmentPage, \"/n1j+j++YyVrhUTkcwfwhdPaJT8=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        _components_ui_Toast__WEBPACK_IMPORTED_MODULE_14__.useToast\n    ];\n});\n_c = TeacherAssignmentPage;\nvar _c;\n$RefreshReg$(_c, \"TeacherAssignmentPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboards)/school-admin/teacher-assignment/page.tsx\n"));

/***/ })

});