// Test script for delete all schools API
// Run with: node test_delete_all_schools.js

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:5000/api';

// Test function
async function testDeleteAllSchools() {
    console.log('🧪 Testing Delete All Schools API...\n');

    try {
        // First, let's check how many schools exist
        console.log('1. Checking current schools count...');
        const getResponse = await fetch(`${BASE_URL}/school/get-schools`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                // Note: You'll need to add a valid JWT token here for real testing
                'Authorization': 'Bearer YOUR_JWT_TOKEN_HERE'
            }
        });

        if (getResponse.ok) {
            const schools = await getResponse.json();
            console.log(`   Found ${schools.length} schools in database`);
        } else {
            console.log('   Could not fetch schools (authentication required)');
        }

        // Test the delete all endpoint
        console.log('\n2. Testing delete all schools endpoint...');
        const deleteResponse = await fetch(`${BASE_URL}/school/delete-all-schools`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                // Note: You'll need to add a valid JWT token here for real testing
                'Authorization': 'Bearer YOUR_JWT_TOKEN_HERE'
            }
        });

        const result = await deleteResponse.json();
        
        if (deleteResponse.ok) {
            console.log('   ✅ Success!');
            console.log(`   Message: ${result.message}`);
            console.log(`   Deleted Count: ${result.deletedCount}`);
        } else {
            console.log('   ❌ Error!');
            console.log(`   Status: ${deleteResponse.status}`);
            console.log(`   Message: ${result.message}`);
        }

    } catch (error) {
        console.error('❌ Test failed with error:', error.message);
    }
}

// Instructions for manual testing
console.log('📋 Manual Testing Instructions:');
console.log('1. Start your backend server: npm start');
console.log('2. Get a valid JWT token from login');
console.log('3. Replace "YOUR_JWT_TOKEN_HERE" with the actual token');
console.log('4. Run this script: node test_delete_all_schools.js');
console.log('');

// Uncomment the line below to run the test
// testDeleteAllSchools();

console.log('⚠️  WARNING: This will delete ALL schools in your database!');
console.log('⚠️  Only run this test on a development/test database!');
console.log('⚠️  Uncomment the testDeleteAllSchools() call to run the test.');
