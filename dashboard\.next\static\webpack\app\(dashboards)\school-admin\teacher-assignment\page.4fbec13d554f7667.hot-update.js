"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/teacher-assignment/page",{

/***/ "(app-pages-browser)/./src/components/widgets/SchoolPoints.tsx":
/*!*************************************************!*\
  !*** ./src/components/widgets/SchoolPoints.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SchoolPoints)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Coins,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Coins,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.js\");\n/* harmony import */ var _barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Coins,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_SchoolServices__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/services/SchoolServices */ \"(app-pages-browser)/./src/app/services/SchoolServices.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction SchoolPoints(param) {\n    let { className = \"\" } = param;\n    var _user_school_ids;\n    _s();\n    const { user } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    const [credits, setCredits] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Get school ID from user\n    const schoolId = (user === null || user === void 0 ? void 0 : (_user_school_ids = user.school_ids) === null || _user_school_ids === void 0 ? void 0 : _user_school_ids[0]) || (user === null || user === void 0 ? void 0 : user.school_id);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SchoolPoints.useEffect\": ()=>{\n            const fetchCredits = {\n                \"SchoolPoints.useEffect.fetchCredits\": async ()=>{\n                    if (!schoolId) {\n                        setLoading(false);\n                        return;\n                    }\n                    try {\n                        setLoading(true);\n                        const schoolCredits = await (0,_app_services_SchoolServices__WEBPACK_IMPORTED_MODULE_2__.getSchoolCredits)(schoolId);\n                        setCredits(schoolCredits);\n                        setError(null);\n                    } catch (err) {\n                        console.error(\"Error fetching school credits:\", err);\n                        setError(\"Failed to load credits\");\n                        setCredits(0);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"SchoolPoints.useEffect.fetchCredits\"];\n            fetchCredits();\n        }\n    }[\"SchoolPoints.useEffect\"], [\n        schoolId\n    ]);\n    // Format number with commas\n    const formatNumber = (num)=>{\n        return new Intl.NumberFormat('en-US').format(num);\n    };\n    // Get color based on credit amount\n    const getCreditColor = ()=>{\n        if (credits >= 1000) return \"text-green-600 dark:text-green-400\";\n        if (credits >= 500) return \"text-blue-600 dark:text-blue-400\";\n        if (credits >= 100) return \"text-yellow-600 dark:text-yellow-400\";\n        return \"text-red-600 dark:text-red-400\";\n    };\n    // Get icon based on credit amount\n    const getCreditIcon = ()=>{\n        if (credits >= 500) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n            lineNumber: 61,\n            columnNumber: 32\n        }, this);\n        if (credits >= 100) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n            lineNumber: 62,\n            columnNumber: 32\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n            lineNumber: 63,\n            columnNumber: 12\n        }, this);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-2 \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden sm:block\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-2 text-red-500 \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-5 w-5\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"hidden sm:block text-sm\",\n                    children: \"Error\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n        initial: {\n            opacity: 0,\n            scale: 0.95\n        },\n        animate: {\n            opacity: 1,\n            scale: 1\n        },\n        className: \"flex items-center space-x-2 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sm:hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    whileHover: {\n                        scale: 1.1\n                    },\n                    whileTap: {\n                        scale: 0.95\n                    },\n                    className: \"relative p-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg shadow-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-5 w-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n                            children: credits > 999 ? '999+' : credits\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden sm:flex items-center space-x-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    whileHover: {\n                        scale: 1.05\n                    },\n                    className: \"flex items-center space-x-2 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 px-3 py-2 rounded-lg border border-yellow-200 dark:border-yellow-800 shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-1.5 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                getCreditIcon()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-bold text-lg \".concat(getCreditColor()),\n                                            children: formatNumber(credits)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-foreground/60 font-medium\",\n                                            children: \"pts\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-foreground/50\",\n                                    children: \"School Credits\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden xs:flex sm:hidden items-center space-x-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    whileHover: {\n                        scale: 1.05\n                    },\n                    className: \"flex items-center space-x-2 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 px-2 py-1.5 rounded-lg border border-yellow-200 dark:border-yellow-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-1 bg-gradient-to-r from-yellow-400 to-orange-500 rounded\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-3 w-3 text-white\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-semibold text-sm \".concat(getCreditColor()),\n                            children: formatNumber(credits)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, this);\n}\n_s(SchoolPoints, \"tdA9imYFgZlUC09PQ1mXR0gK1TI=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    ];\n});\n_c = SchoolPoints;\nvar _c;\n$RefreshReg$(_c, \"SchoolPoints\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/widgets/SchoolPoints.tsx\n"));

/***/ })

});