"use client";

import { FileCheck2, Calendar, Users, TrendingUp, Filter, Download, Plus } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import { Suspense, useEffect, useState } from "react";
import DataTableFix from "@/components/utils/TableFix";

import CircularLoader from "@/components/widgets/CircularLoader";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import { motion } from "framer-motion";
import {
  getAttendanceRecords,
  getAttendanceStats,
  createAttendance,
  updateAttendance,
  deleteAttendance,
  deleteMultipleAttendances,
  AttendanceRecord,
  AttendanceStats
} from "@/app/services/AttendanceServices";
import { getStudentsBySchool } from "@/app/services/StudentServices";
import { getClassesBySchool } from "@/app/services/ClassServices";
import { getAllSubjects } from "@/app/services/SubjectServices";
import { getClassSchedulesBySchool } from "@/app/services/ClassScheduleServices";
import AttendanceModal from "@/components/modals/AttendanceModal";
import DeleteConfirmationModal from "@/components/modals/DeleteConfirmationModal";
import { useToast, ToastContainer } from "@/components/ui/Toast";



const navigation = {
  icon: FileCheck2,
  baseHref: "/school-admin/attendance",
  title: "Attendance"
};

export default function AttendancePage() {
  const { logout, user } = useAuth();
  const { toasts, removeToast, showSuccess, showError } = useToast();

  // State management
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);
  const [stats, setStats] = useState<AttendanceStats>({
    totalStudents: 0,
    presentToday: 0,
    absentToday: 0,
    lateToday: 0,
    excusedToday: 0,
    attendanceRate: 0
  });
  const [loadingData, setLoadingData] = useState(true);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedClass, setSelectedClass] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');

  // Modal states
  const [isAttendanceModalOpen, setIsAttendanceModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [attendanceToEdit, setAttendanceToEdit] = useState<AttendanceRecord | null>(null);
  const [attendanceToDelete, setAttendanceToDelete] = useState<AttendanceRecord | null>(null);
  const [deleteType, setDeleteType] = useState<"single" | "multiple">("single");
  const [selectedAttendances, setSelectedAttendances] = useState<AttendanceRecord[]>([]);

  // Additional data for forms
  const [students, setStudents] = useState<any[]>([]);
  const [classes, setClasses] = useState<any[]>([]);
  const [subjects, setSubjects] = useState<any[]>([]);
  const [schedules, setSchedules] = useState<any[]>([]);

  // Loading states
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [clearSelection, setClearSelection] = useState(false);

  // Get school ID from user
  const schoolId = user?.school_ids?.[0] || user?.school_id;

  // Fetch attendance data from API
  useEffect(() => {
    const fetchAttendanceData = async () => {
      if (!schoolId) {
        showError("Error", "No school ID found");
        setLoadingData(false);
        return;
      }

      try {
        setLoadingData(true);

        // Build filters
        const filters: any = {};
        if (selectedDate) filters.date = selectedDate;
        if (selectedClass !== 'all') filters.class_id = selectedClass;
        if (selectedStatus !== 'all') filters.status = selectedStatus;

        // Fetch records and stats in parallel
        const [recordsResponse, statsResponse] = await Promise.all([
          getAttendanceRecords(schoolId as string, filters),
          getAttendanceStats(schoolId as string, selectedDate)
        ]);

        setAttendanceRecords(recordsResponse.attendance_records);
        setStats(statsResponse.stats);
      } catch (error) {
        console.error("Error fetching attendance data:", error);
        showError("Error", "Failed to load attendance data");
      } finally {
        setLoadingData(false);
      }
    };

    fetchAttendanceData();
  }, [schoolId, selectedDate, selectedClass, selectedStatus]);

  // Fetch additional data for modals
  useEffect(() => {
    const fetchAdditionalData = async () => {
      if (!schoolId) return;

      try {
        // Fetch data with individual error handling
        const results = await Promise.allSettled([
          getStudentsBySchool(schoolId as string),
          getClassesBySchool(schoolId as string),
          getAllSubjects(),
          getClassSchedulesBySchool(schoolId as string)
        ]);

        // Handle each result individually
        if (results[0].status === 'fulfilled') {
          setStudents(results[0].value);
        } else {
          console.error("Failed to fetch students:", results[0].reason);
          setStudents([]);
        }

        if (results[1].status === 'fulfilled') {
          setClasses(results[1].value);
        } else {
          console.error("Failed to fetch classes:", results[1].reason);
          setClasses([]);
        }

        if (results[2].status === 'fulfilled') {
          setSubjects(results[2].value);
        } else {
          console.error("Failed to fetch subjects:", results[2].reason);
          setSubjects([]);
        }

        if (results[3].status === 'fulfilled') {
          setSchedules(results[3].value);
        } else {
          console.error("Failed to fetch schedules:", results[3].reason);
          setSchedules([]);
          // Don't show error for schedules as it's not critical
        }

        // Only show error if critical data failed to load
        const criticalDataFailed = results[0].status === 'rejected' ||
                                  results[1].status === 'rejected' ||
                                  results[2].status === 'rejected';

        if (criticalDataFailed) {
          showError("Warning", "Some form data could not be loaded. Some features may be limited.");
        }
      } catch (error) {
        console.error("Error fetching additional data:", error);
        showError("Error", "Failed to load form data");
      }
    };

    fetchAdditionalData();
  }, [schoolId]); // Removed showError from dependencies

  // CRUD Functions
  const handleCreateAttendance = () => {
    setAttendanceToEdit(null);
    setIsAttendanceModalOpen(true);
  };

  const handleEditAttendance = (attendance: AttendanceRecord) => {
    setAttendanceToEdit(attendance);
    setIsAttendanceModalOpen(true);
  };

  const handleDeleteAttendance = (attendance: AttendanceRecord) => {
    setAttendanceToDelete(attendance);
    setDeleteType("single");
    setIsDeleteModalOpen(true);
  };

  const handleDeleteMultiple = () => {
    setDeleteType("multiple");
    setIsDeleteModalOpen(true);
  };

  const handleSelectionChange = (selectedRows: AttendanceRecord[]) => {
    setSelectedAttendances(selectedRows);
  };

  // Modal submission functions
  const handleAttendanceSubmit = async (data: any) => {
    if (!schoolId) {
      showError("Error", "No school ID found");
      return;
    }

    setIsSubmitting(true);
    try {
      if (attendanceToEdit) {
        // Update existing attendance
        await updateAttendance(attendanceToEdit._id, data);
      } else {
        // Create new attendance
        await createAttendance({ ...data, school_id: schoolId });
      }

      // Refresh attendance list
      const filters: any = {};
      if (selectedDate) filters.date = selectedDate;
      if (selectedClass !== 'all') filters.class_id = selectedClass;
      if (selectedStatus !== 'all') filters.status = selectedStatus;

      const [recordsResponse, statsResponse] = await Promise.all([
        getAttendanceRecords(schoolId as string, filters),
        getAttendanceStats(schoolId as string, selectedDate)
      ]);

      setAttendanceRecords(recordsResponse.attendance_records);
      setStats(statsResponse.stats);
      setIsAttendanceModalOpen(false);
      setAttendanceToEdit(null);

      // Show success notification
      showSuccess(
        attendanceToEdit ? "Attendance Updated" : "Attendance Marked",
        attendanceToEdit ? "Attendance record has been updated successfully." : "Attendance has been marked successfully."
      );
    } catch (error) {
      console.error("Error submitting attendance:", error);
      showError("Error", "Failed to save attendance. Please try again.");
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteConfirm = async () => {
    if (!schoolId) return;

    try {
      if (deleteType === "single" && attendanceToDelete) {
        await deleteAttendance(attendanceToDelete._id);
      } else if (deleteType === "multiple") {
        const selectedIds = selectedAttendances.map(a => a._id);
        await deleteMultipleAttendances(selectedIds);
        setClearSelection(true);
      }

      // Refresh attendance list
      const filters: any = {};
      if (selectedDate) filters.date = selectedDate;
      if (selectedClass !== 'all') filters.class_id = selectedClass;
      if (selectedStatus !== 'all') filters.status = selectedStatus;

      const [recordsResponse, statsResponse] = await Promise.all([
        getAttendanceRecords(schoolId as string, filters),
        getAttendanceStats(schoolId as string, selectedDate)
      ]);

      setAttendanceRecords(recordsResponse.attendance_records);
      setStats(statsResponse.stats);
      setIsDeleteModalOpen(false);
      setAttendanceToDelete(null);
      setSelectedAttendances([]);

      // Show success notification
      if (deleteType === "single") {
        showSuccess("Attendance Deleted", "Attendance record has been deleted successfully.");
      } else {
        showSuccess("Attendances Deleted", `${selectedAttendances.length} attendance records have been deleted successfully.`);
      }
    } catch (error) {
      console.error("Error deleting attendance(s):", error);
      showError("Error", "Failed to delete attendance record(s). Please try again.");
      throw error;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Present':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'Absent':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
      case 'Late':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';
      case 'Excused':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  // Table columns
  const columns = [
    { 
      header: "Student", 
      accessor: (row: AttendanceRecord) => (
        <div>
          <p className="font-medium text-foreground">{row.student_name}</p>
          <p className="text-sm text-foreground/60">{row.student_id}</p>
        </div>
      )
    },
    { 
      header: "Class", 
      accessor: (row: AttendanceRecord) => (
        <span className="text-sm font-medium">{row.class_name}</span>
      )
    },
    { 
      header: "Subject", 
      accessor: (row: AttendanceRecord) => (
        <span className="text-sm">{row.subject_name}</span>
      )
    },
    { 
      header: "Period", 
      accessor: (row: AttendanceRecord) => (
        <span className="text-sm">Period {row.period_number}</span>
      )
    },
    { 
      header: "Teacher", 
      accessor: (row: AttendanceRecord) => (
        <span className="text-sm">{row.teacher_name}</span>
      )
    },
    { 
      header: "Status", 
      accessor: (row: AttendanceRecord) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(row.status)}`}>
          {row.status}
        </span>
      )
    },
    { 
      header: "Date", 
      accessor: (row: AttendanceRecord) => (
        <span className="text-sm">{new Date(row.date).toLocaleDateString()}</span>
      )
    }
  ];

  // Actions for the table
  const actions = [
    {
      label: "Edit",
      onClick: (attendance: AttendanceRecord) => {
        handleEditAttendance(attendance);
      },
    },
    {
      label: "Delete",
      onClick: (attendance: AttendanceRecord) => {
        handleDeleteAttendance(attendance);
      },
    },
  ];
  console.log("attendanceRecords", attendanceRecords);
  // Filter data based on selections
  const filteredRecords = attendanceRecords.filter(record => {
    if (selectedClass !== 'all' && record.class_name !== selectedClass) return false;
    if (selectedStatus !== 'all' && record.status !== selectedStatus) return false;
    return true;
  });

  if (loadingData) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <CircularLoader size={32} color="teal" />
      </div>
    );
  }

  return (
    <ProtectedRoute allowedRoles={["school_admin", "admin", "super"]}>
      <SchoolLayout navigation={navigation} onLogout={logout}>
        <div className="space-y-6">
          {/* Header */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-teal rounded-full flex items-center justify-center">
                  <FileCheck2 className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-foreground">Attendance Management</h1>
                  <p className="text-foreground/60">
                    Monitor and track student attendance across all classes
                  </p>
                </div>
              </div>
              
              <div className="text-right">
                <p className="text-2xl font-bold text-foreground">{stats.attendanceRate}%</p>
                <p className="text-sm text-foreground/60">Today's Rate</p>
              </div>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Total Students</p>
                  <p className="text-2xl font-bold text-foreground">{stats.totalStudents}</p>
                </div>
                <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                  <Users className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Present Today</p>
                  <p className="text-2xl font-bold text-green-600">{stats.presentToday}</p>
                </div>
                <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                  <TrendingUp className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Absent Today</p>
                  <p className="text-2xl font-bold text-red-600">{stats.absentToday}</p>
                </div>
                <div className="w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center">
                  <Users className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Late Today</p>
                  <p className="text-2xl font-bold text-yellow-600">{stats.lateToday}</p>
                </div>
                <div className="w-10 h-10 bg-yellow-500 rounded-lg flex items-center justify-center">
                  <Calendar className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex flex-wrap items-center gap-4">
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-foreground/60" />
                <span className="text-sm font-medium text-foreground">Filters:</span>
              </div>
              
              <div className="flex items-center space-x-2">
                <label className="text-sm text-foreground/70">Date:</label>
                <input
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  className="px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground"
                />
              </div>

              <div className="flex items-center space-x-2">
                <label className="text-sm text-foreground/70">Class:</label>
                <select
                  value={selectedClass}
                  onChange={(e) => setSelectedClass(e.target.value)}
                  className="px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground"
                >
                  <option value="all">All Classes</option>
                  {classes.map((classItem) => (
                    <option key={classItem._id} value={classItem._id}>
                      {classItem.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex items-center space-x-2">
                <label className="text-sm text-foreground/70">Status:</label>
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground"
                >
                  <option value="all">All Status</option>
                  <option value="Present">Present</option>
                  <option value="Absent">Absent</option>
                  <option value="Late">Late</option>
                  <option value="Excused">Excused</option>
                </select>
              </div>

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 text-sm"
              >
                <Download size={14} />
                <span>Export</span>
              </motion.button>
            </div>
          </div>

          {/* Attendance Table */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-foreground">
                Attendance Records ({filteredRecords.length})
              </h2>

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleCreateAttendance}
                className="flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600"
              >
                <Plus size={16} />
                <span>Mark Attendance</span>
              </motion.button>
            </div>

            <Suspense fallback={<CircularLoader size={24} color="teal" />}>
              <DataTableFix<AttendanceRecord>
                data={filteredRecords}
                columns={columns}
                actions={actions}
                defaultItemsPerPage={15}
                onSelectionChange={handleSelectionChange}
                handleDeleteMultiple={handleDeleteMultiple}
                clearSelection={clearSelection}
                onSelectionCleared={() => setClearSelection(false)}
              />
            </Suspense>
          </div>
        </div>

        {/* Attendance Modal */}
        <AttendanceModal
          isOpen={isAttendanceModalOpen}
          onClose={() => {
            setIsAttendanceModalOpen(false);
            setAttendanceToEdit(null);
          }}
          onSubmit={handleAttendanceSubmit}
          attendance={attendanceToEdit}
          students={students}
          classes={classes}
          subjects={subjects}
          schedules={schedules}
          loading={isSubmitting}
        />

        {/* Delete Confirmation Modal */}
        <DeleteConfirmationModal
          isOpen={isDeleteModalOpen}
          onClose={() => {
            setIsDeleteModalOpen(false);
            setAttendanceToDelete(null);
          }}
          onConfirm={handleDeleteConfirm}
          title={
            deleteType === "single"
              ? "Delete Attendance Record"
              : "Delete Selected Attendance Records"
          }
          message={
            deleteType === "single"
              ? "Are you sure you want to delete this attendance record? This action cannot be undone."
              : `Are you sure you want to delete ${selectedAttendances.length} selected attendance records? This action cannot be undone.`
          }
          itemName={
            deleteType === "single" && attendanceToDelete
              ? `${attendanceToDelete.student_name} - ${attendanceToDelete.subject_name} (${new Date(attendanceToDelete.date).toLocaleDateString()})`
              : undefined
          }
          itemCount={deleteType === "multiple" ? selectedAttendances.length : undefined}
          type={deleteType}
        />

        {/* Toast Notifications */}
        <ToastContainer toasts={toasts} onClose={removeToast} />
      </SchoolLayout>
    </ProtectedRoute>
  );
}
