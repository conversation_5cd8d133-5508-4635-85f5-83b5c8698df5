import { getTokenFromCookie } from "./UserServices";

const BASE_API_URL = process.env.BASE_API_URL || "https://scolarify.onrender.com/api";

export interface ClassSchedule {
  _id: string;
  class_id: string;
  subject_id: string;
  teacher_id?: string;
  period_id: string;
  day_of_week: string;
  schedule_type: string;
  school_id: string;
  createdAt?: string;
  updatedAt?: string;
}

// Get all class schedules
export async function getClassSchedules(): Promise<ClassSchedule[]> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/schedule/get-class-schedules`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching class schedules:", response.statusText);
      throw new Error("Failed to fetch class schedules");
    }

    const data = await response.json();
    return data as ClassSchedule[];
  } catch (error) {
    console.error("Fetch class schedules error:", error);
    throw new Error("Failed to fetch class schedules");
  }
}

// Get class schedules by school
export async function getClassSchedulesBySchool(schoolId: string): Promise<ClassSchedule[]> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/schedule/get-class-schedules`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching class schedules by school:", response.statusText);
      // Return empty array instead of throwing error
      return [];
    }

    const data = await response.json();
    // Filter by school_id
    return data.filter((schedule: ClassSchedule) => schedule.school_id === schoolId);
  } catch (error) {
    console.error("Fetch class schedules by school error:", error);
    // Return empty array instead of throwing error
    return [];
  }
}

// Fallback function to create basic schedules from classes and periods
export async function createBasicSchedules(classes: any[], periods: any[], subjects: any[]): Promise<ClassSchedule[]> {
  const schedules: ClassSchedule[] = [];
  const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];

  classes.forEach((classItem, classIndex) => {
    days.forEach((day, dayIndex) => {
      periods.forEach((period, periodIndex) => {
        // Create a basic schedule entry
        const subjectIndex = (classIndex + dayIndex + periodIndex) % subjects.length;
        const subject = subjects[subjectIndex];

        if (subject) {
          schedules.push({
            _id: `basic_${classItem._id}_${day}_${period._id}`,
            class_id: classItem._id,
            subject_id: subject._id,
            teacher_id: undefined,
            period_id: period._id,
            day_of_week: day,
            schedule_type: 'Normal',
            school_id: classItem.school_id || '',
          });
        }
      });
    });
  });

  return schedules;
}
