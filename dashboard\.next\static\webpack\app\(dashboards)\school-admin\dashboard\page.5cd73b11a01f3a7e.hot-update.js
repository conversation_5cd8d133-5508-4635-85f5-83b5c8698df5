"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/widgets/SchoolPoints.tsx":
/*!*************************************************!*\
  !*** ./src/components/widgets/SchoolPoints.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SchoolPoints)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Coins,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Coins,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.js\");\n/* harmony import */ var _barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Coins,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_SchoolServices__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/services/SchoolServices */ \"(app-pages-browser)/./src/app/services/SchoolServices.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction SchoolPoints(param) {\n    let { className = \"\" } = param;\n    var _user_school_ids;\n    _s();\n    const { user } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    const [credits, setCredits] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Get school ID from user\n    const schoolId = (user === null || user === void 0 ? void 0 : (_user_school_ids = user.school_ids) === null || _user_school_ids === void 0 ? void 0 : _user_school_ids[0]) || (user === null || user === void 0 ? void 0 : user.school_id);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SchoolPoints.useEffect\": ()=>{\n            const fetchCredits = {\n                \"SchoolPoints.useEffect.fetchCredits\": async ()=>{\n                    if (!schoolId) {\n                        setLoading(false);\n                        return;\n                    }\n                    try {\n                        setLoading(true);\n                        cons;\n                        const schoolCredits = await (0,_app_services_SchoolServices__WEBPACK_IMPORTED_MODULE_2__.getSchoolCredits)(schoolId);\n                        setCredits(schoolCredits);\n                        setError(null);\n                    } catch (err) {\n                        console.error(\"Error fetching school credits:\", err);\n                        setError(\"Failed to load credits\");\n                        setCredits(0);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"SchoolPoints.useEffect.fetchCredits\"];\n            fetchCredits();\n        }\n    }[\"SchoolPoints.useEffect\"], [\n        schoolId\n    ]);\n    // Format number with commas\n    const formatNumber = (num)=>{\n        return new Intl.NumberFormat('en-US').format(num);\n    };\n    // Get color based on credit amount\n    const getCreditColor = ()=>{\n        if (credits >= 1000) return \"text-green-600 dark:text-green-400\";\n        if (credits >= 500) return \"text-blue-600 dark:text-blue-400\";\n        if (credits >= 100) return \"text-yellow-600 dark:text-yellow-400\";\n        return \"text-red-600 dark:text-red-400\";\n    };\n    // Get icon based on credit amount\n    const getCreditIcon = ()=>{\n        if (credits >= 500) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n            lineNumber: 62,\n            columnNumber: 32\n        }, this);\n        if (credits >= 100) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n            lineNumber: 63,\n            columnNumber: 32\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n            lineNumber: 64,\n            columnNumber: 12\n        }, this);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-2 \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden sm:block\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-2 text-red-500 \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-5 w-5\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"hidden sm:block text-sm\",\n                    children: \"Error\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n        initial: {\n            opacity: 0,\n            scale: 0.95\n        },\n        animate: {\n            opacity: 1,\n            scale: 1\n        },\n        className: \"flex items-center space-x-2 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sm:hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    whileHover: {\n                        scale: 1.1\n                    },\n                    whileTap: {\n                        scale: 0.95\n                    },\n                    className: \"relative p-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg shadow-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-5 w-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n                            children: credits > 999 ? '999+' : credits\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden sm:flex items-center space-x-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    whileHover: {\n                        scale: 1.05\n                    },\n                    className: \"flex items-center space-x-2 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 px-3 py-2 rounded-lg border border-yellow-200 dark:border-yellow-800 shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-1.5 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this),\n                                getCreditIcon()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-bold text-lg \".concat(getCreditColor()),\n                                            children: formatNumber(credits)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-foreground/60 font-medium\",\n                                            children: \"pts\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-foreground/50\",\n                                    children: \"School Credits\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden xs:flex sm:hidden items-center space-x-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    whileHover: {\n                        scale: 1.05\n                    },\n                    className: \"flex items-center space-x-2 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 px-2 py-1.5 rounded-lg border border-yellow-200 dark:border-yellow-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-1 bg-gradient-to-r from-yellow-400 to-orange-500 rounded\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-3 w-3 text-white\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-semibold text-sm \".concat(getCreditColor()),\n                            children: formatNumber(credits)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n_s(SchoolPoints, \"tdA9imYFgZlUC09PQ1mXR0gK1TI=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    ];\n});\n_c = SchoolPoints;\nvar _c;\n$RefreshReg$(_c, \"SchoolPoints\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/widgets/SchoolPoints.tsx\n"));

/***/ })

});