import React from 'react';
import DataTableWithBulkActions from '@/components/enhanced/DataTableWithBulkActions';
import { StudentSchema } from '@/app/models/StudentModel';
import { deleteMultipleStudents, deleteAllStudents } from '@/app/services/StudentServices';

interface StudentsTableWithBulkActionsProps {
  students: StudentSchema[];
  setStudents: React.Dispatch<React.SetStateAction<StudentSchema[]>>;
  columns: any[];
  actions: any[];
  loading?: boolean;
  onLoadingChange?: (loading: boolean) => void;
  onSelectionChange?: (selection: StudentSchema[]) => void;
  onSuccess?: (message: string) => void;
  onError?: (message: string) => void;
}

const StudentsTableWithBulkActions: React.FC<StudentsTableWithBulkActionsProps> = ({
  students,
  setStudents,
  columns,
  actions,
  loading = false,
  onLoadingChange,
  onSelectionChange,
  onSuccess,
  onError
}) => {
  return (
    <DataTableWithBulkActions
      columns={columns}
      data={students}
      actions={actions}
      defaultItemsPerPage={5}
      loading={loading}
      onLoadingChange={onLoadingChange}
      onSelectionChange={onSelectionChange}
      idAccessor="_id"
      enableBulkActions={true}
      deleteMultipleService={deleteMultipleStudents}
      deleteAllService={deleteAllStudents}
      itemName="student"
      setItems={setStudents}
      onSuccess={onSuccess}
      onError={onError}
    />
  );
};

export default StudentsTableWithBulkActions;
