"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/period/page",{

/***/ "(app-pages-browser)/./src/app/services/PeriodServices.tsx":
/*!*********************************************!*\
  !*** ./src/app/services/PeriodServices.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createPeriod: () => (/* binding */ createPeriod),\n/* harmony export */   deleteAllPeriods: () => (/* binding */ deleteAllPeriods),\n/* harmony export */   deleteMultiplePeriods: () => (/* binding */ deleteMultiplePeriods),\n/* harmony export */   deletePeriod: () => (/* binding */ deletePeriod),\n/* harmony export */   getPeriods: () => (/* binding */ getPeriods),\n/* harmony export */   getPeriodsBySchool: () => (/* binding */ getPeriodsBySchool),\n/* harmony export */   updatePeriod: () => (/* binding */ updatePeriod)\n/* harmony export */ });\n/* harmony import */ var _UserServices__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./UserServices */ \"(app-pages-browser)/./src/app/services/UserServices.tsx\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\nconst BASE_API_URL = process.env.BASE_API_URL || \"https://scolarify.onrender.com/api\";\n// Get all periods for a school\nasync function getPeriods(schoolId) {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_0__.getTokenFromCookie)(\"idToken\");\n    try {\n        const response = await fetch(\"\".concat(BASE_API_URL, \"/periods/school/\").concat(schoolId), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching periods:\", response.statusText);\n            throw new Error(\"Failed to fetch periods\");\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Fetch periods error:\", error);\n        throw new Error(\"Failed to fetch periods\");\n    }\n}\n// Get periods by school ID (alias for getPeriods)\nasync function getPeriodsBySchool(schoolId) {\n    const response = await getPeriods(schoolId);\n    return response.periods;\n}\n// Create a new period\nasync function createPeriod(schoolId, periodData) {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_0__.getTokenFromCookie)(\"idToken\");\n    try {\n        const response = await fetch(\"\".concat(BASE_API_URL, \"/periods/school/\").concat(schoolId), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            },\n            body: JSON.stringify(periodData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            console.error(\"Error creating period:\", errorData);\n            throw new Error(errorData.message || \"Failed to create period\");\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Create period error:\", error);\n        throw error;\n    }\n}\n// Update a period\nasync function updatePeriod(schoolId, periodId, periodData) {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_0__.getTokenFromCookie)(\"idToken\");\n    try {\n        const response = await fetch(\"\".concat(BASE_API_URL, \"/periods/school/\").concat(schoolId, \"/\").concat(periodId), {\n            method: \"PUT\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            },\n            body: JSON.stringify(periodData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            console.error(\"Error updating period:\", errorData);\n            throw new Error(errorData.message || \"Failed to update period\");\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Update period error:\", error);\n        throw error;\n    }\n}\n// Delete a period\nasync function deletePeriod(schoolId, periodId) {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_0__.getTokenFromCookie)(\"idToken\");\n    try {\n        const response = await fetch(\"\".concat(BASE_API_URL, \"/periods/school/\").concat(schoolId, \"/\").concat(periodId), {\n            method: \"DELETE\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            console.error(\"Error deleting period:\", errorData);\n            throw new Error(errorData.message || \"Failed to delete period\");\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Delete period error:\", error);\n        throw error;\n    }\n}\n// Delete multiple periods (using legacy route)\nasync function deleteMultiplePeriods(periodIds) {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_0__.getTokenFromCookie)(\"idToken\");\n    try {\n        const response = await fetch(\"\".concat(BASE_API_URL, \"/periods/delete-periods\"), {\n            method: \"DELETE\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            },\n            body: JSON.stringify({\n                ids: periodIds\n            })\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            console.error(\"Error deleting multiple periods:\", errorData);\n            throw new Error(errorData.message || \"Failed to delete periods\");\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Delete multiple periods error:\", error);\n        throw error;\n    }\n}\n// Delete all periods (using legacy route)\nasync function deleteAllPeriods() {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_0__.getTokenFromCookie)(\"idToken\");\n    try {\n        const response = await fetch(\"\".concat(BASE_API_URL, \"/periods/delete-all-periods\"), {\n            method: \"DELETE\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            console.error(\"Error deleting all periods:\", errorData);\n            throw new Error(errorData.message || \"Failed to delete all periods\");\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Delete all periods error:\", error);\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/services/PeriodServices.tsx\n"));

/***/ })

});