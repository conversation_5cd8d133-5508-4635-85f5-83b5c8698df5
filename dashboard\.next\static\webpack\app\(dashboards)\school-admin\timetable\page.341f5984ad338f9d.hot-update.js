"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/timetable/page",{

/***/ "(app-pages-browser)/./src/app/(dashboards)/school-admin/timetable/page.tsx":
/*!**************************************************************!*\
  !*** ./src/app/(dashboards)/school-admin/timetable/page.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TimetablePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock4,Edit,Filter,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock-4.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock4,Edit,Filter,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock4,Edit,Filter,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock4,Edit,Filter,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock4,Edit,Filter,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock4,Edit,Filter,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock4,Edit,Filter,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Dashboard/Layouts/SchoolLayout */ \"(app-pages-browser)/./src/components/Dashboard/Layouts/SchoolLayout.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/widgets/CircularLoader */ \"(app-pages-browser)/./src/components/widgets/CircularLoader.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var _components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/utils/ProtectedRoute */ \"(app-pages-browser)/./src/components/utils/ProtectedRoute.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/services/TimetableServices */ \"(app-pages-browser)/./src/app/services/TimetableServices.tsx\");\n/* harmony import */ var _app_services_ClassServices__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/services/ClassServices */ \"(app-pages-browser)/./src/app/services/ClassServices.tsx\");\n/* harmony import */ var _app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/services/SubjectServices */ \"(app-pages-browser)/./src/app/services/SubjectServices.tsx\");\n/* harmony import */ var _app_services_TeacherServices__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/services/TeacherServices */ \"(app-pages-browser)/./src/app/services/TeacherServices.tsx\");\n/* harmony import */ var _components_modals_TimetableModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/modals/TimetableModal */ \"(app-pages-browser)/./src/components/modals/TimetableModal.tsx\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst navigation = {\n    icon: _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    baseHref: \"/school-admin/timetable\",\n    title: \"Time Table\"\n};\nconst DAYS = [\n    'Monday',\n    'Tuesday',\n    'Wednesday',\n    'Thursday',\n    'Friday'\n];\nfunction TimetablePage() {\n    var _user_school_ids, _classes_find;\n    _s();\n    const { logout, user } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    const { toasts, removeToast, showSuccess, showError } = (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_11__.useToast)();\n    // State management\n    const [timetableData, setTimetableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [allClassesTimetable, setAllClassesTimetable] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [periods, setPeriods] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loadingData, setLoadingData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [selectedClass, setSelectedClass] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all_classes');\n    const [selectedWeek, setSelectedWeek] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('current');\n    // Modal states\n    const [isTimetableModalOpen, setIsTimetableModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [scheduleToEdit, setScheduleToEdit] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Additional data for forms\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [teachers, setTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Loading states\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Get school ID from user\n    const schoolId = (user === null || user === void 0 ? void 0 : (_user_school_ids = user.school_ids) === null || _user_school_ids === void 0 ? void 0 : _user_school_ids[0]) || (user === null || user === void 0 ? void 0 : user.school_id);\n    // Fetch timetable data from API\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"TimetablePage.useEffect\": ()=>{\n            const fetchTimetableData = {\n                \"TimetablePage.useEffect.fetchTimetableData\": async ()=>{\n                    if (!schoolId) {\n                        showError(\"Error\", \"No school ID found\");\n                        setLoadingData(false);\n                        return;\n                    }\n                    try {\n                        setLoadingData(true);\n                        if (selectedClass === 'all_classes') {\n                            // Fetch timetables for all classes\n                            const allClassesResponse = await (0,_app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_6__.getOrganizedTimetable)(schoolId, {});\n                            console.log(\"All Classes Timetable Data:\", allClassesResponse.timetable);\n                            // Group timetable data by class\n                            const classGroupedTimetables = {};\n                            // Get all schedule entries and group by class\n                            const scheduleResponse = await (0,_app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_6__.getTimetable)(schoolId, {});\n                            const scheduleEntries = scheduleResponse.schedule_records;\n                            // Group entries by class name\n                            scheduleEntries.forEach({\n                                \"TimetablePage.useEffect.fetchTimetableData\": (entry)=>{\n                                    if (!classGroupedTimetables[entry.class_name]) {\n                                        classGroupedTimetables[entry.class_name] = {};\n                                        DAYS.forEach({\n                                            \"TimetablePage.useEffect.fetchTimetableData\": (day)=>{\n                                                classGroupedTimetables[entry.class_name][day] = {};\n                                            }\n                                        }[\"TimetablePage.useEffect.fetchTimetableData\"]);\n                                    }\n                                    if (!classGroupedTimetables[entry.class_name][entry.day_of_week]) {\n                                        classGroupedTimetables[entry.class_name][entry.day_of_week] = {};\n                                    }\n                                    classGroupedTimetables[entry.class_name][entry.day_of_week][entry.period_number] = entry;\n                                }\n                            }[\"TimetablePage.useEffect.fetchTimetableData\"]);\n                            setAllClassesTimetable(classGroupedTimetables);\n                            setTimetableData(allClassesResponse.timetable);\n                            setPeriods(allClassesResponse.periods);\n                        } else {\n                            // Build filters for specific class\n                            const filters = {};\n                            if (selectedClass !== 'all_classes') filters.class_id = selectedClass;\n                            // Fetch organized timetable for specific class\n                            const response = await (0,_app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_6__.getOrganizedTimetable)(schoolId, filters);\n                            console.log(\"Timetable Data:\", response.timetable);\n                            setTimetableData(response.timetable);\n                            setPeriods(response.periods);\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching timetable data:\", error);\n                        showError(\"Error\", \"Failed to load timetable data\");\n                    } finally{\n                        setLoadingData(false);\n                    }\n                }\n            }[\"TimetablePage.useEffect.fetchTimetableData\"];\n            fetchTimetableData();\n        }\n    }[\"TimetablePage.useEffect\"], [\n        schoolId,\n        selectedClass\n    ]);\n    // Fetch additional data for modals\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"TimetablePage.useEffect\": ()=>{\n            const fetchAdditionalData = {\n                \"TimetablePage.useEffect.fetchAdditionalData\": async ()=>{\n                    if (!schoolId) return;\n                    console.log(\"Fetching additional data for school ID:\", schoolId);\n                    try {\n                        // Fetch data with individual error handling\n                        const results = await Promise.allSettled([\n                            (0,_app_services_ClassServices__WEBPACK_IMPORTED_MODULE_7__.getClassesBySchool)(schoolId),\n                            (0,_app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_8__.getSubjectsBySchoolId)(schoolId),\n                            (0,_app_services_TeacherServices__WEBPACK_IMPORTED_MODULE_9__.getTeachersBySchool)(schoolId)\n                        ]);\n                        // Handle each result individually\n                        if (results[0].status === 'fulfilled') {\n                            setClasses(results[0].value);\n                        } else {\n                            console.error(\"Failed to fetch classes:\", results[0].reason);\n                            setClasses([]);\n                        }\n                        if (results[1].status === 'fulfilled') {\n                            setSubjects(results[1].value);\n                        } else {\n                            console.error(\"Failed to fetch subjects:\", results[1].reason);\n                            setSubjects([]);\n                        }\n                        if (results[2].status === 'fulfilled') {\n                            setTeachers(results[2].value);\n                        } else {\n                            console.error(\"Failed to fetch teachers:\", results[2].reason);\n                            setTeachers([]);\n                        }\n                        // Show warning if any critical data failed to load\n                        const anyDataFailed = results.some({\n                            \"TimetablePage.useEffect.fetchAdditionalData.anyDataFailed\": (result)=>result.status === 'rejected'\n                        }[\"TimetablePage.useEffect.fetchAdditionalData.anyDataFailed\"]);\n                        if (anyDataFailed) {\n                            showError(\"Warning\", \"Some form data could not be loaded. Some features may be limited.\");\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching additional data:\", error);\n                        showError(\"Error\", \"Failed to load form data\");\n                    }\n                }\n            }[\"TimetablePage.useEffect.fetchAdditionalData\"];\n            fetchAdditionalData();\n        }\n    }[\"TimetablePage.useEffect\"], [\n        schoolId\n    ]); // Removed showError from dependencies\n    // CRUD Functions\n    const handleCreateSchedule = ()=>{\n        setScheduleToEdit(null);\n        setIsTimetableModalOpen(true);\n    };\n    const handleCellClick = (day, periodNumber)=>{\n        var _timetableData_day;\n        // Check if there's already a schedule entry for this slot\n        const existingEntry = (_timetableData_day = timetableData[day]) === null || _timetableData_day === void 0 ? void 0 : _timetableData_day[periodNumber];\n        if (existingEntry) {\n            // Edit existing entry\n            setScheduleToEdit(existingEntry);\n        } else {\n            // Create new entry with pre-filled day and period\n            const period = periods.find((p)=>p.period_number === periodNumber);\n            setScheduleToEdit({\n                day_of_week: day,\n                period_id: (period === null || period === void 0 ? void 0 : period._id) || \"\"\n            });\n        }\n        setIsTimetableModalOpen(true);\n    };\n    // Modal submission function\n    const handleScheduleSubmit = async (data)=>{\n        if (!schoolId) {\n            showError(\"Error\", \"No school ID found\");\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            // Create new schedule entry (we don't have update/delete for now)\n            await (0,_app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_6__.createScheduleEntry)(schoolId, data);\n            // Refresh timetable\n            const filters = {};\n            if (selectedClass !== 'all') filters.class_id = selectedClass;\n            const response = await (0,_app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_6__.getOrganizedTimetable)(schoolId, filters);\n            setTimetableData(response.timetable);\n            setPeriods(response.periods);\n            setIsTimetableModalOpen(false);\n            setScheduleToEdit(null);\n            // Show success notification\n            showSuccess(\"Schedule Created\", \"Schedule entry has been created successfully.\");\n        } catch (error) {\n            console.error(\"Error submitting schedule:\", error);\n            showError(\"Error\", \"Failed to save schedule. Please try again.\");\n            throw error;\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const getSubjectColor = (subject)=>{\n        const colors = {\n            Mathematics: 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300',\n            Physics: 'bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900/30 dark:text-purple-300',\n            Chemistry: 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300',\n            Biology: 'bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/30 dark:text-orange-300',\n            English: 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300',\n            History: 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-300'\n        };\n        return colors[subject] || 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300';\n    };\n    if (loadingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                size: 32,\n                color: \"teal\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                lineNumber: 251,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n            lineNumber: 250,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        allowedRoles: [\n            \"school_admin\",\n            \"admin\",\n            \"super\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            navigation: navigation,\n            onLogout: logout,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-teal rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: \"Time Table Management\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-foreground/60\",\n                                                        children: \"Create and manage class schedules and time tables\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.button, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Add Schedule\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 text-foreground/60\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-foreground\",\n                                                children: \"View:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Class:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedClass,\n                                                onChange: (e)=>setSelectedClass(e.target.value),\n                                                className: \"px-3 py-2 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all_classes\",\n                                                        children: \"All Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    classes.map((classItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: classItem._id,\n                                                            children: classItem.name\n                                                        }, classItem._id, false, {\n                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Week:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedWeek,\n                                                onChange: (e)=>setSelectedWeek(e.target.value),\n                                                className: \"px-3 py-2 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"current\",\n                                                        children: \"Current Week\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"next\",\n                                                        children: \"Next Week\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"custom\",\n                                                        children: \"Custom Range\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-foreground\",\n                                            children: [\n                                                \"Weekly Schedule - \",\n                                                selectedClass === 'all_classes' ? 'All Classes' : ((_classes_find = classes.find((c)=>c._id === selectedClass)) === null || _classes_find === void 0 ? void 0 : _classes_find.name) || 'Unknown Class'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            onClick: handleCreateSchedule,\n                                            className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Add Schedule\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this),\n                                selectedClass === 'all_classes' ? // All Classes View\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8\",\n                                    children: Object.entries(allClassesTimetable).map((param)=>{\n                                        let [className, classTimetable] = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border border-stroke rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-foreground mb-4 bg-gray-50 dark:bg-gray-800 p-3 rounded-md\",\n                                                    children: className\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"overflow-x-auto\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                        className: \"w-full border-collapse\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"border border-stroke p-2 bg-gray-50 dark:bg-gray-800 text-left font-semibold text-foreground text-sm\",\n                                                                            children: \"Period\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                            lineNumber: 355,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        DAYS.map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"border border-stroke p-2 bg-gray-50 dark:bg-gray-800 text-center font-semibold text-foreground min-w-[140px] text-sm\",\n                                                                                children: day\n                                                                            }, day, false, {\n                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                lineNumber: 359,\n                                                                                columnNumber: 31\n                                                                            }, this))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                    lineNumber: 354,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                children: periods.map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"border border-stroke p-2 bg-gray-50 dark:bg-gray-800 font-medium text-foreground text-sm\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-center\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"font-semibold\",\n                                                                                            children: [\n                                                                                                \"P\",\n                                                                                                period.period_number\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                            lineNumber: 370,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-xs text-foreground/60\",\n                                                                                            children: [\n                                                                                                period.start_time.slice(0, 5),\n                                                                                                \"-\",\n                                                                                                period.end_time.slice(0, 5)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                            lineNumber: 371,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                    lineNumber: 369,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                lineNumber: 368,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            DAYS.map((day)=>{\n                                                                                var _classTimetable_day;\n                                                                                const scheduleEntry = (_classTimetable_day = classTimetable[day]) === null || _classTimetable_day === void 0 ? void 0 : _classTimetable_day[period.period_number];\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"border border-stroke p-1\",\n                                                                                    children: scheduleEntry ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"p-2 rounded border \".concat(getSubjectColor(scheduleEntry.subject_name), \" text-xs\"),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"font-semibold mb-1\",\n                                                                                                children: scheduleEntry.subject_name\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                                lineNumber: 385,\n                                                                                                columnNumber: 41\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"opacity-80\",\n                                                                                                children: scheduleEntry.teacher_name\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                                lineNumber: 388,\n                                                                                                columnNumber: 41\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                        lineNumber: 384,\n                                                                                        columnNumber: 39\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"p-2 text-center text-foreground/30 text-xs\",\n                                                                                        children: \"Free\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                        lineNumber: 393,\n                                                                                        columnNumber: 39\n                                                                                    }, this)\n                                                                                }, \"\".concat(className, \"-\").concat(day, \"-\").concat(period.period_number), false, {\n                                                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                    lineNumber: 379,\n                                                                                    columnNumber: 35\n                                                                                }, this);\n                                                                            })\n                                                                        ]\n                                                                    }, period.period_number, true, {\n                                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                        lineNumber: 367,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                lineNumber: 365,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, className, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 15\n                                }, this) : // Single Class View\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full border-collapse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"border border-stroke p-3 bg-gray-50 dark:bg-gray-800 text-left font-semibold text-foreground\",\n                                                            children: \"Period / Day\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        DAYS.map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"border border-stroke p-3 bg-gray-50 dark:bg-gray-800 text-center font-semibold text-foreground min-w-[180px]\",\n                                                                children: day\n                                                            }, day, false, {\n                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: periods.map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"border border-stroke p-3 bg-gray-50 dark:bg-gray-800 font-medium text-foreground\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-semibold\",\n                                                                            children: [\n                                                                                \"P \",\n                                                                                period.period_number\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                            lineNumber: 429,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-foreground/60\",\n                                                                            children: [\n                                                                                period.start_time.slice(0, 5),\n                                                                                \" - \",\n                                                                                period.end_time.slice(0, 5)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                            lineNumber: 430,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                    lineNumber: 428,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            DAYS.map((day)=>{\n                                                                var _timetableData_day;\n                                                                const scheduleEntry = (_timetableData_day = timetableData[day]) === null || _timetableData_day === void 0 ? void 0 : _timetableData_day[period.period_number];\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"border border-stroke p-2 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors\",\n                                                                    onClick: ()=>handleCellClick(day, period.period_number),\n                                                                    children: scheduleEntry ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-3 rounded-lg border-2 \".concat(getSubjectColor(scheduleEntry.subject_name), \" relative group\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-semibold text-sm mb-1\",\n                                                                                children: scheduleEntry.subject_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                lineNumber: 445,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs opacity-80 mb-1\",\n                                                                                children: scheduleEntry.teacher_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                lineNumber: 448,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs opacity-70\",\n                                                                                children: scheduleEntry.class_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                lineNumber: 451,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity p-1 bg-white dark:bg-gray-800 rounded shadow-sm\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    className: \"h-3 w-3 text-foreground/60\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                    lineNumber: 457,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                lineNumber: 456,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                        lineNumber: 444,\n                                                                        columnNumber: 33\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-3 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 text-center text-foreground/40 hover:border-teal hover:text-teal transition-colors\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"h-4 w-4 mx-auto mb-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                lineNumber: 462,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs\",\n                                                                                children: \"Add Class\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                lineNumber: 463,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                        lineNumber: 461,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, \"\".concat(day, \"-\").concat(period.period_number), false, {\n                                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                    lineNumber: 438,\n                                                                    columnNumber: 29\n                                                                }, this);\n                                                            })\n                                                        ]\n                                                    }, period.period_number, true, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Total Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: Object.values(timetableData).reduce((total, day)=>total + Object.values(day).filter((entry)=>entry !== null).length, 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Free Periods\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: DAYS.length * periods.length - Object.values(timetableData).reduce((total, day)=>total + Object.values(day).filter((entry)=>entry !== null).length, 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Teachers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: new Set(Object.values(timetableData).flatMap((day)=>Object.values(day)).filter((entry)=>entry !== null).map((entry)=>entry.teacher_name)).size\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Subjects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 533,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: new Set(Object.values(timetableData).flatMap((day)=>Object.values(day)).filter((entry)=>entry !== null).map((entry)=>entry.subject_name)).size\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 532,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 531,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                    lineNumber: 530,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                            lineNumber: 478,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_TimetableModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    isOpen: isTimetableModalOpen,\n                    onClose: ()=>{\n                        setIsTimetableModalOpen(false);\n                        setScheduleToEdit(null);\n                    },\n                    onSubmit: handleScheduleSubmit,\n                    schedule: scheduleToEdit,\n                    classes: classes,\n                    subjects: subjects,\n                    teachers: teachers,\n                    periods: periods,\n                    loading: isSubmitting,\n                    preSelectedClass: selectedClass !== 'all_classes' ? selectedClass : undefined,\n                    isClassLocked: selectedClass !== 'all_classes'\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                    lineNumber: 552,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Toast__WEBPACK_IMPORTED_MODULE_11__.ToastContainer, {\n                    toasts: toasts,\n                    onClose: removeToast\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                    lineNumber: 570,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n            lineNumber: 258,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n        lineNumber: 257,\n        columnNumber: 5\n    }, this);\n}\n_s(TimetablePage, \"92L3/DB093ESpwcNTSP/EXrapis=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        _components_ui_Toast__WEBPACK_IMPORTED_MODULE_11__.useToast\n    ];\n});\n_c = TimetablePage;\nvar _c;\n$RefreshReg$(_c, \"TimetablePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboards)/school-admin/timetable/page.tsx\n"));

/***/ })

});