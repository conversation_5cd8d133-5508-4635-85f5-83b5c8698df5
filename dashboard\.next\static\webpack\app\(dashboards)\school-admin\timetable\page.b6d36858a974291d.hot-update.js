"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/timetable/page",{

/***/ "(app-pages-browser)/./src/app/(dashboards)/school-admin/timetable/page.tsx":
/*!**************************************************************!*\
  !*** ./src/app/(dashboards)/school-admin/timetable/page.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TimetablePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock4,Edit,Filter,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock-4.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock4,Edit,Filter,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock4,Edit,Filter,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock4,Edit,Filter,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock4,Edit,Filter,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock4,Edit,Filter,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock4,Edit,Filter,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Dashboard/Layouts/SchoolLayout */ \"(app-pages-browser)/./src/components/Dashboard/Layouts/SchoolLayout.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/widgets/CircularLoader */ \"(app-pages-browser)/./src/components/widgets/CircularLoader.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var _components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/utils/ProtectedRoute */ \"(app-pages-browser)/./src/components/utils/ProtectedRoute.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/services/TimetableServices */ \"(app-pages-browser)/./src/app/services/TimetableServices.tsx\");\n/* harmony import */ var _app_services_ClassServices__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/services/ClassServices */ \"(app-pages-browser)/./src/app/services/ClassServices.tsx\");\n/* harmony import */ var _app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/services/SubjectServices */ \"(app-pages-browser)/./src/app/services/SubjectServices.tsx\");\n/* harmony import */ var _app_services_TeacherServices__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/services/TeacherServices */ \"(app-pages-browser)/./src/app/services/TeacherServices.tsx\");\n/* harmony import */ var _components_modals_TimetableModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/modals/TimetableModal */ \"(app-pages-browser)/./src/components/modals/TimetableModal.tsx\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst navigation = {\n    icon: _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    baseHref: \"/school-admin/timetable\",\n    title: \"Time Table\"\n};\nconst DAYS = [\n    'Monday',\n    'Tuesday',\n    'Wednesday',\n    'Thursday',\n    'Friday'\n];\nfunction TimetablePage() {\n    var _user_school_ids, _classes_find;\n    _s();\n    const { logout, user } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    const { toasts, removeToast, showSuccess, showError } = (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_11__.useToast)();\n    // State management\n    const [timetableData, setTimetableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [allClassesTimetable, setAllClassesTimetable] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [periods, setPeriods] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loadingData, setLoadingData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [selectedClass, setSelectedClass] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all_classes');\n    const [selectedWeek, setSelectedWeek] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('current');\n    // Modal states\n    const [isTimetableModalOpen, setIsTimetableModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [scheduleToEdit, setScheduleToEdit] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Additional data for forms\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [teachers, setTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Loading states\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Get school ID from user\n    const schoolId = (user === null || user === void 0 ? void 0 : (_user_school_ids = user.school_ids) === null || _user_school_ids === void 0 ? void 0 : _user_school_ids[0]) || (user === null || user === void 0 ? void 0 : user.school_id);\n    // Fetch timetable data from API\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"TimetablePage.useEffect\": ()=>{\n            const fetchTimetableData = {\n                \"TimetablePage.useEffect.fetchTimetableData\": async ()=>{\n                    if (!schoolId) {\n                        showError(\"Error\", \"No school ID found\");\n                        setLoadingData(false);\n                        return;\n                    }\n                    try {\n                        setLoadingData(true);\n                        if (selectedClass === 'all_classes') {\n                            // Fetch timetables for all classes\n                            const allClassesResponse = await (0,_app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_6__.getOrganizedTimetable)(schoolId, {});\n                            console.log(\"All Classes Timetable Data:\", allClassesResponse.timetable);\n                            // Group timetable data by class\n                            const classGroupedTimetables = {};\n                            // Get all schedule entries and group by class\n                            const scheduleResponse = await (0,_app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_6__.getTimetable)(schoolId, {});\n                            const scheduleEntries = scheduleResponse.schedule_records;\n                            // Group entries by class name\n                            scheduleEntries.forEach({\n                                \"TimetablePage.useEffect.fetchTimetableData\": (entry)=>{\n                                    if (!classGroupedTimetables[entry.class_name]) {\n                                        classGroupedTimetables[entry.class_name] = {};\n                                        DAYS.forEach({\n                                            \"TimetablePage.useEffect.fetchTimetableData\": (day)=>{\n                                                classGroupedTimetables[entry.class_name][day] = {};\n                                            }\n                                        }[\"TimetablePage.useEffect.fetchTimetableData\"]);\n                                    }\n                                    if (!classGroupedTimetables[entry.class_name][entry.day_of_week]) {\n                                        classGroupedTimetables[entry.class_name][entry.day_of_week] = {};\n                                    }\n                                    classGroupedTimetables[entry.class_name][entry.day_of_week][entry.period_number] = entry;\n                                }\n                            }[\"TimetablePage.useEffect.fetchTimetableData\"]);\n                            setAllClassesTimetable(classGroupedTimetables);\n                            setTimetableData(allClassesResponse.timetable);\n                            setPeriods(allClassesResponse.periods);\n                        } else {\n                            // Build filters for specific class\n                            const filters = {};\n                            if (selectedClass !== 'all_classes') filters.class_id = selectedClass;\n                            // Fetch organized timetable for specific class\n                            const response = await (0,_app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_6__.getOrganizedTimetable)(schoolId, filters);\n                            console.log(\"Timetable Data:\", response.timetable);\n                            setTimetableData(response.timetable);\n                            setPeriods(response.periods);\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching timetable data:\", error);\n                        showError(\"Error\", \"Failed to load timetable data\");\n                    } finally{\n                        setLoadingData(false);\n                    }\n                }\n            }[\"TimetablePage.useEffect.fetchTimetableData\"];\n            fetchTimetableData();\n        }\n    }[\"TimetablePage.useEffect\"], [\n        schoolId,\n        selectedClass\n    ]);\n    // Fetch additional data for modals\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"TimetablePage.useEffect\": ()=>{\n            const fetchAdditionalData = {\n                \"TimetablePage.useEffect.fetchAdditionalData\": async ()=>{\n                    if (!schoolId) return;\n                    console.log(\"Fetching additional data for school ID:\", schoolId);\n                    try {\n                        // Fetch data with individual error handling\n                        const results = await Promise.allSettled([\n                            (0,_app_services_ClassServices__WEBPACK_IMPORTED_MODULE_7__.getClassesBySchool)(schoolId),\n                            (0,_app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_8__.getSubjectsBySchoolId)(schoolId),\n                            (0,_app_services_TeacherServices__WEBPACK_IMPORTED_MODULE_9__.getTeachersBySchool)(schoolId)\n                        ]);\n                        // Handle each result individually\n                        if (results[0].status === 'fulfilled') {\n                            setClasses(results[0].value);\n                        } else {\n                            console.error(\"Failed to fetch classes:\", results[0].reason);\n                            setClasses([]);\n                        }\n                        if (results[1].status === 'fulfilled') {\n                            setSubjects(results[1].value);\n                        } else {\n                            console.error(\"Failed to fetch subjects:\", results[1].reason);\n                            setSubjects([]);\n                        }\n                        if (results[2].status === 'fulfilled') {\n                            setTeachers(results[2].value);\n                        } else {\n                            console.error(\"Failed to fetch teachers:\", results[2].reason);\n                            setTeachers([]);\n                        }\n                        // Show warning if any critical data failed to load\n                        const anyDataFailed = results.some({\n                            \"TimetablePage.useEffect.fetchAdditionalData.anyDataFailed\": (result)=>result.status === 'rejected'\n                        }[\"TimetablePage.useEffect.fetchAdditionalData.anyDataFailed\"]);\n                        if (anyDataFailed) {\n                            showError(\"Warning\", \"Some form data could not be loaded. Some features may be limited.\");\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching additional data:\", error);\n                        showError(\"Error\", \"Failed to load form data\");\n                    }\n                }\n            }[\"TimetablePage.useEffect.fetchAdditionalData\"];\n            fetchAdditionalData();\n        }\n    }[\"TimetablePage.useEffect\"], [\n        schoolId\n    ]); // Removed showError from dependencies\n    // CRUD Functions\n    const handleCreateSchedule = ()=>{\n        setScheduleToEdit(null);\n        setIsTimetableModalOpen(true);\n    };\n    const handleCellClick = (day, periodNumber)=>{\n        var _timetableData_day;\n        // Check if there's already a schedule entry for this slot\n        const existingEntry = (_timetableData_day = timetableData[day]) === null || _timetableData_day === void 0 ? void 0 : _timetableData_day[periodNumber];\n        if (existingEntry) {\n            // Edit existing entry\n            setScheduleToEdit(existingEntry);\n        } else {\n            // Create new entry with pre-filled day and period\n            const period = periods.find((p)=>p.period_number === periodNumber);\n            setScheduleToEdit({\n                day_of_week: day,\n                period_id: (period === null || period === void 0 ? void 0 : period._id) || \"\"\n            });\n        }\n        setIsTimetableModalOpen(true);\n        console.log(\"scheduleToEdit\", scheduleToEdit);\n    };\n    // Modal submission function\n    const handleScheduleSubmit = async (data)=>{\n        if (!schoolId) {\n            showError(\"Error\", \"No school ID found\");\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            // Create new schedule entry (we don't have update/delete for now)\n            await (0,_app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_6__.createScheduleEntry)(schoolId, data);\n            // Refresh timetable\n            const filters = {};\n            if (selectedClass !== 'all') filters.class_id = selectedClass;\n            const response = await (0,_app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_6__.getOrganizedTimetable)(schoolId, filters);\n            setTimetableData(response.timetable);\n            setPeriods(response.periods);\n            setIsTimetableModalOpen(false);\n            setScheduleToEdit(null);\n            // Show success notification\n            showSuccess(\"Schedule Created\", \"Schedule entry has been created successfully.\");\n        } catch (error) {\n            console.error(\"Error submitting schedule:\", error);\n            showError(\"Error\", \"Failed to save schedule. Please try again.\");\n            throw error;\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const getSubjectColor = (subject)=>{\n        const colors = {\n            Mathematics: 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300',\n            Physics: 'bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900/30 dark:text-purple-300',\n            Chemistry: 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300',\n            Biology: 'bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/30 dark:text-orange-300',\n            English: 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300',\n            History: 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-300'\n        };\n        return colors[subject] || 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300';\n    };\n    if (loadingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                size: 32,\n                color: \"teal\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                lineNumber: 251,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n            lineNumber: 250,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        allowedRoles: [\n            \"school_admin\",\n            \"admin\",\n            \"super\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            navigation: navigation,\n            onLogout: logout,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-teal rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: \"Time Table Management\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-foreground/60\",\n                                                        children: \"Create and manage class schedules and time tables\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.button, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Add Schedule\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 text-foreground/60\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-foreground\",\n                                                children: \"View:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Class:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedClass,\n                                                onChange: (e)=>setSelectedClass(e.target.value),\n                                                className: \"px-3 py-2 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all_classes\",\n                                                        children: \"All Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    classes.map((classItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: classItem._id,\n                                                            children: classItem.name\n                                                        }, classItem._id, false, {\n                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Week:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedWeek,\n                                                onChange: (e)=>setSelectedWeek(e.target.value),\n                                                className: \"px-3 py-2 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"current\",\n                                                        children: \"Current Week\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"next\",\n                                                        children: \"Next Week\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"custom\",\n                                                        children: \"Custom Range\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-foreground\",\n                                            children: [\n                                                \"Weekly Schedule - \",\n                                                selectedClass === 'all_classes' ? 'All Classes' : ((_classes_find = classes.find((c)=>c._id === selectedClass)) === null || _classes_find === void 0 ? void 0 : _classes_find.name) || 'Unknown Class'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            onClick: handleCreateSchedule,\n                                            className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Add Schedule\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this),\n                                selectedClass === 'all_classes' ? // All Classes View\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8\",\n                                    children: Object.entries(allClassesTimetable).map((param)=>{\n                                        let [className, classTimetable] = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border border-stroke rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-foreground mb-4 bg-gray-50 dark:bg-gray-800 p-3 rounded-md\",\n                                                    children: className\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"overflow-x-auto\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                        className: \"w-full border-collapse\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"border border-stroke p-2 bg-gray-50 dark:bg-gray-800 text-left font-semibold text-foreground text-sm\",\n                                                                            children: \"Period\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                            lineNumber: 355,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        DAYS.map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"border border-stroke p-2 bg-gray-50 dark:bg-gray-800 text-center font-semibold text-foreground min-w-[140px] text-sm\",\n                                                                                children: day\n                                                                            }, day, false, {\n                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                lineNumber: 359,\n                                                                                columnNumber: 31\n                                                                            }, this))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                    lineNumber: 354,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                children: periods.map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"border border-stroke p-2 bg-gray-50 dark:bg-gray-800 font-medium text-foreground text-sm\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-center\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"font-semibold\",\n                                                                                            children: [\n                                                                                                \"P\",\n                                                                                                period.period_number\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                            lineNumber: 370,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-xs text-foreground/60\",\n                                                                                            children: [\n                                                                                                period.start_time.slice(0, 5),\n                                                                                                \"-\",\n                                                                                                period.end_time.slice(0, 5)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                            lineNumber: 371,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                    lineNumber: 369,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                lineNumber: 368,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            DAYS.map((day)=>{\n                                                                                var _classTimetable_day;\n                                                                                const scheduleEntry = (_classTimetable_day = classTimetable[day]) === null || _classTimetable_day === void 0 ? void 0 : _classTimetable_day[period.period_number];\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"border border-stroke p-1\",\n                                                                                    children: scheduleEntry ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"p-2 rounded border \".concat(getSubjectColor(scheduleEntry.subject_name), \" text-xs\"),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"font-semibold mb-1\",\n                                                                                                children: scheduleEntry.subject_name\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                                lineNumber: 385,\n                                                                                                columnNumber: 41\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"opacity-80\",\n                                                                                                children: scheduleEntry.teacher_name\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                                lineNumber: 388,\n                                                                                                columnNumber: 41\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                        lineNumber: 384,\n                                                                                        columnNumber: 39\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"p-2 text-center text-foreground/30 text-xs\",\n                                                                                        children: \"Free\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                        lineNumber: 393,\n                                                                                        columnNumber: 39\n                                                                                    }, this)\n                                                                                }, \"\".concat(className, \"-\").concat(day, \"-\").concat(period.period_number), false, {\n                                                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                    lineNumber: 379,\n                                                                                    columnNumber: 35\n                                                                                }, this);\n                                                                            })\n                                                                        ]\n                                                                    }, period.period_number, true, {\n                                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                        lineNumber: 367,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                lineNumber: 365,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, className, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 15\n                                }, this) : // Single Class View\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full border-collapse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"border border-stroke p-3 bg-gray-50 dark:bg-gray-800 text-left font-semibold text-foreground\",\n                                                            children: \"Period / Day\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        DAYS.map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"border border-stroke p-3 bg-gray-50 dark:bg-gray-800 text-center font-semibold text-foreground min-w-[180px]\",\n                                                                children: day\n                                                            }, day, false, {\n                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: periods.map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"border border-stroke p-3 bg-gray-50 dark:bg-gray-800 font-medium text-foreground\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-semibold\",\n                                                                            children: [\n                                                                                \"P \",\n                                                                                period.period_number\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                            lineNumber: 429,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-foreground/60\",\n                                                                            children: [\n                                                                                period.start_time.slice(0, 5),\n                                                                                \" - \",\n                                                                                period.end_time.slice(0, 5)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                            lineNumber: 430,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                    lineNumber: 428,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            DAYS.map((day)=>{\n                                                                var _timetableData_day;\n                                                                const scheduleEntry = (_timetableData_day = timetableData[day]) === null || _timetableData_day === void 0 ? void 0 : _timetableData_day[period.period_number];\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"border border-stroke p-2 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors\",\n                                                                    onClick: ()=>handleCellClick(day, period.period_number),\n                                                                    children: scheduleEntry ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-3 rounded-lg border-2 \".concat(getSubjectColor(scheduleEntry.subject_name), \" relative group\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-semibold text-sm mb-1\",\n                                                                                children: scheduleEntry.subject_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                lineNumber: 445,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs opacity-80 mb-1\",\n                                                                                children: scheduleEntry.teacher_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                lineNumber: 448,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs opacity-70\",\n                                                                                children: scheduleEntry.class_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                lineNumber: 451,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity p-1 bg-white dark:bg-gray-800 rounded shadow-sm\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    className: \"h-3 w-3 text-foreground/60\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                    lineNumber: 457,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                lineNumber: 456,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                        lineNumber: 444,\n                                                                        columnNumber: 33\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-3 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 text-center text-foreground/40 hover:border-teal hover:text-teal transition-colors\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"h-4 w-4 mx-auto mb-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                lineNumber: 462,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs\",\n                                                                                children: \"Add Class\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                lineNumber: 463,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                        lineNumber: 461,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, \"\".concat(day, \"-\").concat(period.period_number), false, {\n                                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                    lineNumber: 438,\n                                                                    columnNumber: 29\n                                                                }, this);\n                                                            })\n                                                        ]\n                                                    }, period.period_number, true, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Total Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: Object.values(timetableData).reduce((total, day)=>total + Object.values(day).filter((entry)=>entry !== null).length, 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Free Periods\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: DAYS.length * periods.length - Object.values(timetableData).reduce((total, day)=>total + Object.values(day).filter((entry)=>entry !== null).length, 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Teachers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: new Set(Object.values(timetableData).flatMap((day)=>Object.values(day)).filter((entry)=>entry !== null).map((entry)=>entry.teacher_name)).size\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Subjects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 533,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: new Set(Object.values(timetableData).flatMap((day)=>Object.values(day)).filter((entry)=>entry !== null).map((entry)=>entry.subject_name)).size\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 532,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 531,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                    lineNumber: 530,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                            lineNumber: 478,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_TimetableModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    isOpen: isTimetableModalOpen,\n                    onClose: ()=>{\n                        setIsTimetableModalOpen(false);\n                        setScheduleToEdit(null);\n                    },\n                    onSubmit: handleScheduleSubmit,\n                    schedule: scheduleToEdit,\n                    classes: classes,\n                    subjects: subjects,\n                    teachers: teachers,\n                    periods: periods,\n                    loading: isSubmitting,\n                    preSelectedClass: selectedClass !== 'all_classes' ? selectedClass : undefined,\n                    isClassLocked: selectedClass !== 'all_classes'\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                    lineNumber: 552,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Toast__WEBPACK_IMPORTED_MODULE_11__.ToastContainer, {\n                    toasts: toasts,\n                    onClose: removeToast\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                    lineNumber: 570,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n            lineNumber: 258,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n        lineNumber: 257,\n        columnNumber: 5\n    }, this);\n}\n_s(TimetablePage, \"92L3/DB093ESpwcNTSP/EXrapis=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        _components_ui_Toast__WEBPACK_IMPORTED_MODULE_11__.useToast\n    ];\n});\n_c = TimetablePage;\nvar _c;\n$RefreshReg$(_c, \"TimetablePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboards)/school-admin/timetable/page.tsx\n"));

/***/ })

});