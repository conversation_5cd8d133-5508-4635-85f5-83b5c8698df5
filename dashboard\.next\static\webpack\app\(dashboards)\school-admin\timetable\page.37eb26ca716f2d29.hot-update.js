"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/timetable/page",{

/***/ "(app-pages-browser)/./src/app/(dashboards)/school-admin/timetable/page.tsx":
/*!**************************************************************!*\
  !*** ./src/app/(dashboards)/school-admin/timetable/page.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TimetablePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock4,Edit,Filter,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock-4.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock4,Edit,Filter,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock4,Edit,Filter,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock4,Edit,Filter,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock4,Edit,Filter,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock4,Edit,Filter,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock4,Edit,Filter,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Dashboard/Layouts/SchoolLayout */ \"(app-pages-browser)/./src/components/Dashboard/Layouts/SchoolLayout.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/widgets/CircularLoader */ \"(app-pages-browser)/./src/components/widgets/CircularLoader.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var _components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/utils/ProtectedRoute */ \"(app-pages-browser)/./src/components/utils/ProtectedRoute.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/services/TimetableServices */ \"(app-pages-browser)/./src/app/services/TimetableServices.tsx\");\n/* harmony import */ var _app_services_ClassServices__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/services/ClassServices */ \"(app-pages-browser)/./src/app/services/ClassServices.tsx\");\n/* harmony import */ var _app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/services/SubjectServices */ \"(app-pages-browser)/./src/app/services/SubjectServices.tsx\");\n/* harmony import */ var _app_services_TeacherServices__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/services/TeacherServices */ \"(app-pages-browser)/./src/app/services/TeacherServices.tsx\");\n/* harmony import */ var _components_modals_TimetableModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/modals/TimetableModal */ \"(app-pages-browser)/./src/components/modals/TimetableModal.tsx\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst navigation = {\n    icon: _barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    baseHref: \"/school-admin/timetable\",\n    title: \"Time Table\"\n};\nconst DAYS = [\n    'Monday',\n    'Tuesday',\n    'Wednesday',\n    'Thursday',\n    'Friday'\n];\nfunction TimetablePage() {\n    var _user_school_ids;\n    _s();\n    const { logout, user } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    const { toasts, removeToast, showSuccess, showError } = (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_11__.useToast)();\n    // State management\n    const [timetableData, setTimetableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [allClassesTimetable, setAllClassesTimetable] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [periods, setPeriods] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loadingData, setLoadingData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [selectedClass, setSelectedClass] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all_classes');\n    const [selectedWeek, setSelectedWeek] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('current');\n    // Modal states\n    const [isTimetableModalOpen, setIsTimetableModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [scheduleToEdit, setScheduleToEdit] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Additional data for forms\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [teachers, setTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Loading states\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Get school ID from user\n    const schoolId = (user === null || user === void 0 ? void 0 : (_user_school_ids = user.school_ids) === null || _user_school_ids === void 0 ? void 0 : _user_school_ids[0]) || (user === null || user === void 0 ? void 0 : user.school_id);\n    // Fetch timetable data from API\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"TimetablePage.useEffect\": ()=>{\n            const fetchTimetableData = {\n                \"TimetablePage.useEffect.fetchTimetableData\": async ()=>{\n                    if (!schoolId) {\n                        showError(\"Error\", \"No school ID found\");\n                        setLoadingData(false);\n                        return;\n                    }\n                    try {\n                        setLoadingData(true);\n                        if (selectedClass === 'all_classes') {\n                            // Fetch timetables for all classes\n                            const allClassesResponse = await (0,_app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_6__.getOrganizedTimetable)(schoolId, {});\n                            console.log(\"All Classes Timetable Data:\", allClassesResponse.timetable);\n                            // Group timetable data by class\n                            const classGroupedTimetables = {};\n                            // Get all schedule entries and group by class\n                            const scheduleResponse = await (0,_app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_6__.getTimetable)(schoolId, {});\n                            const scheduleEntries = scheduleResponse.schedule_records;\n                            // Group entries by class name\n                            scheduleEntries.forEach({\n                                \"TimetablePage.useEffect.fetchTimetableData\": (entry)=>{\n                                    if (!classGroupedTimetables[entry.class_name]) {\n                                        classGroupedTimetables[entry.class_name] = {};\n                                        DAYS.forEach({\n                                            \"TimetablePage.useEffect.fetchTimetableData\": (day)=>{\n                                                classGroupedTimetables[entry.class_name][day] = {};\n                                            }\n                                        }[\"TimetablePage.useEffect.fetchTimetableData\"]);\n                                    }\n                                    if (!classGroupedTimetables[entry.class_name][entry.day_of_week]) {\n                                        classGroupedTimetables[entry.class_name][entry.day_of_week] = {};\n                                    }\n                                    classGroupedTimetables[entry.class_name][entry.day_of_week][entry.period_number] = entry;\n                                }\n                            }[\"TimetablePage.useEffect.fetchTimetableData\"]);\n                            setAllClassesTimetable(classGroupedTimetables);\n                            setTimetableData(allClassesResponse.timetable);\n                            setPeriods(allClassesResponse.periods);\n                        } else {\n                            // Build filters for specific class\n                            const filters = {};\n                            if (selectedClass !== 'all_classes') filters.class_id = selectedClass;\n                            // Fetch organized timetable for specific class\n                            const response = await (0,_app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_6__.getOrganizedTimetable)(schoolId, filters);\n                            console.log(\"Timetable Data:\", response.timetable);\n                            setTimetableData(response.timetable);\n                            setPeriods(response.periods);\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching timetable data:\", error);\n                        showError(\"Error\", \"Failed to load timetable data\");\n                    } finally{\n                        setLoadingData(false);\n                    }\n                }\n            }[\"TimetablePage.useEffect.fetchTimetableData\"];\n            fetchTimetableData();\n        }\n    }[\"TimetablePage.useEffect\"], [\n        schoolId,\n        selectedClass\n    ]);\n    // Fetch additional data for modals\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"TimetablePage.useEffect\": ()=>{\n            const fetchAdditionalData = {\n                \"TimetablePage.useEffect.fetchAdditionalData\": async ()=>{\n                    if (!schoolId) return;\n                    console.log(\"Fetching additional data for school ID:\", schoolId);\n                    try {\n                        // Fetch data with individual error handling\n                        const results = await Promise.allSettled([\n                            (0,_app_services_ClassServices__WEBPACK_IMPORTED_MODULE_7__.getClassesBySchool)(schoolId),\n                            (0,_app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_8__.getSubjectsBySchoolId)(schoolId),\n                            (0,_app_services_TeacherServices__WEBPACK_IMPORTED_MODULE_9__.getTeachersBySchool)(schoolId)\n                        ]);\n                        // Handle each result individually\n                        if (results[0].status === 'fulfilled') {\n                            setClasses(results[0].value);\n                        } else {\n                            console.error(\"Failed to fetch classes:\", results[0].reason);\n                            setClasses([]);\n                        }\n                        if (results[1].status === 'fulfilled') {\n                            setSubjects(results[1].value);\n                        } else {\n                            console.error(\"Failed to fetch subjects:\", results[1].reason);\n                            setSubjects([]);\n                        }\n                        if (results[2].status === 'fulfilled') {\n                            setTeachers(results[2].value);\n                        } else {\n                            console.error(\"Failed to fetch teachers:\", results[2].reason);\n                            setTeachers([]);\n                        }\n                        // Show warning if any critical data failed to load\n                        const anyDataFailed = results.some({\n                            \"TimetablePage.useEffect.fetchAdditionalData.anyDataFailed\": (result)=>result.status === 'rejected'\n                        }[\"TimetablePage.useEffect.fetchAdditionalData.anyDataFailed\"]);\n                        if (anyDataFailed) {\n                            showError(\"Warning\", \"Some form data could not be loaded. Some features may be limited.\");\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching additional data:\", error);\n                        showError(\"Error\", \"Failed to load form data\");\n                    }\n                }\n            }[\"TimetablePage.useEffect.fetchAdditionalData\"];\n            fetchAdditionalData();\n        }\n    }[\"TimetablePage.useEffect\"], [\n        schoolId\n    ]); // Removed showError from dependencies\n    // CRUD Functions\n    const handleCreateSchedule = ()=>{\n        setScheduleToEdit(null);\n        setIsTimetableModalOpen(true);\n    };\n    const handleCellClick = (day, periodNumber)=>{\n        var _timetableData_day;\n        // Check if there's already a schedule entry for this slot\n        const existingEntry = (_timetableData_day = timetableData[day]) === null || _timetableData_day === void 0 ? void 0 : _timetableData_day[periodNumber];\n        if (existingEntry) {\n            // Edit existing entry\n            setScheduleToEdit(existingEntry);\n        } else {\n            // Create new entry with pre-filled day and period\n            const period = periods.find((p)=>p.period_number === periodNumber);\n            setScheduleToEdit({\n                day_of_week: day,\n                period_id: (period === null || period === void 0 ? void 0 : period._id) || \"\"\n            });\n        }\n        setIsTimetableModalOpen(true);\n    };\n    // Modal submission function\n    const handleScheduleSubmit = async (data)=>{\n        if (!schoolId) {\n            showError(\"Error\", \"No school ID found\");\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            // Create new schedule entry (we don't have update/delete for now)\n            await (0,_app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_6__.createScheduleEntry)(schoolId, data);\n            // Refresh timetable\n            const filters = {};\n            if (selectedClass !== 'all') filters.class_id = selectedClass;\n            const response = await (0,_app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_6__.getOrganizedTimetable)(schoolId, filters);\n            setTimetableData(response.timetable);\n            setPeriods(response.periods);\n            setIsTimetableModalOpen(false);\n            setScheduleToEdit(null);\n            // Show success notification\n            showSuccess(\"Schedule Created\", \"Schedule entry has been created successfully.\");\n        } catch (error) {\n            console.error(\"Error submitting schedule:\", error);\n            showError(\"Error\", \"Failed to save schedule. Please try again.\");\n            throw error;\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const getSubjectColor = (subject)=>{\n        const colors = {\n            Mathematics: 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300',\n            Physics: 'bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900/30 dark:text-purple-300',\n            Chemistry: 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300',\n            Biology: 'bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/30 dark:text-orange-300',\n            English: 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300',\n            History: 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-300'\n        };\n        return colors[subject] || 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300';\n    };\n    if (loadingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                size: 32,\n                color: \"teal\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                lineNumber: 250,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n            lineNumber: 249,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        allowedRoles: [\n            \"school_admin\",\n            \"admin\",\n            \"super\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            navigation: navigation,\n            onLogout: logout,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-teal rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: \"Time Table Management\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-foreground/60\",\n                                                        children: \"Create and manage class schedules and time tables\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.button, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Add Schedule\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 text-foreground/60\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-foreground\",\n                                                children: \"View:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Class:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedClass,\n                                                onChange: (e)=>setSelectedClass(e.target.value),\n                                                className: \"px-3 py-2 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    classes.map((classItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: classItem._id,\n                                                            children: [\n                                                                classItem.name,\n                                                                \" (Grade \",\n                                                                classItem.grade_level,\n                                                                \")\"\n                                                            ]\n                                                        }, classItem._id, true, {\n                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Week:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedWeek,\n                                                onChange: (e)=>setSelectedWeek(e.target.value),\n                                                className: \"px-3 py-2 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"current\",\n                                                        children: \"Current Week\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"next\",\n                                                        children: \"Next Week\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"custom\",\n                                                        children: \"Custom Range\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-foreground\",\n                                            children: [\n                                                \"Weekly Schedule - \",\n                                                selectedClass\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            onClick: handleCreateSchedule,\n                                            className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Add Schedule\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full border-collapse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"border border-stroke p-3 bg-gray-50 dark:bg-gray-800 text-left font-semibold text-foreground\",\n                                                            children: \"Period / Day\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        DAYS.map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"border border-stroke p-3 bg-gray-50 dark:bg-gray-800 text-center font-semibold text-foreground min-w-[180px]\",\n                                                                children: day\n                                                            }, day, false, {\n                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: periods.map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"border border-stroke p-3 bg-gray-50 dark:bg-gray-800 font-medium text-foreground\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-semibold\",\n                                                                            children: [\n                                                                                \"Period \",\n                                                                                period.period_number\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                            lineNumber: 361,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-foreground/60\",\n                                                                            children: [\n                                                                                period.start_time.slice(0, 5),\n                                                                                \" - \",\n                                                                                period.end_time.slice(0, 5)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                            lineNumber: 362,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                    lineNumber: 360,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            DAYS.map((day)=>{\n                                                                var _timetableData_day;\n                                                                const scheduleEntry = (_timetableData_day = timetableData[day]) === null || _timetableData_day === void 0 ? void 0 : _timetableData_day[period.period_number];\n                                                                console.log(\"Schedule Entry:\", scheduleEntry);\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"border border-stroke p-2 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors\",\n                                                                    onClick: ()=>handleCellClick(day, period.period_number),\n                                                                    children: scheduleEntry ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-3 rounded-lg border-2 \".concat(getSubjectColor(scheduleEntry.subject_name), \" relative group\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-semibold text-sm mb-1\",\n                                                                                children: scheduleEntry.subject_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                lineNumber: 378,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs opacity-80 mb-1\",\n                                                                                children: scheduleEntry.teacher_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                lineNumber: 381,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity p-1 bg-white dark:bg-gray-800 rounded shadow-sm\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    className: \"h-3 w-3 text-foreground/60\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                    lineNumber: 387,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                lineNumber: 386,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                        lineNumber: 377,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-3 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 text-center text-foreground/40 hover:border-teal hover:text-teal transition-colors\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"h-4 w-4 mx-auto mb-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                lineNumber: 392,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs\",\n                                                                                children: \"Add Class\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                                lineNumber: 393,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                        lineNumber: 391,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, \"\".concat(day, \"-\").concat(period.period_number), false, {\n                                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 27\n                                                                }, this);\n                                                            })\n                                                        ]\n                                                    }, period.period_number, true, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Total Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: Object.values(timetableData).reduce((total, day)=>total + Object.values(day).filter((entry)=>entry !== null).length, 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Free Periods\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: DAYS.length * periods.length - Object.values(timetableData).reduce((total, day)=>total + Object.values(day).filter((entry)=>entry !== null).length, 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Teachers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: new Set(Object.values(timetableData).flatMap((day)=>Object.values(day)).filter((entry)=>entry !== null).map((entry)=>entry.teacher_name)).size\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                    lineNumber: 440,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Subjects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: new Set(Object.values(timetableData).flatMap((day)=>Object.values(day)).filter((entry)=>entry !== null).map((entry)=>entry.subject_name)).size\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock4_Edit_Filter_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_TimetableModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    isOpen: isTimetableModalOpen,\n                    onClose: ()=>{\n                        setIsTimetableModalOpen(false);\n                        setScheduleToEdit(null);\n                    },\n                    onSubmit: handleScheduleSubmit,\n                    schedule: scheduleToEdit,\n                    classes: classes,\n                    subjects: subjects,\n                    teachers: teachers,\n                    periods: periods,\n                    loading: isSubmitting\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                    lineNumber: 481,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Toast__WEBPACK_IMPORTED_MODULE_11__.ToastContainer, {\n                    toasts: toasts,\n                    onClose: removeToast\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n                    lineNumber: 497,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n            lineNumber: 257,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\timetable\\\\page.tsx\",\n        lineNumber: 256,\n        columnNumber: 5\n    }, this);\n}\n_s(TimetablePage, \"92L3/DB093ESpwcNTSP/EXrapis=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        _components_ui_Toast__WEBPACK_IMPORTED_MODULE_11__.useToast\n    ];\n});\n_c = TimetablePage;\nvar _c;\n$RefreshReg$(_c, \"TimetablePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboards)/school-admin/timetable/page.tsx\n"));

/***/ })

});