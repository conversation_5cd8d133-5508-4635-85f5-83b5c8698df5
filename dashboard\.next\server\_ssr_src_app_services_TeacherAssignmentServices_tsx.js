"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_app_services_TeacherAssignmentServices_tsx";
exports.ids = ["_ssr_src_app_services_TeacherAssignmentServices_tsx"];
exports.modules = {

/***/ "(ssr)/./src/app/services/TeacherAssignmentServices.tsx":
/*!********************************************************!*\
  !*** ./src/app/services/TeacherAssignmentServices.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getTeacherAssignments: () => (/* binding */ getTeacherAssignments),\n/* harmony export */   getTeacherClasses: () => (/* binding */ getTeacherClasses),\n/* harmony export */   getTeacherStudents: () => (/* binding */ getTeacherStudents),\n/* harmony export */   getTeacherSubjects: () => (/* binding */ getTeacherSubjects)\n/* harmony export */ });\n/* harmony import */ var _UserServices__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./UserServices */ \"(ssr)/./src/app/services/UserServices.tsx\");\n\nconst BASE_API_URL = process.env.BASE_API_URL || \"https://scolarify.onrender.com/api\";\n// Get teacher's assigned classes and subjects for a specific school\nasync function getTeacherAssignments(schoolId) {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_0__.getTokenFromCookie)(\"idToken\");\n    try {\n        const response = await fetch(`${BASE_API_URL}/teacher/assignments/${schoolId}`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${token}`\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching teacher assignments:\", response.statusText);\n            throw new Error(\"Failed to fetch teacher assignments\");\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Fetch teacher assignments error:\", error);\n        throw new Error(\"Failed to fetch teacher assignments\");\n    }\n}\n// Get classes assigned to the current teacher\nasync function getTeacherClasses(schoolId) {\n    try {\n        const assignments = await getTeacherAssignments(schoolId);\n        return assignments.assigned_classes;\n    } catch (error) {\n        console.error(\"Error fetching teacher classes:\", error);\n        return [];\n    }\n}\n// Get subjects assigned to the current teacher\nasync function getTeacherSubjects(schoolId) {\n    try {\n        const assignments = await getTeacherAssignments(schoolId);\n        return assignments.assigned_subjects;\n    } catch (error) {\n        console.error(\"Error fetching teacher subjects:\", error);\n        return [];\n    }\n}\n// Get students in teacher's assigned classes\nasync function getTeacherStudents(schoolId) {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_0__.getTokenFromCookie)(\"idToken\");\n    try {\n        const response = await fetch(`${BASE_API_URL}/teacher/students/${schoolId}`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${token}`\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching teacher students:\", response.statusText);\n            throw new Error(\"Failed to fetch teacher students\");\n        }\n        const data = await response.json();\n        return data.students || [];\n    } catch (error) {\n        console.error(\"Fetch teacher students error:\", error);\n        return [];\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/services/TeacherAssignmentServices.tsx\n");

/***/ })

};
;