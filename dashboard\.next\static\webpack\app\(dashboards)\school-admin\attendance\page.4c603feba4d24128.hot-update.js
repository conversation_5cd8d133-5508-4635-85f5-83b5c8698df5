"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/attendance/page",{

/***/ "(app-pages-browser)/./src/app/(dashboards)/school-admin/attendance/page.tsx":
/*!***************************************************************!*\
  !*** ./src/app/(dashboards)/school-admin/attendance/page.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AttendancePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,FileCheck2,Filter,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check-2.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,FileCheck2,Filter,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,FileCheck2,Filter,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,FileCheck2,Filter,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,FileCheck2,Filter,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,FileCheck2,Filter,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,FileCheck2,Filter,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Dashboard/Layouts/SchoolLayout */ \"(app-pages-browser)/./src/components/Dashboard/Layouts/SchoolLayout.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_utils_TableFix__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/utils/TableFix */ \"(app-pages-browser)/./src/components/utils/TableFix.tsx\");\n/* harmony import */ var _components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/widgets/CircularLoader */ \"(app-pages-browser)/./src/components/widgets/CircularLoader.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var _components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/utils/ProtectedRoute */ \"(app-pages-browser)/./src/components/utils/ProtectedRoute.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/services/AttendanceServices */ \"(app-pages-browser)/./src/app/services/AttendanceServices.tsx\");\n/* harmony import */ var _app_services_StudentServices__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/services/StudentServices */ \"(app-pages-browser)/./src/app/services/StudentServices.tsx\");\n/* harmony import */ var _app_services_ClassServices__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/services/ClassServices */ \"(app-pages-browser)/./src/app/services/ClassServices.tsx\");\n/* harmony import */ var _app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/services/SubjectServices */ \"(app-pages-browser)/./src/app/services/SubjectServices.tsx\");\n/* harmony import */ var _app_services_ClassScheduleServices__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/services/ClassScheduleServices */ \"(app-pages-browser)/./src/app/services/ClassScheduleServices.tsx\");\n/* harmony import */ var _components_modals_AttendanceModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/modals/AttendanceModal */ \"(app-pages-browser)/./src/components/modals/AttendanceModal.tsx\");\n/* harmony import */ var _components_modals_DeleteConfirmationModal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/modals/DeleteConfirmationModal */ \"(app-pages-browser)/./src/components/modals/DeleteConfirmationModal.tsx\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst navigation = {\n    icon: _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n    baseHref: \"/school-admin/attendance\",\n    title: \"Attendance\"\n};\nfunction AttendancePage() {\n    var _user_school_ids;\n    _s();\n    const { logout, user } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const { toasts, removeToast, showSuccess, showError } = (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_14__.useToast)();\n    // State management\n    const [attendanceRecords, setAttendanceRecords] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        totalStudents: 0,\n        presentToday: 0,\n        absentToday: 0,\n        lateToday: 0,\n        excusedToday: 0,\n        attendanceRate: 0\n    });\n    const [loadingData, setLoadingData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(new Date().toISOString().split('T')[0]);\n    const [selectedClass, setSelectedClass] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    // Modal states\n    const [isAttendanceModalOpen, setIsAttendanceModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isDeleteModalOpen, setIsDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [attendanceToEdit, setAttendanceToEdit] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [attendanceToDelete, setAttendanceToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [deleteType, setDeleteType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"single\");\n    const [selectedAttendances, setSelectedAttendances] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Additional data for forms\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [schedules, setSchedules] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Loading states\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [clearSelection, setClearSelection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Get school ID from user\n    const schoolId = (user === null || user === void 0 ? void 0 : (_user_school_ids = user.school_ids) === null || _user_school_ids === void 0 ? void 0 : _user_school_ids[0]) || (user === null || user === void 0 ? void 0 : user.school_id);\n    // Fetch attendance data from API\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"AttendancePage.useEffect\": ()=>{\n            const fetchAttendanceData = {\n                \"AttendancePage.useEffect.fetchAttendanceData\": async ()=>{\n                    if (!schoolId) {\n                        showError(\"Error\", \"No school ID found\");\n                        setLoadingData(false);\n                        return;\n                    }\n                    try {\n                        setLoadingData(true);\n                        // Build filters\n                        const filters = {};\n                        if (selectedDate) filters.date = selectedDate;\n                        if (selectedClass !== 'all') filters.class_id = selectedClass;\n                        if (selectedStatus !== 'all') filters.status = selectedStatus;\n                        // Fetch records and stats in parallel\n                        const [recordsResponse, statsResponse] = await Promise.all([\n                            (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.getAttendanceRecords)(schoolId, filters),\n                            (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.getAttendanceStats)(schoolId, selectedDate)\n                        ]);\n                        setAttendanceRecords(recordsResponse.attendance_records);\n                        setStats(statsResponse.stats);\n                    } catch (error) {\n                        console.error(\"Error fetching attendance data:\", error);\n                        showError(\"Error\", \"Failed to load attendance data\");\n                    } finally{\n                        setLoadingData(false);\n                    }\n                }\n            }[\"AttendancePage.useEffect.fetchAttendanceData\"];\n            fetchAttendanceData();\n        }\n    }[\"AttendancePage.useEffect\"], [\n        schoolId,\n        selectedDate,\n        selectedClass,\n        selectedStatus\n    ]);\n    // Fetch additional data for modals\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"AttendancePage.useEffect\": ()=>{\n            const fetchAdditionalData = {\n                \"AttendancePage.useEffect.fetchAdditionalData\": async ()=>{\n                    if (!schoolId) return;\n                    try {\n                        // Fetch data with individual error handling\n                        const results = await Promise.allSettled([\n                            (0,_app_services_StudentServices__WEBPACK_IMPORTED_MODULE_8__.getStudentsBySchool)(schoolId),\n                            (0,_app_services_ClassServices__WEBPACK_IMPORTED_MODULE_9__.getClassesBySchool)(schoolId),\n                            (0,_app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_10__.getAllSubjects)(),\n                            (0,_app_services_ClassScheduleServices__WEBPACK_IMPORTED_MODULE_11__.getClassSchedulesBySchool)(schoolId)\n                        ]);\n                        // Handle each result individually\n                        if (results[0].status === 'fulfilled') {\n                            setStudents(results[0].value);\n                        } else {\n                            console.error(\"Failed to fetch students:\", results[0].reason);\n                            setStudents([]);\n                        }\n                        if (results[1].status === 'fulfilled') {\n                            setClasses(results[1].value);\n                        } else {\n                            console.error(\"Failed to fetch classes:\", results[1].reason);\n                            setClasses([]);\n                        }\n                        if (results[2].status === 'fulfilled') {\n                            setSubjects(results[2].value);\n                        } else {\n                            console.error(\"Failed to fetch subjects:\", results[2].reason);\n                            setSubjects([]);\n                        }\n                        if (results[3].status === 'fulfilled') {\n                            setSchedules(results[3].value);\n                        } else {\n                            console.error(\"Failed to fetch schedules:\", results[3].reason);\n                            setSchedules([]);\n                        // Don't show error for schedules as it's not critical\n                        }\n                        // Only show error if critical data failed to load\n                        const criticalDataFailed = results[0].status === 'rejected' || results[1].status === 'rejected' || results[2].status === 'rejected';\n                        if (criticalDataFailed) {\n                            showError(\"Warning\", \"Some form data could not be loaded. Some features may be limited.\");\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching additional data:\", error);\n                        showError(\"Error\", \"Failed to load form data\");\n                    }\n                }\n            }[\"AttendancePage.useEffect.fetchAdditionalData\"];\n            fetchAdditionalData();\n        }\n    }[\"AttendancePage.useEffect\"], [\n        schoolId\n    ]); // Removed showError from dependencies\n    // CRUD Functions\n    const handleCreateAttendance = ()=>{\n        setAttendanceToEdit(null);\n        setIsAttendanceModalOpen(true);\n    };\n    const handleEditAttendance = (attendance)=>{\n        setAttendanceToEdit(attendance);\n        setIsAttendanceModalOpen(true);\n    };\n    const handleDeleteAttendance = (attendance)=>{\n        setAttendanceToDelete(attendance);\n        setDeleteType(\"single\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleDeleteMultiple = ()=>{\n        setDeleteType(\"multiple\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleSelectionChange = (selectedRows)=>{\n        setSelectedAttendances(selectedRows);\n    };\n    // Modal submission functions\n    const handleAttendanceSubmit = async (data)=>{\n        if (!schoolId) {\n            showError(\"Error\", \"No school ID found\");\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            if (attendanceToEdit) {\n                // Update existing attendance\n                await (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.updateAttendance)(attendanceToEdit._id, data);\n            } else {\n                // Create new attendance\n                await (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.createAttendance)({\n                    ...data,\n                    school_id: schoolId\n                });\n            }\n            // Refresh attendance list\n            const filters = {};\n            if (selectedDate) filters.date = selectedDate;\n            if (selectedClass !== 'all') filters.class_id = selectedClass;\n            if (selectedStatus !== 'all') filters.status = selectedStatus;\n            const [recordsResponse, statsResponse] = await Promise.all([\n                (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.getAttendanceRecords)(schoolId, filters),\n                (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.getAttendanceStats)(schoolId, selectedDate)\n            ]);\n            setAttendanceRecords(recordsResponse.attendance_records);\n            setStats(statsResponse.stats);\n            setIsAttendanceModalOpen(false);\n            setAttendanceToEdit(null);\n            // Show success notification\n            showSuccess(attendanceToEdit ? \"Attendance Updated\" : \"Attendance Marked\", attendanceToEdit ? \"Attendance record has been updated successfully.\" : \"Attendance has been marked successfully.\");\n        } catch (error) {\n            console.error(\"Error submitting attendance:\", error);\n            showError(\"Error\", \"Failed to save attendance. Please try again.\");\n            throw error;\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleDeleteConfirm = async ()=>{\n        if (!schoolId) return;\n        try {\n            if (deleteType === \"single\" && attendanceToDelete) {\n                await (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.deleteAttendance)(attendanceToDelete._id);\n            } else if (deleteType === \"multiple\") {\n                const selectedIds = selectedAttendances.map((a)=>a._id);\n                await (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.deleteMultipleAttendances)(selectedIds);\n                setClearSelection(true);\n            }\n            // Refresh attendance list\n            const filters = {};\n            if (selectedDate) filters.date = selectedDate;\n            if (selectedClass !== 'all') filters.class_id = selectedClass;\n            if (selectedStatus !== 'all') filters.status = selectedStatus;\n            const [recordsResponse, statsResponse] = await Promise.all([\n                (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.getAttendanceRecords)(schoolId, filters),\n                (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.getAttendanceStats)(schoolId, selectedDate)\n            ]);\n            setAttendanceRecords(recordsResponse.attendance_records);\n            setStats(statsResponse.stats);\n            setIsDeleteModalOpen(false);\n            setAttendanceToDelete(null);\n            setSelectedAttendances([]);\n            // Show success notification\n            if (deleteType === \"single\") {\n                showSuccess(\"Attendance Deleted\", \"Attendance record has been deleted successfully.\");\n            } else {\n                showSuccess(\"Attendances Deleted\", \"\".concat(selectedAttendances.length, \" attendance records have been deleted successfully.\"));\n            }\n        } catch (error) {\n            console.error(\"Error deleting attendance(s):\", error);\n            showError(\"Error\", \"Failed to delete attendance record(s). Please try again.\");\n            throw error;\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'Present':\n                return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';\n            case 'Absent':\n                return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';\n            case 'Late':\n                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';\n            case 'Excused':\n                return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';\n            default:\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';\n        }\n    };\n    // Table columns\n    const columns = [\n        {\n            header: \"Student\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-medium text-foreground\",\n                            children: row.student_name\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-foreground/60\",\n                            children: row.student_id\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Class\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-medium\",\n                    children: row.class_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 320,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Subject\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.subject_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 326,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Period\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: [\n                        \"Period \",\n                        row.period_number\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 332,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Teacher\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.teacher_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 338,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Status\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getStatusColor(row.status)),\n                    children: row.status\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 344,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Date\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: new Date(row.date).toLocaleDateString()\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    // Actions for the table\n    const actions = [\n        {\n            label: \"Edit\",\n            onClick: (attendance)=>{\n                handleEditAttendance(attendance);\n            }\n        },\n        {\n            label: \"Delete\",\n            onClick: (attendance)=>{\n                handleDeleteAttendance(attendance);\n            }\n        }\n    ];\n    // Filter data based on selections\n    const filteredRecords = attendanceRecords.filter((record)=>{\n        if (selectedClass !== 'all' && record.class_name !== selectedClass) return false;\n        if (selectedStatus !== 'all' && record.status !== selectedStatus) return false;\n        return true;\n    });\n    if (loadingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: 32,\n                color: \"teal\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                lineNumber: 383,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n            lineNumber: 382,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        allowedRoles: [\n            \"school_admin\",\n            \"admin\",\n            \"super\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            navigation: navigation,\n            onLogout: logout,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-teal rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: \"Attendance Management\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-foreground/60\",\n                                                        children: \"Monitor and track student attendance across all classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-foreground\",\n                                                children: [\n                                                    stats.attendanceRate,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-foreground/60\",\n                                                children: \"Today's Rate\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                            lineNumber: 393,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Total Students\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: stats.totalStudents\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Present Today\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-green-600\",\n                                                        children: stats.presentToday\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Absent Today\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-red-600\",\n                                                        children: stats.absentToday\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                    lineNumber: 440,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Late Today\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-yellow-600\",\n                                                        children: stats.lateToday\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-yellow-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                            lineNumber: 415,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-4 w-4 text-foreground/60\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-foreground\",\n                                                children: \"Filters:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Date:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                value: selectedDate,\n                                                onChange: (e)=>setSelectedDate(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Class:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedClass,\n                                                onChange: (e)=>setSelectedClass(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: classes.map((classItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: classItem._id,\n                                                        children: classItem.name\n                                                    }, classItem._id, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Status:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 499,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedStatus,\n                                                onChange: (e)=>setSelectedStatus(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Present\",\n                                                        children: \"Present\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Absent\",\n                                                        children: \"Absent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Late\",\n                                                        children: \"Late\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Excused\",\n                                                        children: \"Excused\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_20__.motion.button, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 518,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Export\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-foreground\",\n                                            children: [\n                                                \"Attendance Records (\",\n                                                filteredRecords.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_20__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            onClick: handleCreateAttendance,\n                                            className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Mark Attendance\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        size: 24,\n                                        color: \"teal\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 542,\n                                        columnNumber: 33\n                                    }, void 0),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_TableFix__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        data: filteredRecords,\n                                        columns: columns,\n                                        actions: actions,\n                                        defaultItemsPerPage: 15,\n                                        onSelectionChange: handleSelectionChange,\n                                        handleDeleteMultiple: handleDeleteMultiple,\n                                        clearSelection: clearSelection,\n                                        onSelectionCleared: ()=>setClearSelection(false)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 543,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                            lineNumber: 525,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 391,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_AttendanceModal__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    isOpen: isAttendanceModalOpen,\n                    onClose: ()=>{\n                        setIsAttendanceModalOpen(false);\n                        setAttendanceToEdit(null);\n                    },\n                    onSubmit: handleAttendanceSubmit,\n                    attendance: attendanceToEdit,\n                    students: students,\n                    classes: classes,\n                    subjects: subjects,\n                    schedules: schedules,\n                    loading: isSubmitting\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 558,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_DeleteConfirmationModal__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    isOpen: isDeleteModalOpen,\n                    onClose: ()=>{\n                        setIsDeleteModalOpen(false);\n                        setAttendanceToDelete(null);\n                    },\n                    onConfirm: handleDeleteConfirm,\n                    title: deleteType === \"single\" ? \"Delete Attendance Record\" : \"Delete Selected Attendance Records\",\n                    message: deleteType === \"single\" ? \"Are you sure you want to delete this attendance record? This action cannot be undone.\" : \"Are you sure you want to delete \".concat(selectedAttendances.length, \" selected attendance records? This action cannot be undone.\"),\n                    itemName: deleteType === \"single\" && attendanceToDelete ? \"\".concat(attendanceToDelete.student_name, \" - \").concat(attendanceToDelete.subject_name, \" (\").concat(new Date(attendanceToDelete.date).toLocaleDateString(), \")\") : undefined,\n                    itemCount: deleteType === \"multiple\" ? selectedAttendances.length : undefined,\n                    type: deleteType\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 574,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Toast__WEBPACK_IMPORTED_MODULE_14__.ToastContainer, {\n                    toasts: toasts,\n                    onClose: removeToast\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 601,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n            lineNumber: 390,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n        lineNumber: 389,\n        columnNumber: 5\n    }, this);\n}\n_s(AttendancePage, \"ANoYHScOC7tz7PVR3x5l6KpvzZA=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        _components_ui_Toast__WEBPACK_IMPORTED_MODULE_14__.useToast\n    ];\n});\n_c = AttendancePage;\nvar _c;\n$RefreshReg$(_c, \"AttendancePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboards)/school-admin/attendance/page.tsx\n"));

/***/ })

});