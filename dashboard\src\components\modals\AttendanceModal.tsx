"use client";

import React, { useState, useEffect } from "react";
import { X, FileCheck2, Save } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface AttendanceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => Promise<void>;
  attendance?: any | null;
  students: any[];
  classes: any[];
  subjects: any[];
  schedules: any[];
  loading?: boolean;
}

export default function AttendanceModal({
  isOpen,
  onClose,
  onSubmit,
  attendance,
  students,
  classes,
  subjects,
  schedules,
  loading = false
}: AttendanceModalProps) {
  const [formData, setFormData] = useState({
    student_id: "",
    schedule_id: "",
    status: "Present" as "Present" | "Absent" | "Late" | "Excused",
    date: new Date().toISOString().split('T')[0],
    academic_year: "2024-2025"
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [filteredStudents, setFilteredStudents] = useState<any[]>([]);

  const isEditing = !!attendance;

  useEffect(() => {
    if (isOpen) {
      if (attendance) {
        setFormData({
          student_id: attendance.student_id || "",
          schedule_id: attendance.schedule_id || "",
          status: attendance.status || "Present",
          date: attendance.date ? new Date(attendance.date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
          academic_year: attendance.academic_year || "2024-2025"
        });
      } else {
        setFormData({
          student_id: "",
          schedule_id: "",
          status: "Present",
          date: new Date().toISOString().split('T')[0],
          academic_year: "2024-2025"
        });
      }
      setErrors({});
    }
  }, [isOpen, attendance]);

  // Filter students based on selected schedule
  useEffect(() => {
    if (formData.schedule_id) {
      const selectedSchedule = schedules.find(s => s._id === formData.schedule_id);
      if (selectedSchedule) {
        const classStudents = students.filter(student => 
          student.class_id === selectedSchedule.class_id
        );
        setFilteredStudents(classStudents);
      }
    } else {
      setFilteredStudents(students);
    }
  }, [formData.schedule_id, schedules, students]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.student_id) {
      newErrors.student_id = "Student is required";
    }
    if (!formData.schedule_id) {
      newErrors.schedule_id = "Class schedule is required";
    }
    if (!formData.date) {
      newErrors.date = "Date is required";
    }
    if (!formData.academic_year) {
      newErrors.academic_year = "Academic year is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      await onSubmit(formData);
      onClose();
    } catch (error) {
      console.error("Error submitting attendance:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  const getScheduleDisplay = (schedule: any) => {
    const subject = subjects.find(s => s._id === schedule.subject_id);
    const classInfo = classes.find(c => c._id === schedule.class_id);
    return `${subject?.name || 'Unknown Subject'} - ${classInfo?.name || 'Unknown Class'} (${schedule.day_of_week})`;
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={onClose}
          />
          
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md mx-4"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                  <FileCheck2 className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {isEditing ? "Edit Attendance" : "Mark Attendance"}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {isEditing ? "Update attendance record" : "Create new attendance record"}
                  </p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit} className="p-6 space-y-4">
              {/* Class Schedule */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Class Schedule
                </label>
                <select
                  value={formData.schedule_id}
                  onChange={(e) => handleInputChange("schedule_id", e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white ${
                    errors.schedule_id 
                      ? "border-red-500 dark:border-red-500" 
                      : "border-gray-300 dark:border-gray-600"
                  }`}
                >
                  <option value="">Select class schedule</option>
                  {schedules.map((schedule) => (
                    <option key={schedule._id} value={schedule._id}>
                      {getScheduleDisplay(schedule)}
                    </option>
                  ))}
                </select>
                {errors.schedule_id && (
                  <p className="mt-1 text-sm text-red-500">{errors.schedule_id}</p>
                )}
              </div>

              {/* Student */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Student
                </label>
                <select
                  value={formData.student_id}
                  onChange={(e) => handleInputChange("student_id", e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white ${
                    errors.student_id 
                      ? "border-red-500 dark:border-red-500" 
                      : "border-gray-300 dark:border-gray-600"
                  }`}
                  disabled={!formData.schedule_id}
                >
                  <option value="">Select student</option>
                  {filteredStudents.map((student) => (
                    <option key={student._id} value={student._id}>
                      {student.first_name} {student.last_name} ({student.student_id})
                    </option>
                  ))}
                </select>
                {errors.student_id && (
                  <p className="mt-1 text-sm text-red-500">{errors.student_id}</p>
                )}
              </div>

              {/* Status */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Status
                </label>
                <select
                  value={formData.status}
                  onChange={(e) => handleInputChange("status", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="Present">Present</option>
                  <option value="Absent">Absent</option>
                  <option value="Late">Late</option>
                  <option value="Excused">Excused</option>
                </select>
              </div>

              {/* Date */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Date
                </label>
                <input
                  type="date"
                  value={formData.date}
                  onChange={(e) => handleInputChange("date", e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white ${
                    errors.date 
                      ? "border-red-500 dark:border-red-500" 
                      : "border-gray-300 dark:border-gray-600"
                  }`}
                />
                {errors.date && (
                  <p className="mt-1 text-sm text-red-500">{errors.date}</p>
                )}
              </div>

              {/* Academic Year */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Academic Year
                </label>
                <input
                  type="text"
                  value={formData.academic_year}
                  onChange={(e) => handleInputChange("academic_year", e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white ${
                    errors.academic_year 
                      ? "border-red-500 dark:border-red-500" 
                      : "border-gray-300 dark:border-gray-600"
                  }`}
                  placeholder="e.g., 2024-2025"
                />
                {errors.academic_year && (
                  <p className="mt-1 text-sm text-red-500">{errors.academic_year}</p>
                )}
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  {isSubmitting ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Save className="h-4 w-4" />
                  )}
                  <span>{isSubmitting ? "Saving..." : (isEditing ? "Update" : "Create")}</span>
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
}
