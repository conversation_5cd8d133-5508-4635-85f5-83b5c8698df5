"use client";

import React from "react";
import usePermissions from "@/app/hooks/usePermissions";
import { Lock } from "lucide-react";

interface PermissionGateProps {
  children: React.ReactNode;
  module?: keyof ReturnType<typeof usePermissions>['permissions'];
  action?: string;
  requiredPermissions?: { module: keyof ReturnType<typeof usePermissions>['permissions']; action: string }[];
  requireAll?: boolean; // If true, user must have ALL permissions. If false, user needs ANY permission
  fallback?: React.ReactNode;
  showFallback?: boolean;
  roles?: string[]; // Alternative: check by roles instead of permissions
}

export default function PermissionGate({
  children,
  module,
  action,
  requiredPermissions,
  requireAll = true,
  fallback,
  showFallback = false,
  roles,
}: PermissionGateProps) {
  const { hasPermission, hasAnyPermission, hasAllPermissions, canAccess, userRole, isLoading } = usePermissions();

  // Show loading state
  if (isLoading) {
    return showFallback ? (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-teal"></div>
      </div>
    ) : null;
  }

  // Check by roles if provided
  if (roles && roles.length > 0) {
    const hasRole = userRole && roles.includes(userRole);
    if (!hasRole) {
      return showFallback ? (
        fallback || (
          <div className="flex items-center justify-center p-8 text-center">
            <div className="max-w-sm">
              <Lock className="h-12 w-12 text-foreground/30 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">Access Restricted</h3>
              <p className="text-foreground/60">
                You don't have the required role to access this feature.
              </p>
            </div>
          </div>
        )
      ) : null;
    }
    return <>{children}</>;
  }

  // Check by specific module and action
  if (module && action) {
    const hasAccess = hasPermission(module, action);
    if (!hasAccess) {
      return showFallback ? (
        fallback || (
          <div className="flex items-center justify-center p-8 text-center">
            <div className="max-w-sm">
              <Lock className="h-12 w-12 text-foreground/30 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">Permission Required</h3>
              <p className="text-foreground/60">
                You don't have permission to {action.replace(/_/g, ' ')} in {module.replace(/_/g, ' ')}.
              </p>
            </div>
          </div>
        )
      ) : null;
    }
    return <>{children}</>;
  }

  // Check by multiple permissions
  if (requiredPermissions && requiredPermissions.length > 0) {
    const hasAccess = requireAll 
      ? canAccess(requiredPermissions)
      : requiredPermissions.some(({ module, action }) => hasPermission(module, action));

    if (!hasAccess) {
      return showFallback ? (
        fallback || (
          <div className="flex items-center justify-center p-8 text-center">
            <div className="max-w-sm">
              <Lock className="h-12 w-12 text-foreground/30 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">Insufficient Permissions</h3>
              <p className="text-foreground/60">
                You don't have the required permissions to access this feature.
              </p>
            </div>
          </div>
        )
      ) : null;
    }
    return <>{children}</>;
  }

  // If no specific permissions are required, show children
  return <>{children}</>;
}

// Convenience components for common permission checks
export function StaffPermissionGate({ children, action, showFallback = false }: {
  children: React.ReactNode;
  action: string;
  showFallback?: boolean;
}) {
  return (
    <PermissionGate module="staff" action={action} showFallback={showFallback}>
      {children}
    </PermissionGate>
  );
}

export function StudentPermissionGate({ children, action, showFallback = false }: {
  children: React.ReactNode;
  action: string;
  showFallback?: boolean;
}) {
  return (
    <PermissionGate module="students" action={action} showFallback={showFallback}>
      {children}
    </PermissionGate>
  );
}

export function AnnouncementPermissionGate({ children, action, showFallback = false }: {
  children: React.ReactNode;
  action: string;
  showFallback?: boolean;
}) {
  return (
    <PermissionGate module="announcements" action={action} showFallback={showFallback}>
      {children}
    </PermissionGate>
  );
}

export function AcademicPermissionGate({ children, action, showFallback = false }: {
  children: React.ReactNode;
  action: string;
  showFallback?: boolean;
}) {
  return (
    <PermissionGate module="academic_records" action={action} showFallback={showFallback}>
      {children}
    </PermissionGate>
  );
}

export function AdminOnlyGate({ children, showFallback = false }: {
  children: React.ReactNode;
  showFallback?: boolean;
}) {
  return (
    <PermissionGate roles={['admin', 'super', 'school_admin']} showFallback={showFallback}>
      {children}
    </PermissionGate>
  );
}

export function SuperAdminOnlyGate({ children, showFallback = false }: {
  children: React.ReactNode;
  showFallback?: boolean;
}) {
  return (
    <PermissionGate roles={['super']} showFallback={showFallback}>
      {children}
    </PermissionGate>
  );
}
