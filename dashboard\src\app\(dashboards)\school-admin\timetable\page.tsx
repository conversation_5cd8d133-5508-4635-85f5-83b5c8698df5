"use client";

import { Clock4, Calendar, Users, BookOpen, Filter, Plus, Edit } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import { Suspense, useEffect, useState } from "react";

import CircularLoader from "@/components/widgets/CircularLoader";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import { motion } from "framer-motion";
import {
  getOrganizedTimetable,
  getTimetable,
  getTimetableStats,
  createScheduleEntry,
  TimetableData,
  Period,
  ScheduleEntry
} from "@/app/services/TimetableServices";
import { getClassesBySchool } from "@/app/services/ClassServices";
import { getSubjectsBySchoolId } from "@/app/services/SubjectServices";
import { getTeachersBySchool } from "@/app/services/TeacherServices";
import TimetableModal from "@/components/modals/TimetableModal";
import { useToast, ToastContainer } from "@/components/ui/Toast";



const navigation = {
  icon: Clock4,
  baseHref: "/school-admin/timetable",
  title: "Time Table"
};

const DAYS = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];

export default function TimetablePage() {
  const { logout, user } = useAuth();
  const { toasts, removeToast, showSuccess, showError } = useToast();

  // State management
  const [timetableData, setTimetableData] = useState<TimetableData>({});
  const [allClassesTimetable, setAllClassesTimetable] = useState<{[className: string]: TimetableData}>({});
  const [periods, setPeriods] = useState<Period[]>([]);
  const [loadingData, setLoadingData] = useState(true);
  const [selectedClass, setSelectedClass] = useState('all_classes');
  const [selectedWeek, setSelectedWeek] = useState('current');

  // Modal states
  const [isTimetableModalOpen, setIsTimetableModalOpen] = useState(false);
  const [scheduleToEdit, setScheduleToEdit] = useState<any | null>(null);

  // Additional data for forms
  const [classes, setClasses] = useState<any[]>([]);
  const [subjects, setSubjects] = useState<any[]>([]);
  const [teachers, setTeachers] = useState<any[]>([]);

  // Loading states
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get school ID from user
  const schoolId = user?.school_ids?.[0] || user?.school_id;

  // Fetch timetable data from API
  useEffect(() => {
    const fetchTimetableData = async () => {
      if (!schoolId) {
        showError("Error", "No school ID found");
        setLoadingData(false);
        return;
      }

      try {
        setLoadingData(true);

        if (selectedClass === 'all_classes') {
          // Fetch timetables for all classes
          const allClassesResponse = await getOrganizedTimetable(schoolId as string, {});
          console.log("All Classes Timetable Data:", allClassesResponse.timetable);

          // Group timetable data by class
          const classGroupedTimetables: {[className: string]: TimetableData} = {};

          // Get all schedule entries and group by class
          const scheduleResponse = await getTimetable(schoolId as string, {});
          const scheduleEntries = scheduleResponse.schedule_records;

          // Group entries by class name
          scheduleEntries.forEach((entry: ScheduleEntry) => {
            if (!classGroupedTimetables[entry.class_name]) {
              classGroupedTimetables[entry.class_name] = {};
              DAYS.forEach(day => {
                classGroupedTimetables[entry.class_name][day] = {};
              });
            }

            if (!classGroupedTimetables[entry.class_name][entry.day_of_week]) {
              classGroupedTimetables[entry.class_name][entry.day_of_week] = {};
            }

            classGroupedTimetables[entry.class_name][entry.day_of_week][entry.period_number] = entry;
          });

          setAllClassesTimetable(classGroupedTimetables);
          setTimetableData(allClassesResponse.timetable);
          setPeriods(allClassesResponse.periods);
        } else {
          // Build filters for specific class
          const filters: any = {};
          if (selectedClass !== 'all_classes') filters.class_id = selectedClass;

          // Fetch organized timetable for specific class
          const response = await getOrganizedTimetable(schoolId as string, filters);
          console.log("Timetable Data:", response.timetable);
          setTimetableData(response.timetable);
          setPeriods(response.periods);
        }
      } catch (error) {
        console.error("Error fetching timetable data:", error);
        showError("Error", "Failed to load timetable data");
      } finally {
        setLoadingData(false);
      }
    };

    fetchTimetableData();
  }, [schoolId, selectedClass]);

  // Fetch additional data for modals
  useEffect(() => {
    const fetchAdditionalData = async () => {
      if (!schoolId) return;
      console.log("Fetching additional data for school ID:", schoolId);
      try {
        // Fetch data with individual error handling
        const results = await Promise.allSettled([
          getClassesBySchool(schoolId as string),
          getSubjectsBySchoolId(schoolId as string),
          getTeachersBySchool(schoolId as string)
        ]);

        // Handle each result individually
        if (results[0].status === 'fulfilled') {
          setClasses(results[0].value);
        } else {
          console.error("Failed to fetch classes:", results[0].reason);
          setClasses([]);
        }

        if (results[1].status === 'fulfilled') {
          setSubjects(results[1].value);
        } else {
          console.error("Failed to fetch subjects:", results[1].reason);
          setSubjects([]);
        }

        if (results[2].status === 'fulfilled') {
          setTeachers(results[2].value);
        } else {
          console.error("Failed to fetch teachers:", results[2].reason);
          setTeachers([]);
        }

        // Show warning if any critical data failed to load
        const anyDataFailed = results.some(result => result.status === 'rejected');
        if (anyDataFailed) {
          showError("Warning", "Some form data could not be loaded. Some features may be limited.");
        }
      } catch (error) {
        console.error("Error fetching additional data:", error);
        showError("Error", "Failed to load form data");
      }
    };

    fetchAdditionalData();
  }, [schoolId]); // Removed showError from dependencies

  // CRUD Functions
  const handleCreateSchedule = () => {
    setScheduleToEdit(null);
    setIsTimetableModalOpen(true);
  };

  const handleCellClick = (day: string, periodNumber: number) => {
    // Check if there's already a schedule entry for this slot
    const existingEntry = timetableData[day]?.[periodNumber];
    if (existingEntry) {
      // Edit existing entry
      setScheduleToEdit(existingEntry);
    } else {
      // Create new entry with pre-filled day and period
      const period = periods.find(p => p.period_number === periodNumber);
      setScheduleToEdit({
        day_of_week: day,
        period_id: period?._id || ""
      });
    }
    setIsTimetableModalOpen(true);
  };

  // Modal submission function
  const handleScheduleSubmit = async (data: any) => {
    if (!schoolId) {
      showError("Error", "No school ID found");
      return;
    }

    setIsSubmitting(true);
    try {
      // Create new schedule entry (we don't have update/delete for now)
      await createScheduleEntry(schoolId as string, data);

      // Refresh timetable
      const filters: any = {};
      if (selectedClass !== 'all') filters.class_id = selectedClass;

      const response = await getOrganizedTimetable(schoolId as string, filters);
      setTimetableData(response.timetable);
      setPeriods(response.periods);
      setIsTimetableModalOpen(false);
      setScheduleToEdit(null);

      // Show success notification
      showSuccess("Schedule Created", "Schedule entry has been created successfully.");
    } catch (error) {
      console.error("Error submitting schedule:", error);
      showError("Error", "Failed to save schedule. Please try again.");
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  const getSubjectColor = (subject: string) => {
    const colors = {
      Mathematics: 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300',
      Physics: 'bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900/30 dark:text-purple-300',
      Chemistry: 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300',
      Biology: 'bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/30 dark:text-orange-300',
      English: 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300',
      History: 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-300'
    };
    return colors[subject as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300';
  };



  if (loadingData) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <CircularLoader size={32} color="teal" />
      </div>
    );
  }

  return (
    <ProtectedRoute allowedRoles={["school_admin", "admin", "super"]}>
      <SchoolLayout navigation={navigation} onLogout={logout}>
        <div className="space-y-6">
          {/* Header */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-teal rounded-full flex items-center justify-center">
                  <Clock4 className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-foreground">Time Table Management</h1>
                  <p className="text-foreground/60">
                    Create and manage class schedules and time tables
                  </p>
                </div>
              </div>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600"
              >
                <Plus size={16} />
                <span>Add Schedule</span>
              </motion.button>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex flex-wrap items-center gap-4">
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-foreground/60" />
                <span className="text-sm font-medium text-foreground">View:</span>
              </div>
              
              <div className="flex items-center space-x-2">
                <label className="text-sm text-foreground/70">Class:</label>
                <select
                  value={selectedClass}
                  onChange={(e) => setSelectedClass(e.target.value)}
                  className="px-3 py-2 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground"
                >
                  <option value="all_classes">All Classes</option>
                  {classes.map((classItem) => (
                    <option key={classItem._id} value={classItem._id}>
                      {classItem.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex items-center space-x-2">
                <label className="text-sm text-foreground/70">Week:</label>
                <select
                  value={selectedWeek}
                  onChange={(e) => setSelectedWeek(e.target.value)}
                  className="px-3 py-2 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground"
                >
                  <option value="current">Current Week</option>
                  <option value="next">Next Week</option>
                  <option value="custom">Custom Range</option>
                </select>
              </div>
            </div>
          </div>

          {/* Timetable Grid */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-foreground">
                Weekly Schedule - {selectedClass === 'all_classes' ? 'All Classes' : classes.find(c => c._id === selectedClass)?.name || 'Unknown Class'}
              </h2>

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleCreateSchedule}
                className="flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600"
              >
                <Plus size={16} />
                <span>Add Schedule</span>
              </motion.button>
            </div>

            {selectedClass === 'all_classes' ? (
              // All Classes View
              <div className="space-y-8">
                {Object.entries(allClassesTimetable).map(([className, classTimetable]) => (
                  <div key={className} className="border border-stroke rounded-lg p-4">
                    <h3 className="text-lg font-semibold text-foreground mb-4 bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
                      {className}
                    </h3>
                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse">
                        <thead>
                          <tr>
                            <th className="border border-stroke p-2 bg-gray-50 dark:bg-gray-800 text-left font-semibold text-foreground text-sm">
                              Period
                            </th>
                            {DAYS.map(day => (
                              <th key={day} className="border border-stroke p-2 bg-gray-50 dark:bg-gray-800 text-center font-semibold text-foreground min-w-[140px] text-sm">
                                {day}
                              </th>
                            ))}
                          </tr>
                        </thead>
                        <tbody>
                          {periods.map(period => (
                            <tr key={period.period_number}>
                              <td className="border border-stroke p-2 bg-gray-50 dark:bg-gray-800 font-medium text-foreground text-sm">
                                <div className="text-center">
                                  <div className="font-semibold">P{period.period_number}</div>
                                  <div className="text-xs text-foreground/60">
                                    {period.start_time.slice(0, 5)}-{period.end_time.slice(0, 5)}
                                  </div>
                                </div>
                              </td>
                              {DAYS.map(day => {
                                const scheduleEntry = classTimetable[day]?.[period.period_number];
                                return (
                                  <td
                                    key={`${className}-${day}-${period.period_number}`}
                                    className="border border-stroke p-1"
                                  >
                                    {scheduleEntry ? (
                                      <div className={`p-2 rounded border ${getSubjectColor(scheduleEntry.subject_name)} text-xs`}>
                                        <div className="font-semibold mb-1">
                                          {scheduleEntry.subject_name}
                                        </div>
                                        <div className="opacity-80">
                                          {scheduleEntry.teacher_name}
                                        </div>
                                      </div>
                                    ) : (
                                      <div className="p-2 text-center text-foreground/30 text-xs">
                                        Free
                                      </div>
                                    )}
                                  </td>
                                );
                              })}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              // Single Class View
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr>
                      <th className="border border-stroke p-3 bg-gray-50 dark:bg-gray-800 text-left font-semibold text-foreground">
                        Period / Day
                      </th>
                      {DAYS.map(day => (
                        <th key={day} className="border border-stroke p-3 bg-gray-50 dark:bg-gray-800 text-center font-semibold text-foreground min-w-[180px]">
                          {day}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {periods.map(period => (
                      <tr key={period.period_number}>
                        <td className="border border-stroke p-3 bg-gray-50 dark:bg-gray-800 font-medium text-foreground">
                          <div className="text-center">
                            <div className="font-semibold">Period {period.period_number}</div>
                            <div className="text-xs text-foreground/60">
                              {period.start_time.slice(0, 5)} - {period.end_time.slice(0, 5)}
                            </div>
                          </div>
                        </td>
                        {DAYS.map(day => {
                          const scheduleEntry = timetableData[day]?.[period.period_number];
                          return (
                            <td
                              key={`${day}-${period.period_number}`}
                              className="border border-stroke p-2 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                              onClick={() => handleCellClick(day, period.period_number)}
                            >
                              {scheduleEntry ? (
                                <div className={`p-3 rounded-lg border-2 ${getSubjectColor(scheduleEntry.subject_name)} relative group`}>
                                  <div className="font-semibold text-sm mb-1">
                                    {scheduleEntry.subject_name}
                                  </div>
                                  <div className="text-xs opacity-80 mb-1">
                                    {scheduleEntry.teacher_name}
                                  </div>
                                  <div className="text-xs opacity-70">
                                    {scheduleEntry.class_name}
                                  </div>

                                  {/* Edit button on hover */}
                                  <button className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity p-1 bg-white dark:bg-gray-800 rounded shadow-sm">
                                    <Edit className="h-3 w-3 text-foreground/60" />
                                  </button>
                                </div>
                              ) : (
                                <div className="p-3 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 text-center text-foreground/40 hover:border-teal hover:text-teal transition-colors">
                                  <Plus className="h-4 w-4 mx-auto mb-1" />
                                  <div className="text-xs">Add Class</div>
                                </div>
                              )}
                            </td>
                          );
                        })}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Total Classes</p>
                  <p className="text-2xl font-bold text-foreground">
                    {Object.values(timetableData).reduce((total, day) => 
                      total + Object.values(day).filter(entry => entry !== null).length, 0
                    )}
                  </p>
                </div>
                <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                  <BookOpen className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Free Periods</p>
                  <p className="text-2xl font-bold text-foreground">
                    {DAYS.length * periods.length - Object.values(timetableData).reduce((total, day) =>
                      total + Object.values(day).filter(entry => entry !== null).length, 0
                    )}
                  </p>
                </div>
                <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                  <Calendar className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Teachers</p>
                  <p className="text-2xl font-bold text-foreground">
                    {new Set(
                      Object.values(timetableData)
                        .flatMap(day => Object.values(day))
                        .filter(entry => entry !== null)
                        .map(entry => entry!.teacher_name)
                    ).size}
                  </p>
                </div>
                <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                  <Users className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Subjects</p>
                  <p className="text-2xl font-bold text-foreground">
                    {new Set(
                      Object.values(timetableData)
                        .flatMap(day => Object.values(day))
                        .filter(entry => entry !== null)
                        .map(entry => entry!.subject_name)
                    ).size}
                  </p>
                </div>
                <div className="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center">
                  <BookOpen className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Timetable Modal */}
        <TimetableModal
          isOpen={isTimetableModalOpen}
          onClose={() => {
            setIsTimetableModalOpen(false);
            setScheduleToEdit(null);
          }}
          onSubmit={handleScheduleSubmit}
          schedule={scheduleToEdit}
          classes={classes}
          subjects={subjects}
          teachers={teachers}
          periods={periods}
          loading={isSubmitting}
          preSelectedClass={selectedClass !== 'all_classes' ? selectedClass : undefined}
          isClassLocked={selectedClass !== 'all_classes'}
        />

        {/* Toast Notifications */}
        <ToastContainer toasts={toasts} onClose={removeToast} />
      </SchoolLayout>
    </ProtectedRoute>
  );
}
