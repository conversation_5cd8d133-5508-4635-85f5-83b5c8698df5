export interface DisciplineSchema extends Record<string, unknown> {
  _id: string;
  discipline_id: string;     // Unique identifier for the discipline entry
  school_id?: string;        // Optional MongoDB ObjectId as string (reference to School)
  student_id: string;        // MongoDB ObjectId as string (reference to Student)
  comments?: string;         // Optional comments about the discipline
  createdAt?: string;        // Auto-generated timestamp
  updatedAt?: string;        // Auto-generated timestamp
}

export interface DisciplineCreateSchema extends Record<string, unknown> {
  discipline_id: string;     // Required
  student_id: string;        // Required
  school_id?: string;        // Optional
  comments?: string;         // Optional
}

export interface DisciplineUpdateSchema extends Record<string, unknown> {
  _id: string;               // Required to identify the record
  discipline_id?: string;
  student_id?: string;
  school_id?: string;
  comments?: string;
}

export interface DisciplineDeleteSchema extends Record<string, unknown> {
  _id: string;               // Required MongoDB ID to delete
}
