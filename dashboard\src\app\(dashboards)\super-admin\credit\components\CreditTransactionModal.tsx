"use client";

import React, { useState, useEffect } from "react";
import { X, ChevronDown } from "lucide-react";
import SubmissionFeedback from "@/components/widgets/SubmissionFeedback";
import CircularLoader from "@/components/widgets/CircularLoader";
import { motion } from "framer-motion";
import { CreditTransactionSchema } from "@/app/models/CreditTransactionModel";
import { AcademicYearSchema } from "@/app/models/AcademicYear";
import { SchoolSchema } from "@/app/models/SchoolModel";

interface CreditTransactionModalProps {
  onClose: () => void;
  onSave: (data: CreditTransactionSchema) => Promise<void>;
  submitStatus: "success" | "failure" | null;
  isSubmitting: boolean;
  schools: SchoolSchema[];
  academicYears: AcademicYearSchema[];
}

const paymentMethods = ["manual", "automatic"];
const COST_PER_CREDIT = 500; // adjust as needed

const CreditTransactionModal: React.FC<CreditTransactionModalProps> = ({
  onClose,
  onSave,
  submitStatus,
  isSubmitting,
  schools,
  academicYears,
}) => {
  const [formData, setFormData] = useState<CreditTransactionSchema>({
    _id: "",
    school_id: "",
    academicYear_id: "",
    payment_method: "manual",
    amountPaid: 0,
    credit: 0,
    paidAt: "", // will be auto-generated on backend, not displayed
  });

  const [showPaymentMethodDropdown, setShowPaymentMethodDropdown] = useState(false);

  // Update amountPaid whenever credit changes
  useEffect(() => {
    setFormData((prev) => ({
      ...prev,
      amountPaid: prev.credit * COST_PER_CREDIT,
    }));
  }, [formData.credit]);

  const togglePaymentMethodDropdown = () =>
    setShowPaymentMethodDropdown((v) => !v);

  const selectPaymentMethod = (method: "manual" | "automatic") => {
    setFormData((prev) => ({ ...prev, payment_method: method }));
    setShowPaymentMethodDropdown(false);
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: name === "credit" ? Number(value) : value,
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (
      !formData.school_id ||
      !formData.academicYear_id ||
      !formData.payment_method ||
      formData.amountPaid === 0 ||
      formData.credit === 0
    ) {
      alert("Please fill in all required fields.");
      return;
    }

    onSave(formData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-lg p-6 relative">
        <button
          onClick={onClose}
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-700"
          aria-label="Close modal"
        >
          <X size={20} />
        </button>

        <h2 className="text-xl font-semibold mb-4 text-foreground">
          New Credit Transaction
        </h2>

        {submitStatus ? (
          <SubmissionFeedback
            status={submitStatus}
            message={
              submitStatus === "success"
                ? "Transaction saved successfully!"
                : "Error saving transaction. Please try again."
            }
          />
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* School select */}
            <div>
              <label htmlFor="school_id" className="block font-semibold mb-1">
                School <span className="text-red-600">*</span>
              </label>
              <select
                id="school_id"
                name="school_id"
                value={formData.school_id}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border rounded-md dark:bg-gray-700 dark:text-white"
              >
                <option value="">Select school</option>
                {schools?.length > 0 &&
                  schools.map((school) => (
                    <option key={school._id} value={school._id}>
                      {school.name}
                    </option>
                  ))}
              </select>
            </div>

            {/* Academic Year select */}
            <div>
              <label htmlFor="academicYear_id" className="block font-semibold mb-1">
                Academic Year <span className="text-red-600">*</span>
              </label>
              <select
                id="academicYear_id"
                name="academicYear_id"
                value={formData.academicYear_id}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border rounded-md dark:bg-gray-700 dark:text-white"
              >
                <option value="">Select academic year</option>
                {academicYears.map((ay) => (
                  <option key={ay._id ?? ay.academic_year} value={ay._id ?? ay.academic_year}>
                    {ay.academic_year}
                  </option>
                ))}
              </select>
            </div>

            {/* Payment Method - read only */}
            <div>
              <label htmlFor="payment_method" className="block font-semibold mb-1">
                Payment Method <span className="text-red-600">*</span>
              </label>
              <input
                id="payment_method"
                name="payment_method"
                type="text"
                value="Manual"
                readOnly
                className="w-full px-3 py-2 border rounded-md bg-gray-100 dark:bg-gray-700 dark:text-white cursor-not-allowed"
              />
            </div>

            {/* Credit input */}
            <div>
              <label htmlFor="credit" className="block font-semibold mb-1">
                Credit <span className="text-red-600">*</span>
              </label>
              <input
                id="credit"
                name="credit"
                type="number"
                min={0}
                step={1}
                value={formData.credit}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border rounded-md dark:bg-gray-700 dark:text-white"
              />
            </div>

            {/* amountPaid - calculated and read-only */}
            <div>
              <label htmlFor="amountPaid" className="block font-semibold mb-1">
                Amount Paid (XAF)
              </label>
              <input
                id="amountPaid"
                name="amountPaid"
                type="text"
                value={new Intl.NumberFormat().format(formData.amountPaid)}
                readOnly
                className="w-full px-3 py-2 border rounded-md bg-gray-100 dark:bg-gray-700 dark:text-white cursor-not-allowed"
              />
            </div>

            <div className="flex justify-end space-x-2 mt-6">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 300 }}
                type="button"
                onClick={onClose}
                className="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500"
                disabled={isSubmitting}
              >
                Cancel
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 300 }}
                type="submit"
                className="px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 flex items-center gap-2"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <CircularLoader size={18} color="teal-500" />
                    Sending...
                  </>
                ) : (
                  "Send Credit"
                )}
              </motion.button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default CreditTransactionModal;
