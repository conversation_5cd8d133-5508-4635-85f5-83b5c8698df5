"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5e8eea740c8f\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJEOlxcUHJvamV0XFxzY2hvbGFyaWZ5XFxkYXNoYm9hcmRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjVlOGVlYTc0MGM4ZlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/services/AuthContext.tsx":
/*!******************************************!*\
  !*** ./src/app/services/AuthContext.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthContext: () => (/* binding */ AuthContext),\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   BASE_API_URL: () => (/* binding */ BASE_API_URL)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! js-cookie */ \"(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _UserServices__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./UserServices */ \"(app-pages-browser)/./src/app/services/UserServices.tsx\");\n/* harmony import */ var _utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/httpInterceptor */ \"(app-pages-browser)/./src/app/utils/httpInterceptor.tsx\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ BASE_API_URL,AuthContext,AuthProvider auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst BASE_API_URL = process.env.BASE_API_URL || \"https://scolarify.onrender.com/api\";\n// Create a context for authentication\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)(undefined);\n// composant AuthProvider qui fournit le contexte d'authentification\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [redirectAfterLogin, setRedirectAfterLogin] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [authCheckInterval, setAuthCheckInterval] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Fonction pour forcer la déconnexion\n    const forceLogout = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"AuthProvider.useCallback[forceLogout]\": ()=>{\n            console.warn(\"Force logout triggered\");\n            setUser(null);\n            (0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__.clearSession)();\n            if (authCheckInterval) {\n                clearInterval(authCheckInterval);\n                setAuthCheckInterval(null);\n            }\n            if (true) {\n                window.location.href = '/login';\n            }\n        }\n    }[\"AuthProvider.useCallback[forceLogout]\"], [\n        authCheckInterval\n    ]);\n    // Fonction pour vérifier le statut d'authentification\n    const checkAuthStatus = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"AuthProvider.useCallback[checkAuthStatus]\": async ()=>{\n            try {\n                // Vérifier si le token existe\n                const token = js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(\"idToken\");\n                if (!token) {\n                    forceLogout();\n                    return;\n                }\n                // Vérifier si le token est expiré\n                if ((0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__.isTokenExpired)()) {\n                    console.warn(\"Token expired, logging out\");\n                    forceLogout();\n                    return;\n                }\n                // Vérifier avec le serveur\n                const currentUser = await (0,_UserServices__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)();\n                if (!currentUser) {\n                    forceLogout();\n                    return;\n                }\n                setUser(currentUser);\n            } catch (error) {\n                console.error(\"Auth check failed:\", error);\n                forceLogout();\n            }\n        }\n    }[\"AuthProvider.useCallback[checkAuthStatus]\"], [\n        forceLogout\n    ]);\n    // vérifier si un utilisateur est déja connecté\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const checkUserLoggedIn = {\n                \"AuthProvider.useEffect.checkUserLoggedIn\": async ()=>{\n                    try {\n                        // Vérifier d'abord si le token existe et n'est pas expiré\n                        const token = js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(\"idToken\");\n                        if (!token || (0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__.isTokenExpired)()) {\n                            setUser(null);\n                            setLoading(false);\n                            return;\n                        }\n                        const user = await (0,_UserServices__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)();\n                        if (user) {\n                            setUser(user);\n                            // Démarrer la vérification périodique\n                            const interval = setInterval(checkAuthStatus, 60000); // Vérifier toutes les minutes\n                            setAuthCheckInterval(interval);\n                        } else {\n                            js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"idToken\");\n                            setUser(null);\n                        }\n                    } catch (error) {\n                        console.error(\"Error verifying token:\", error);\n                        js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"idToken\");\n                        setUser(null);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.checkUserLoggedIn\"];\n            checkUserLoggedIn();\n            // Cleanup interval on unmount\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    if (authCheckInterval) {\n                        clearInterval(authCheckInterval);\n                    }\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        checkAuthStatus,\n        authCheckInterval\n    ]);\n    const login = async (email, password, rememberMe, redirectUrl)=>{\n        try {\n            const response = await fetch(\"\".concat(BASE_API_URL, \"/auth/login\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email: email,\n                    password: password\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                console.error(\"Login error:\", data.message || \"Unknown error\");\n                throw new Error(data.message || \"Login failed\");\n            }\n            const { idToken } = data;\n            if (!idToken) {\n                throw new Error(\"No idToken received\");\n            }\n            // Stocker le token dans les cookies\n            js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].set(\"idToken\", idToken, {\n                expires: rememberMe ? 30 : 7\n            }); // Expire dans 7 jours\n            const user = await (0,_UserServices__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)(); // Vérifier si l'utilisateur est connecté à nouveau après la connexion réussie\n            if (user) {\n                setUser(user);\n            }\n            // Si une URL de redirection est fournie, stocke-la\n            if (redirectUrl) {\n                setRedirectAfterLogin(redirectUrl);\n            }\n        } catch (error) {\n            console.error(\"Login failed:\", error);\n            throw error;\n        }\n    };\n    const logout = async ()=>{\n        setUser(null);\n        js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"idToken\"); // Supprimer le token des cookies\n        setRedirectAfterLogin(null); // Réinitialiser l'URL de redirection\n        return Promise.resolve();\n    };\n    const isAuthentiacted = !!user; // Vérifier si l'utilisateur est authentifié\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            isAuthenticated: isAuthentiacted,\n            loading,\n            setRedirectAfterLogin,\n            redirectAfterLogin,\n            login,\n            logout,\n            checkAuthStatus,\n            forceLogout\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\services\\\\AuthContext.tsx\",\n        lineNumber: 184,\n        columnNumber: 9\n    }, undefined);\n};\n_s(AuthProvider, \"IXehwLkee0KKjuVl2ztGE/oHnEQ=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/services/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/services/UserServices.tsx":
/*!*******************************************!*\
  !*** ./src/app/services/UserServices.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   deleteAllUsers: () => (/* binding */ deleteAllUsers),\n/* harmony export */   deleteMultipleUsers: () => (/* binding */ deleteMultipleUsers),\n/* harmony export */   deleteUser: () => (/* binding */ deleteUser),\n/* harmony export */   forget_password: () => (/* binding */ forget_password),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getMonthlyUserStarts: () => (/* binding */ getMonthlyUserStarts),\n/* harmony export */   getParents: () => (/* binding */ getParents),\n/* harmony export */   getTokenFromCookie: () => (/* binding */ getTokenFromCookie),\n/* harmony export */   getTotalUsers: () => (/* binding */ getTotalUsers),\n/* harmony export */   getUserById: () => (/* binding */ getUserById),\n/* harmony export */   getUserBy_id: () => (/* binding */ getUserBy_id),\n/* harmony export */   getUserCountWithChange: () => (/* binding */ getUserCountWithChange),\n/* harmony export */   getUsers: () => (/* binding */ getUsers),\n/* harmony export */   handleUserSearch: () => (/* binding */ handleUserSearch),\n/* harmony export */   registerParent: () => (/* binding */ registerParent),\n/* harmony export */   resend_Code: () => (/* binding */ resend_Code),\n/* harmony export */   resetParentPasswordService: () => (/* binding */ resetParentPasswordService),\n/* harmony export */   reset_password: () => (/* binding */ reset_password),\n/* harmony export */   updateUser: () => (/* binding */ updateUser),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verify_otp: () => (/* binding */ verify_otp)\n/* harmony export */ });\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var jwt_decode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jwt-decode */ \"(app-pages-browser)/./node_modules/jwt-decode/build/esm/index.js\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"(app-pages-browser)/./src/app/services/AuthContext.tsx\");\n/* harmony import */ var _utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/httpInterceptor */ \"(app-pages-browser)/./src/app/utils/httpInterceptor.tsx\");\n\n\n\n\nfunction getTokenFromCookie(name) {\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(name);\n    return token;\n}\nasync function getCurrentUser() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        if (!token) {\n            return null;\n        }\n        const decodedUser = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_1__.jwtDecode)(token);\n        const email = decodedUser.email;\n        const response = await (0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_3__.authenticatedGet)(\"/user/get-user-email/\".concat(email));\n        if (!response.ok) {\n            console.error(\"Error fetching user:\", response.statusText);\n            return null;\n        }\n        const user = await response.json();\n        return user;\n    } catch (error) {\n        console.error(\"Error fetching current user:\", error);\n        return null;\n    }\n}\nasync function getParents() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/get-parents\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching parents:\", response.statusText);\n            throw new Error(\"Failed to fetch parents data\");\n        }\n        const parentsList = await response.json();\n        const parents = parentsList.map((user)=>({\n                _id: user._id,\n                user_id: user.user_id,\n                firebaseUid: user.firebaseUid,\n                name: user.name,\n                email: user.email,\n                phone: user.phone,\n                role: user.role,\n                avatar: user.avatar,\n                address: user.address,\n                school_ids: user.school_ids,\n                student_ids: user.student_ids,\n                isVerified: user.isVerified,\n                verificationCode: user.verificationCode,\n                verificationCodeExpires: user.verificationCodeExpires,\n                lastLogin: user.lastLogin,\n                createdAt: user.createdAt,\n                updatedAt: user.updatedAt\n            }));\n        return parents;\n    } catch (error) {\n        console.error(\"Error fetching parents:\", error);\n        throw new Error(\"Failed to fetch parents data\");\n    }\n}\nasync function getUsers() {\n    try {\n        const token = getTokenFromCookie(\"idToken\"); // Assuming this function gets the token from cookies\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/get-users\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching users:\", response.statusText);\n            throw new Error(\"Failed to fetch users data\");\n        }\n        const usersList = await response.json();\n        const users = usersList.map((user)=>{\n            return {\n                _id: user._id,\n                user_id: user.user_id,\n                firebaseUid: user.firebaseUid,\n                name: user.name,\n                email: user.email,\n                phone: user.phone,\n                role: user.role,\n                avatar: user.avatar,\n                address: user.address,\n                school_ids: user.school_ids,\n                isVerified: user.isVerified,\n                verificationCode: user.verificationCode,\n                verificationCodeExpires: user.verificationCodeExpires,\n                lastLogin: user.lastLogin,\n                createdAt: user.createdAt,\n                updatedAt: user.updatedAt\n            };\n        });\n        return users;\n    } catch (error) {\n        console.error(\"Error fetching users:\", error);\n        throw new Error(\"Failed to fetch users data\");\n    }\n}\nasync function createUser(userData) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/register-user\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n            },\n            body: JSON.stringify(userData)\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to create user data\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                // If parsing the error body fails, use default message\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            console.error(\"Error creating user:\", errorMessage);\n            throw new Error(errorMessage);\n        }\n        const data = await response.json(); // Parse the response as JSON\n        return data; // Return the response data (usually the created user object)\n    } catch (error) {\n        console.error(\"Error creating user:\", error);\n        throw new Error(error instanceof Error ? error.message : \"Failed to create user data\");\n    }\n}\nasync function updateUser(user_id, userData) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/update-user/\").concat(user_id), {\n            method: \"PUT\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n            },\n            body: JSON.stringify(userData)\n        });\n        if (!response.ok) {\n            console.error(\"Error updating user:\", response.statusText);\n            throw new Error(\"Failed to update user data\");\n        }\n        const data = await response.json(); // Parse the response as JSON\n        return data; // Return the updated user data (this could be the user object with updated fields)\n    } catch (error) {\n        console.error(\"Error updating user:\", error);\n        throw new Error(\"Failed to update user data\");\n    }\n}\nasync function getUserById(userId) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/get-user/\").concat(userId), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching user:\", response.statusText);\n        throw new Error(\"Failed to fetch user data\");\n    }\n    const data = await response.json();\n    const user = {\n        _id: data._id,\n        user_id: data.user_id,\n        name: data.name,\n        email: data.email,\n        phone: data.phone,\n        role: data.role,\n        address: data.address,\n        school_ids: data.school_ids,\n        isVerified: data.isVerified,\n        description: data.description,\n        firebaseUid: data.firebaseUid,\n        createdAt: data.createdAt,\n        updatedAt: data.updatedAt\n    };\n    return user;\n}\nasync function verifyPassword(password, email) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/auth/verify-password\"), {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n            email: email,\n            password: password\n        })\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching user:\", response.statusText);\n        return false;\n    }\n    return true;\n}\nasync function deleteUser(user_id) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/delete-user/\").concat(user_id), {\n            method: \"DELETE\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n            }\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to delete user\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            console.error(\"Error deleting user:\", errorMessage);\n            throw new Error(errorMessage);\n        }\n        const result = await response.json();\n        return result; // Might return a success message or deleted user data\n    } catch (error) {\n        console.error(\"Error deleting user:\", error);\n        throw new Error(error instanceof Error ? error.message : \"Failed to delete user\");\n    }\n}\nasync function getUserBy_id(_id) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/get-user-by-id/\").concat(_id), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching user:\", response.statusText);\n        throw new Error(\"Failed to fetch user data\");\n    }\n    const data = await response.json();\n    const user = {\n        _id: data._id,\n        user_id: data.user_id,\n        name: data.name,\n        email: data.email,\n        phone: data.phone,\n        role: data.role,\n        address: data.address,\n        school_ids: data.school_ids,\n        isVerified: data.isVerified,\n        description: data.description,\n        firebaseUid: data.firebaseUid,\n        createdAt: data.createdAt,\n        updatedAt: data.updatedAt\n    };\n    return user;\n}\nasync function forget_password(email) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/auth/forgot-password\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email: email\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to send reset password email: check your email\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error sending reset password email:\", error);\n        throw new Error(\"Failed to send reset password email\");\n    }\n}\nasync function verify_otp(code, email) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/auth/verify-code\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                code: code,\n                email: email\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to verify OTP\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error verifying OTP:\", error);\n        throw new Error(\"Failed to verify OTP\");\n    }\n}\nasync function resend_Code(email) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/auth/resend-code\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email: email\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to resend code\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error resending code:\", error);\n        throw new Error(\"Failed to resend code\");\n    }\n}\nasync function reset_password(newPassword, email, code) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/auth/reset-password\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email: email,\n                code: code,\n                newPassword: newPassword\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to reset password\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error resetting password:\", error);\n        throw new Error(\"Failed to reset password\");\n    }\n}\nasync function handleUserSearch(query) {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/search-users?query=\").concat(encodeURIComponent(query)), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error searching users:\", response.statusText);\n            throw new Error(\"Failed to search users\");\n        }\n        const results = await response.json();\n        const users = results.map((user)=>{\n            return {\n                _id: user._id,\n                user_id: user.user_id,\n                firebaseUid: user.firebaseUid,\n                name: user.name,\n                email: user.email,\n                phone: user.phone,\n                role: user.role,\n                avatar: user.avatar,\n                address: user.address,\n                school_ids: user.school_ids,\n                isVerified: user.isVerified,\n                verificationCode: user.verificationCode,\n                verificationCodeExpires: user.verificationCodeExpires,\n                lastLogin: user.lastLogin,\n                createdAt: user.createdAt,\n                updatedAt: user.updatedAt\n            };\n        });\n        return users;\n    } catch (error) {\n        console.error(\"Error searching users:\", error);\n        throw new Error(\"Failed to search users\");\n    }\n}\nasync function registerParent(parentData) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/register-parent\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n            },\n            body: JSON.stringify(parentData)\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to register parent\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            console.error(\"Error registering parent:\", errorMessage);\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data; // This includes user object and generatedPassword (if new)\n    } catch (error) {\n        console.error(\"Error in registerParent service:\", error);\n        throw new Error(error instanceof Error ? error.message : \"Failed to register parent\");\n    }\n}\nasync function deleteMultipleUsers(userIds) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/delete-users\"), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n        },\n        body: JSON.stringify({\n            ids: userIds\n        })\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting multiple users:\", response.statusText);\n        throw new Error(\"Failed to delete multiple users\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function deleteAllUsers() {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/delete-all-users\"), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting all users:\", response.statusText);\n        throw new Error(\"Failed to delete all users\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function getTotalUsers() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/total-users\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching total users:\", response.statusText);\n            throw new Error(\"Failed to fetch total users\");\n        }\n        const data = await response.json();\n        return data.totalUsers;\n    } catch (error) {\n        console.error(\"Error fetching total users:\", error);\n        throw error;\n    }\n}\nasync function getUserCountWithChange() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/users-count-change\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching user count change:\", response.statusText);\n            throw new Error(\"Failed to fetch user count change\");\n        }\n        const data = await response.json();\n        return {\n            totalUsersThisMonth: data.totalUsersThisMonth,\n            percentageChange: data.percentageChange\n        };\n    } catch (error) {\n        console.error(\"Error fetching user count change:\", error);\n        throw error;\n    }\n}\nasync function getMonthlyUserStarts() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/monthly-user-starts\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Failed to fetch monthly user stats:\", response.statusText);\n            throw new Error(\"Failed to fetch monthly user stats\");\n        }\n        const data = await response.json();\n        // data has type { [year: string]: MonthlyUserStat[] }\n        return data;\n    } catch (error) {\n        console.error(\"Error fetching monthly user stats:\", error);\n        throw error;\n    }\n}\nasync function resetParentPasswordService(param) {\n    let { email, phone } = param;\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"idToken\");\n    if (!token) {\n        throw new Error(\"You must be logged in to perform this action.\");\n    }\n    if (!email && !phone) {\n        throw new Error(\"Email or phone number is required to reset password.\");\n    }\n    const payload = {};\n    if (email) payload.email = email;\n    if (phone) payload.phone = phone;\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/reset-parent-password\"), {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        },\n        body: JSON.stringify(payload)\n    });\n    if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || \"Failed to reset password\");\n    }\n    const result = await response.json();\n    return result;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/services/UserServices.tsx\n"));

/***/ })

});