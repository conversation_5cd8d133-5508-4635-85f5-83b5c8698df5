"use client";

import { ChartNoAxesGantt, Plus, Clock, Edit, Trash2 } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import { Suspense, useEffect, useState } from "react";
import DataTableFix from "@/components/utils/TableFix";
import { useRouter } from "next/navigation";
import CircularLoader from "@/components/widgets/CircularLoader";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import { motion } from "framer-motion";
import {
  getPeriods,
  createPeriod,
  updatePeriod,
  deletePeriod,
  deleteMultiplePeriods,
  deleteAllPeriods,
  Period
} from "@/app/services/PeriodServices";
import PeriodModal from "@/components/modals/PeriodModal";
import DeleteConfirmationModal from "@/components/modals/DeleteConfirmationModal";
import { useToast, ToastContainer } from "@/components/ui/Toast";
import { PeriodSchema } from "@/app/models/Period";



const navigation = {
  icon: ChartNoAxesGantt,
  baseHref: "/school-admin/period",
  title: "Periods"
};

export default function PeriodsPage() {
  const { logout, user } = useAuth();
  const router = useRouter();
  const { toasts, removeToast, showSuccess, showError } = useToast();

  // State management
  const [periods, setPeriods] = useState<PeriodSchema[]>([]);
  const [loadingData, setLoadingData] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Modal states
  const [isPeriodModalOpen, setIsPeriodModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [periodToEdit, setPeriodToEdit] = useState<PeriodSchema | null>(null);
  const [periodToDelete, setPeriodToDelete] = useState<PeriodSchema | null>(null);
  const [deleteType, setDeleteType] = useState<"single" | "multiple" | "all">("single");
  const [selectedPeriods, setSelectedPeriods] = useState<PeriodSchema[]>([]);

  // Loading states
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [clearSelection, setClearSelection] = useState(false);

  // Get school ID from user
  const schoolId = (user?.school_ids?.[0] || user?.school_id || "") as string;

  // Fetch periods from API
  useEffect(() => {
    const fetchPeriods = async () => {
      if (!schoolId) {
        setError("No school ID found");
        setLoadingData(false);
        return;
      }

      try {
        setLoadingData(true);
        setError(null);
        const response = await getPeriods(schoolId as string);
        setPeriods(response.periods);
      } catch (error) {
        console.error("Error fetching periods:", error);
        setError("Failed to load periods");
      } finally {
        setLoadingData(false);
      }
    };

    fetchPeriods();
  }, [schoolId]);

  // Format time for display
  const formatTime = (timeString: string) => {
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  // Calculate duration
  const calculateDuration = (startTime: string, endTime: string) => {
    const start = new Date(`2000-01-01T${startTime}`);
    const end = new Date(`2000-01-01T${endTime}`);
    const diffMs = end.getTime() - start.getTime();
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins >= 60) {
      const hours = Math.floor(diffMins / 60);
      const remainingMins = diffMins % 60;
      if (remainingMins === 0) {
        return `${hours}h`;
      } else {
        return `${hours}h ${remainingMins}min`;
      }
    } else {
      return `${diffMins}min`;
    }
  };

  // Table columns
  const columns = [
    { 
      header: "Period", 
      accessor: (row: PeriodSchema) => (
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-teal rounded-full flex items-center justify-center text-white font-semibold text-sm">
            {row.period_number}
          </div>
          <span className="font-medium">Period {row.period_number}</span>
        </div>
      )
    },
    { 
      header: "Start Time", 
      accessor: (row: PeriodSchema) => (
        <span className="font-mono text-sm">{formatTime(row.start_time)}</span>
      )
    },
    { 
      header: "End Time", 
      accessor: (row: PeriodSchema) => (
        <span className="font-mono text-sm">{formatTime(row.end_time)}</span>
      )
    },
    { 
      header: "Duration", 
      accessor: (row: PeriodSchema) => (
        <span className="text-sm text-foreground/70">
          {calculateDuration(row.start_time, row.end_time)}
        </span>
      )
    },
    { 
      header: "Time Range", 
      accessor: (row: PeriodSchema) => (
        <div className="flex items-center space-x-2 text-sm">
          <Clock className="h-4 w-4 text-foreground/50" />
          <span>{formatTime(row.start_time)} - {formatTime(row.end_time)}</span>
        </div>
      )
    }
  ];

  // Table actions
  const actions = [
    {
      label: "Edit",
      onClick: (period: PeriodSchema) => {
        setPeriodToEdit(period);
        setIsPeriodModalOpen(true);
      },
    },
    {
      label: "Delete",
      onClick: (period: PeriodSchema) => {
        handleDeletePeriod(period);
      },
    },
  ];

  // CRUD Functions
  const handleCreatePeriod = () => {
    setPeriodToEdit(null);
    setIsPeriodModalOpen(true);
  };

  const handleEditPeriod = (period: PeriodSchema) => {
    setPeriodToEdit(period);
    setIsPeriodModalOpen(true);
  };

  const handleDeletePeriod = (period: PeriodSchema) => {
    setPeriodToDelete(period);
    setDeleteType("single");
    setIsDeleteModalOpen(true);
  };

  const handleDeleteMultiple = (selectedIds: string[]) => {
    setDeleteType("multiple");
    setIsDeleteModalOpen(true);
  };

  const handleDeleteAll = () => {
    setDeleteType("all");
    setIsDeleteModalOpen(true);
  };

  const handleSelectionChange = (selectedRows: PeriodSchema[]) => {
    setSelectedPeriods(selectedRows);
  };

  // Modal submission functions
  const handlePeriodSubmit = async (data: any) => {
    if (!schoolId) {
      setError("No school ID found");
      return;
    }

    setIsSubmitting(true);
    try {
      if (periodToEdit) {
        // Update existing period
        await updatePeriod(schoolId, periodToEdit._id, data);
      } else {
        // Create new period
        await createPeriod(schoolId, data);
      }

      // Refresh periods list
      const response = await getPeriods(schoolId);
      setPeriods(response.periods);
      setIsPeriodModalOpen(false);
      setPeriodToEdit(null);

      // Show success notification
      showSuccess(
        periodToEdit ? "Period Updated" : "Period Created",
        periodToEdit ? "Period has been updated successfully." : "New period has been created successfully."
      );
    } catch (error) {
      console.error("Error submitting period:", error);
      showError("Error", "Failed to save period. Please try again.");
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteConfirm = async () => {
    if (!schoolId) return;

    try {
      if (deleteType === "single" && periodToDelete) {
        await deletePeriod(schoolId, periodToDelete._id);
      } else if (deleteType === "multiple") {
        const selectedIds = selectedPeriods.map(p => p._id);
        await deleteMultiplePeriods(selectedIds);
        setClearSelection(true);
      } else if (deleteType === "all") {
        await deleteAllPeriods();
        setClearSelection(true);
      }

      // Refresh periods list
      const response = await getPeriods(schoolId);
      setPeriods(response.periods);
      setIsDeleteModalOpen(false);
      setPeriodToDelete(null);
      setSelectedPeriods([]);

      // Show success notification
      if (deleteType === "single") {
        showSuccess("Period Deleted", "Period has been deleted successfully.");
      } else if (deleteType === "multiple") {
        showSuccess("Periods Deleted", `${selectedPeriods.length} periods have been deleted successfully.`);
      } else {
        showSuccess("All Periods Deleted", "All periods have been deleted successfully.");
      }
    } catch (error) {
      console.error("Error deleting period(s):", error);
      showError("Error", "Failed to delete period(s). Please try again.");
      throw error;
    }
  };

  if (loadingData) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <CircularLoader size={32} color="teal" />
      </div>
    );
  }

  if (error) {
    return (
      <ProtectedRoute allowedRoles={["school_admin", "admin", "super"]}>
        <SchoolLayout navigation={navigation} onLogout={logout}>
          <div className="min-h-screen bg-background flex items-center justify-center">
            <div className="text-center">
              <div className="text-red-500 text-lg font-semibold mb-2">Error</div>
              <div className="text-foreground/60">{error}</div>
              <button
                onClick={() => window.location.reload()}
                className="mt-4 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600"
              >
                Retry
              </button>
            </div>
          </div>
        </SchoolLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute allowedRoles={["school_admin", "admin", "super"]}>
      <SchoolLayout navigation={navigation} onLogout={logout}>
        <div className="space-y-6">
          {/* Header */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-teal rounded-full flex items-center justify-center">
                  <ChartNoAxesGantt className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-foreground">School Periods</h1>
                  <p className="text-foreground/60">
                    Manage class periods and time slots for your school
                  </p>
                </div>
              </div>
              
              <div className="text-right">
                <p className="text-2xl font-bold text-foreground">{periods.length}</p>
                <p className="text-sm text-foreground/60">Total Periods</p>
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Total Periods</p>
                  <p className="text-2xl font-bold text-foreground">{periods.length}</p>
                </div>
                <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                  <ChartNoAxesGantt className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">First Period</p>
                  <p className="text-lg font-bold text-foreground">
                    {periods.length > 0 ? formatTime(periods[0].start_time) : 'N/A'}
                  </p>
                </div>
                <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                  <Clock className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Last Period</p>
                  <p className="text-lg font-bold text-foreground">
                    {periods.length > 0 ? formatTime(periods[periods.length - 1].end_time) : 'N/A'}
                  </p>
                </div>
                <div className="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center">
                  <Clock className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Avg Duration</p>
                  <p className="text-lg font-bold text-foreground">
                    {periods.length > 0 ? '45 min' : 'N/A'}
                  </p>
                </div>
                <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                  <Clock className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>
          </div>

          {/* Periods Table */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-foreground">All Periods</h2>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: 'spring', stiffness: 300 }}
                onClick={handleCreatePeriod}
                className="flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600"
              >
                <Plus size={16} />
                <span>Add Period</span>
              </motion.button>
            </div>

            <Suspense fallback={<CircularLoader size={24} color="teal" />}>
              <DataTableFix<PeriodSchema>
                data={periods}
                columns={columns}
                actions={actions}
                defaultItemsPerPage={10}
                onSelectionChange={handleSelectionChange}
                handleDeleteMultiple={handleDeleteMultiple}
                handleDeleteAll={handleDeleteAll}
                clearSelection={clearSelection}
                onSelectionCleared={() => setClearSelection(false)}
              />
            </Suspense>
          </div>
        </div>

        {/* Period Modal */}
        <PeriodModal
          isOpen={isPeriodModalOpen}
          onClose={() => {
            setIsPeriodModalOpen(false);
            setPeriodToEdit(null);
          }}
          onSubmit={handlePeriodSubmit}
          period={periodToEdit}
          existingPeriods={periods}
          loading={isSubmitting}
        />

        {/* Delete Confirmation Modal */}
        <DeleteConfirmationModal
          isOpen={isDeleteModalOpen}
          onClose={() => {
            setIsDeleteModalOpen(false);
            setPeriodToDelete(null);
          }}
          onConfirm={handleDeleteConfirm}
          title={
            deleteType === "single"
              ? "Delete Period"
              : deleteType === "multiple"
                ? "Delete Selected Periods"
                : "Delete All Periods"
          }
          message={
            deleteType === "single"
              ? "Are you sure you want to delete this period? This action cannot be undone."
              : deleteType === "multiple"
                ? `Are you sure you want to delete ${selectedPeriods.length} selected periods? This action cannot be undone.`
                : "Are you sure you want to delete ALL periods? This action cannot be undone and will remove all period data."
          }
          itemName={
            deleteType === "single" && periodToDelete
              ? `Period ${periodToDelete.period_number} (${periodToDelete.start_time} - ${periodToDelete.end_time})`
              : undefined
          }
          itemCount={deleteType === "multiple" ? selectedPeriods.length : undefined}
          type={deleteType}
        />

        {/* Toast Notifications */}
        <ToastContainer toasts={toasts} onClose={removeToast} />
      </SchoolLayout>
    </ProtectedRoute>
  );
}
