"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/dashboard/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chevron-left.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ ChevronLeft)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.485.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m15 18-6-6 6-6\",\n            key: \"1wnfg3\"\n        }\n    ]\n];\nconst ChevronLeft = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"chevron-left\", __iconNode);\n //# sourceMappingURL=chevron-left.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hldnJvbi1sZWZ0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUdhLGlCQUF1QjtJQUFDO1FBQUMsTUFBUTtRQUFBLENBQUU7WUFBQSxFQUFHLGlCQUFrQjtZQUFBLEtBQUssQ0FBUztRQUFBLENBQUM7S0FBQztDQUFBO0FBYS9FLGtCQUFjLGtFQUFpQixpQkFBZ0IsQ0FBVSIsInNvdXJjZXMiOlsiRDpcXHNyY1xcaWNvbnNcXGNoZXZyb24tbGVmdC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcbmltcG9ydCB7IEljb25Ob2RlIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3QgX19pY29uTm9kZTogSWNvbk5vZGUgPSBbWydwYXRoJywgeyBkOiAnbTE1IDE4LTYtNiA2LTYnLCBrZXk6ICcxd25mZzMnIH1dXTtcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIENoZXZyb25MZWZ0XG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjR0YwYUNCa1BTSnRNVFVnTVRndE5pMDJJRFl0TmlJZ0x6NEtQQzl6ZG1jK0NnPT0pIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2NoZXZyb24tbGVmdFxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IENoZXZyb25MZWZ0ID0gY3JlYXRlTHVjaWRlSWNvbignY2hldnJvbi1sZWZ0JywgX19pY29uTm9kZSk7XG5cbmV4cG9ydCBkZWZhdWx0IENoZXZyb25MZWZ0O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/recharts/es6/cartesian/Area.js":
/*!*****************************************************!*\
  !*** ./node_modules/recharts/es6/cartesian/Area.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Area: () => (/* binding */ Area)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var react_smooth__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-smooth */ \"(app-pages-browser)/./node_modules/react-smooth/es6/index.js\");\n/* harmony import */ var lodash_isFunction__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/isFunction */ \"(app-pages-browser)/./node_modules/lodash/isFunction.js\");\n/* harmony import */ var lodash_isFunction__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_isFunction__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lodash_max__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash/max */ \"(app-pages-browser)/./node_modules/lodash/max.js\");\n/* harmony import */ var lodash_max__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash_max__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var lodash_isNil__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lodash/isNil */ \"(app-pages-browser)/./node_modules/lodash/isNil.js\");\n/* harmony import */ var lodash_isNil__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(lodash_isNil__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var lodash_isNaN__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/isNaN */ \"(app-pages-browser)/./node_modules/lodash/isNaN.js\");\n/* harmony import */ var lodash_isNaN__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_isNaN__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_isEqual__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/isEqual */ \"(app-pages-browser)/./node_modules/lodash/isEqual.js\");\n/* harmony import */ var lodash_isEqual__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_isEqual__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _shape_Curve__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../shape/Curve */ \"(app-pages-browser)/./node_modules/recharts/es6/shape/Curve.js\");\n/* harmony import */ var _shape_Dot__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../shape/Dot */ \"(app-pages-browser)/./node_modules/recharts/es6/shape/Dot.js\");\n/* harmony import */ var _container_Layer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../container/Layer */ \"(app-pages-browser)/./node_modules/recharts/es6/container/Layer.js\");\n/* harmony import */ var _component_LabelList__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../component/LabelList */ \"(app-pages-browser)/./node_modules/recharts/es6/component/LabelList.js\");\n/* harmony import */ var _util_Global__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../util/Global */ \"(app-pages-browser)/./node_modules/recharts/es6/util/Global.js\");\n/* harmony import */ var _util_DataUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../util/DataUtils */ \"(app-pages-browser)/./node_modules/recharts/es6/util/DataUtils.js\");\n/* harmony import */ var _util_ChartUtils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../util/ChartUtils */ \"(app-pages-browser)/./node_modules/recharts/es6/util/ChartUtils.js\");\n/* harmony import */ var _util_ReactUtils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../util/ReactUtils */ \"(app-pages-browser)/./node_modules/recharts/es6/util/ReactUtils.js\");\nvar _excluded = [\n    \"layout\",\n    \"type\",\n    \"stroke\",\n    \"connectNulls\",\n    \"isRange\",\n    \"ref\"\n], _excluded2 = [\n    \"key\"\n];\nvar _Area;\nfunction _typeof(o) {\n    \"@babel/helpers - typeof\";\n    return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function(o) {\n        return typeof o;\n    } : function(o) {\n        return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n    }, _typeof(o);\n}\nfunction _objectWithoutProperties(source, excluded) {\n    if (source == null) return {};\n    var target = _objectWithoutPropertiesLoose(source, excluded);\n    var key, i;\n    if (Object.getOwnPropertySymbols) {\n        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n        for(i = 0; i < sourceSymbolKeys.length; i++){\n            key = sourceSymbolKeys[i];\n            if (excluded.indexOf(key) >= 0) continue;\n            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    for(var key in source){\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n            if (excluded.indexOf(key) >= 0) continue;\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\nfunction _extends() {\n    _extends = Object.assign ? Object.assign.bind() : function(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source){\n                if (Object.prototype.hasOwnProperty.call(source, key)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            _defineProperty(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\nfunction _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n    }\n}\nfunction _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    Object.defineProperty(Constructor, \"prototype\", {\n        writable: false\n    });\n    return Constructor;\n}\nfunction _callSuper(t, o, e) {\n    return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e));\n}\nfunction _possibleConstructorReturn(self, call) {\n    if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n        return call;\n    } else if (call !== void 0) {\n        throw new TypeError(\"Derived constructors may only return object or undefined\");\n    }\n    return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n    if (self === void 0) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n    }\n    return self;\n}\nfunction _isNativeReflectConstruct() {\n    try {\n        var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {}));\n    } catch (t) {}\n    return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n        return !!t;\n    })();\n}\nfunction _getPrototypeOf(o) {\n    _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n        return o.__proto__ || Object.getPrototypeOf(o);\n    };\n    return _getPrototypeOf(o);\n}\nfunction _inherits(subClass, superClass) {\n    if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function\");\n    }\n    subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n            value: subClass,\n            writable: true,\n            configurable: true\n        }\n    });\n    Object.defineProperty(subClass, \"prototype\", {\n        writable: false\n    });\n    if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n    _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n        o.__proto__ = p;\n        return o;\n    };\n    return _setPrototypeOf(o, p);\n}\nfunction _defineProperty(obj, key, value) {\n    key = _toPropertyKey(key);\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n    if (\"object\" != _typeof(t) || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n        var i = e.call(t, r || \"default\");\n        if (\"object\" != _typeof(i)) return i;\n        throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n}\n/**\n * @fileOverview Area\n */ \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar Area = /*#__PURE__*/ function(_PureComponent) {\n    function Area() {\n        var _this;\n        _classCallCheck(this, Area);\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        _this = _callSuper(this, Area, [].concat(args));\n        _defineProperty(_this, \"state\", {\n            isAnimationFinished: true\n        });\n        _defineProperty(_this, \"id\", (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_7__.uniqueId)('recharts-area-'));\n        _defineProperty(_this, \"handleAnimationEnd\", function() {\n            var onAnimationEnd = _this.props.onAnimationEnd;\n            _this.setState({\n                isAnimationFinished: true\n            });\n            if (lodash_isFunction__WEBPACK_IMPORTED_MODULE_2___default()(onAnimationEnd)) {\n                onAnimationEnd();\n            }\n        });\n        _defineProperty(_this, \"handleAnimationStart\", function() {\n            var onAnimationStart = _this.props.onAnimationStart;\n            _this.setState({\n                isAnimationFinished: false\n            });\n            if (lodash_isFunction__WEBPACK_IMPORTED_MODULE_2___default()(onAnimationStart)) {\n                onAnimationStart();\n            }\n        });\n        return _this;\n    }\n    _inherits(Area, _PureComponent);\n    return _createClass(Area, [\n        {\n            key: \"renderDots\",\n            value: function renderDots(needClip, clipDot, clipPathId) {\n                var isAnimationActive = this.props.isAnimationActive;\n                var isAnimationFinished = this.state.isAnimationFinished;\n                if (isAnimationActive && !isAnimationFinished) {\n                    return null;\n                }\n                var _this$props = this.props, dot = _this$props.dot, points = _this$props.points, dataKey = _this$props.dataKey;\n                var areaProps = (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_8__.filterProps)(this.props, false);\n                var customDotProps = (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_8__.filterProps)(dot, true);\n                var dots = points.map(function(entry, i) {\n                    var dotProps = _objectSpread(_objectSpread(_objectSpread({\n                        key: \"dot-\".concat(i),\n                        r: 3\n                    }, areaProps), customDotProps), {}, {\n                        index: i,\n                        cx: entry.x,\n                        cy: entry.y,\n                        dataKey: dataKey,\n                        value: entry.value,\n                        payload: entry.payload,\n                        points: points\n                    });\n                    return Area.renderDotItem(dot, dotProps);\n                });\n                var dotsProps = {\n                    clipPath: needClip ? \"url(#clipPath-\".concat(clipDot ? '' : 'dots-').concat(clipPathId, \")\") : null\n                };\n                return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_container_Layer__WEBPACK_IMPORTED_MODULE_9__.Layer, _extends({\n                    className: \"recharts-area-dots\"\n                }, dotsProps), dots);\n            }\n        },\n        {\n            key: \"renderHorizontalRect\",\n            value: function renderHorizontalRect(alpha) {\n                var _this$props2 = this.props, baseLine = _this$props2.baseLine, points = _this$props2.points, strokeWidth = _this$props2.strokeWidth;\n                var startX = points[0].x;\n                var endX = points[points.length - 1].x;\n                var width = alpha * Math.abs(startX - endX);\n                var maxY = lodash_max__WEBPACK_IMPORTED_MODULE_3___default()(points.map(function(entry) {\n                    return entry.y || 0;\n                }));\n                if ((0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_7__.isNumber)(baseLine) && typeof baseLine === 'number') {\n                    maxY = Math.max(baseLine, maxY);\n                } else if (baseLine && Array.isArray(baseLine) && baseLine.length) {\n                    maxY = Math.max(lodash_max__WEBPACK_IMPORTED_MODULE_3___default()(baseLine.map(function(entry) {\n                        return entry.y || 0;\n                    })), maxY);\n                }\n                if ((0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_7__.isNumber)(maxY)) {\n                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"rect\", {\n                        x: startX < endX ? startX : startX - width,\n                        y: 0,\n                        width: width,\n                        height: Math.floor(maxY + (strokeWidth ? parseInt(\"\".concat(strokeWidth), 10) : 1))\n                    });\n                }\n                return null;\n            }\n        },\n        {\n            key: \"renderVerticalRect\",\n            value: function renderVerticalRect(alpha) {\n                var _this$props3 = this.props, baseLine = _this$props3.baseLine, points = _this$props3.points, strokeWidth = _this$props3.strokeWidth;\n                var startY = points[0].y;\n                var endY = points[points.length - 1].y;\n                var height = alpha * Math.abs(startY - endY);\n                var maxX = lodash_max__WEBPACK_IMPORTED_MODULE_3___default()(points.map(function(entry) {\n                    return entry.x || 0;\n                }));\n                if ((0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_7__.isNumber)(baseLine) && typeof baseLine === 'number') {\n                    maxX = Math.max(baseLine, maxX);\n                } else if (baseLine && Array.isArray(baseLine) && baseLine.length) {\n                    maxX = Math.max(lodash_max__WEBPACK_IMPORTED_MODULE_3___default()(baseLine.map(function(entry) {\n                        return entry.x || 0;\n                    })), maxX);\n                }\n                if ((0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_7__.isNumber)(maxX)) {\n                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"rect\", {\n                        x: 0,\n                        y: startY < endY ? startY : startY - height,\n                        width: maxX + (strokeWidth ? parseInt(\"\".concat(strokeWidth), 10) : 1),\n                        height: Math.floor(height)\n                    });\n                }\n                return null;\n            }\n        },\n        {\n            key: \"renderClipRect\",\n            value: function renderClipRect(alpha) {\n                var layout = this.props.layout;\n                if (layout === 'vertical') {\n                    return this.renderVerticalRect(alpha);\n                }\n                return this.renderHorizontalRect(alpha);\n            }\n        },\n        {\n            key: \"renderAreaStatically\",\n            value: function renderAreaStatically(points, baseLine, needClip, clipPathId) {\n                var _this$props4 = this.props, layout = _this$props4.layout, type = _this$props4.type, stroke = _this$props4.stroke, connectNulls = _this$props4.connectNulls, isRange = _this$props4.isRange, ref = _this$props4.ref, others = _objectWithoutProperties(_this$props4, _excluded);\n                return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_container_Layer__WEBPACK_IMPORTED_MODULE_9__.Layer, {\n                    clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n                }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_shape_Curve__WEBPACK_IMPORTED_MODULE_10__.Curve, _extends({}, (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_8__.filterProps)(others, true), {\n                    points: points,\n                    connectNulls: connectNulls,\n                    type: type,\n                    baseLine: baseLine,\n                    layout: layout,\n                    stroke: \"none\",\n                    className: \"recharts-area-area\"\n                })), stroke !== 'none' && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_shape_Curve__WEBPACK_IMPORTED_MODULE_10__.Curve, _extends({}, (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_8__.filterProps)(this.props, false), {\n                    className: \"recharts-area-curve\",\n                    layout: layout,\n                    type: type,\n                    connectNulls: connectNulls,\n                    fill: \"none\",\n                    points: points\n                })), stroke !== 'none' && isRange && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_shape_Curve__WEBPACK_IMPORTED_MODULE_10__.Curve, _extends({}, (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_8__.filterProps)(this.props, false), {\n                    className: \"recharts-area-curve\",\n                    layout: layout,\n                    type: type,\n                    connectNulls: connectNulls,\n                    fill: \"none\",\n                    points: baseLine\n                })));\n            }\n        },\n        {\n            key: \"renderAreaWithAnimation\",\n            value: function renderAreaWithAnimation(needClip, clipPathId) {\n                var _this2 = this;\n                var _this$props5 = this.props, points = _this$props5.points, baseLine = _this$props5.baseLine, isAnimationActive = _this$props5.isAnimationActive, animationBegin = _this$props5.animationBegin, animationDuration = _this$props5.animationDuration, animationEasing = _this$props5.animationEasing, animationId = _this$props5.animationId;\n                var _this$state = this.state, prevPoints = _this$state.prevPoints, prevBaseLine = _this$state.prevBaseLine;\n                // const clipPathId = isNil(id) ? this.id : id;\n                return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(react_smooth__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    begin: animationBegin,\n                    duration: animationDuration,\n                    isActive: isAnimationActive,\n                    easing: animationEasing,\n                    from: {\n                        t: 0\n                    },\n                    to: {\n                        t: 1\n                    },\n                    key: \"area-\".concat(animationId),\n                    onAnimationEnd: this.handleAnimationEnd,\n                    onAnimationStart: this.handleAnimationStart\n                }, function(_ref) {\n                    var t = _ref.t;\n                    if (prevPoints) {\n                        var prevPointsDiffFactor = prevPoints.length / points.length;\n                        // update animtaion\n                        var stepPoints = points.map(function(entry, index) {\n                            var prevPointIndex = Math.floor(index * prevPointsDiffFactor);\n                            if (prevPoints[prevPointIndex]) {\n                                var prev = prevPoints[prevPointIndex];\n                                var interpolatorX = (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_7__.interpolateNumber)(prev.x, entry.x);\n                                var interpolatorY = (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_7__.interpolateNumber)(prev.y, entry.y);\n                                return _objectSpread(_objectSpread({}, entry), {}, {\n                                    x: interpolatorX(t),\n                                    y: interpolatorY(t)\n                                });\n                            }\n                            return entry;\n                        });\n                        var stepBaseLine;\n                        if ((0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_7__.isNumber)(baseLine) && typeof baseLine === 'number') {\n                            var interpolator = (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_7__.interpolateNumber)(prevBaseLine, baseLine);\n                            stepBaseLine = interpolator(t);\n                        } else if (lodash_isNil__WEBPACK_IMPORTED_MODULE_4___default()(baseLine) || lodash_isNaN__WEBPACK_IMPORTED_MODULE_5___default()(baseLine)) {\n                            var _interpolator = (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_7__.interpolateNumber)(prevBaseLine, 0);\n                            stepBaseLine = _interpolator(t);\n                        } else {\n                            stepBaseLine = baseLine.map(function(entry, index) {\n                                var prevPointIndex = Math.floor(index * prevPointsDiffFactor);\n                                if (prevBaseLine[prevPointIndex]) {\n                                    var prev = prevBaseLine[prevPointIndex];\n                                    var interpolatorX = (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_7__.interpolateNumber)(prev.x, entry.x);\n                                    var interpolatorY = (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_7__.interpolateNumber)(prev.y, entry.y);\n                                    return _objectSpread(_objectSpread({}, entry), {}, {\n                                        x: interpolatorX(t),\n                                        y: interpolatorY(t)\n                                    });\n                                }\n                                return entry;\n                            });\n                        }\n                        return _this2.renderAreaStatically(stepPoints, stepBaseLine, needClip, clipPathId);\n                    }\n                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_container_Layer__WEBPACK_IMPORTED_MODULE_9__.Layer, null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"defs\", null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"clipPath\", {\n                        id: \"animationClipPath-\".concat(clipPathId)\n                    }, _this2.renderClipRect(t))), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_container_Layer__WEBPACK_IMPORTED_MODULE_9__.Layer, {\n                        clipPath: \"url(#animationClipPath-\".concat(clipPathId, \")\")\n                    }, _this2.renderAreaStatically(points, baseLine, needClip, clipPathId)));\n                });\n            }\n        },\n        {\n            key: \"renderArea\",\n            value: function renderArea(needClip, clipPathId) {\n                var _this$props6 = this.props, points = _this$props6.points, baseLine = _this$props6.baseLine, isAnimationActive = _this$props6.isAnimationActive;\n                var _this$state2 = this.state, prevPoints = _this$state2.prevPoints, prevBaseLine = _this$state2.prevBaseLine, totalLength = _this$state2.totalLength;\n                if (isAnimationActive && points && points.length && (!prevPoints && totalLength > 0 || !lodash_isEqual__WEBPACK_IMPORTED_MODULE_6___default()(prevPoints, points) || !lodash_isEqual__WEBPACK_IMPORTED_MODULE_6___default()(prevBaseLine, baseLine))) {\n                    return this.renderAreaWithAnimation(needClip, clipPathId);\n                }\n                return this.renderAreaStatically(points, baseLine, needClip, clipPathId);\n            }\n        },\n        {\n            key: \"render\",\n            value: function render() {\n                var _filterProps;\n                var _this$props7 = this.props, hide = _this$props7.hide, dot = _this$props7.dot, points = _this$props7.points, className = _this$props7.className, top = _this$props7.top, left = _this$props7.left, xAxis = _this$props7.xAxis, yAxis = _this$props7.yAxis, width = _this$props7.width, height = _this$props7.height, isAnimationActive = _this$props7.isAnimationActive, id = _this$props7.id;\n                if (hide || !points || !points.length) {\n                    return null;\n                }\n                var isAnimationFinished = this.state.isAnimationFinished;\n                var hasSinglePoint = points.length === 1;\n                var layerClass = (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])('recharts-area', className);\n                var needClipX = xAxis && xAxis.allowDataOverflow;\n                var needClipY = yAxis && yAxis.allowDataOverflow;\n                var needClip = needClipX || needClipY;\n                var clipPathId = lodash_isNil__WEBPACK_IMPORTED_MODULE_4___default()(id) ? this.id : id;\n                var _ref2 = (_filterProps = (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_8__.filterProps)(dot, false)) !== null && _filterProps !== void 0 ? _filterProps : {\n                    r: 3,\n                    strokeWidth: 2\n                }, _ref2$r = _ref2.r, r = _ref2$r === void 0 ? 3 : _ref2$r, _ref2$strokeWidth = _ref2.strokeWidth, strokeWidth = _ref2$strokeWidth === void 0 ? 2 : _ref2$strokeWidth;\n                var _ref3 = (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_8__.hasClipDot)(dot) ? dot : {}, _ref3$clipDot = _ref3.clipDot, clipDot = _ref3$clipDot === void 0 ? true : _ref3$clipDot;\n                var dotSize = r * 2 + strokeWidth;\n                return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_container_Layer__WEBPACK_IMPORTED_MODULE_9__.Layer, {\n                    className: layerClass\n                }, needClipX || needClipY ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"defs\", null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"clipPath\", {\n                    id: \"clipPath-\".concat(clipPathId)\n                }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"rect\", {\n                    x: needClipX ? left : left - width / 2,\n                    y: needClipY ? top : top - height / 2,\n                    width: needClipX ? width : width * 2,\n                    height: needClipY ? height : height * 2\n                })), !clipDot && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"clipPath\", {\n                    id: \"clipPath-dots-\".concat(clipPathId)\n                }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"rect\", {\n                    x: left - dotSize / 2,\n                    y: top - dotSize / 2,\n                    width: width + dotSize,\n                    height: height + dotSize\n                }))) : null, !hasSinglePoint ? this.renderArea(needClip, clipPathId) : null, (dot || hasSinglePoint) && this.renderDots(needClip, clipDot, clipPathId), (!isAnimationActive || isAnimationFinished) && _component_LabelList__WEBPACK_IMPORTED_MODULE_12__.LabelList.renderCallByParent(this.props, points));\n            }\n        }\n    ], [\n        {\n            key: \"getDerivedStateFromProps\",\n            value: function getDerivedStateFromProps(nextProps, prevState) {\n                if (nextProps.animationId !== prevState.prevAnimationId) {\n                    return {\n                        prevAnimationId: nextProps.animationId,\n                        curPoints: nextProps.points,\n                        curBaseLine: nextProps.baseLine,\n                        prevPoints: prevState.curPoints,\n                        prevBaseLine: prevState.curBaseLine\n                    };\n                }\n                if (nextProps.points !== prevState.curPoints || nextProps.baseLine !== prevState.curBaseLine) {\n                    return {\n                        curPoints: nextProps.points,\n                        curBaseLine: nextProps.baseLine\n                    };\n                }\n                return null;\n            }\n        }\n    ]);\n}(react__WEBPACK_IMPORTED_MODULE_0__.PureComponent);\n_Area = Area;\n_defineProperty(Area, \"displayName\", 'Area');\n_defineProperty(Area, \"defaultProps\", {\n    stroke: '#3182bd',\n    fill: '#3182bd',\n    fillOpacity: 0.6,\n    xAxisId: 0,\n    yAxisId: 0,\n    legendType: 'line',\n    connectNulls: false,\n    // points of area\n    points: [],\n    dot: false,\n    activeDot: true,\n    hide: false,\n    isAnimationActive: !_util_Global__WEBPACK_IMPORTED_MODULE_13__.Global.isSsr,\n    animationBegin: 0,\n    animationDuration: 1500,\n    animationEasing: 'ease'\n});\n_defineProperty(Area, \"getBaseValue\", function(props, item, xAxis, yAxis) {\n    var layout = props.layout, chartBaseValue = props.baseValue;\n    var itemBaseValue = item.props.baseValue;\n    // The baseValue can be defined both on the AreaChart as well as on the Area.\n    // The value for the item takes precedence.\n    var baseValue = itemBaseValue !== null && itemBaseValue !== void 0 ? itemBaseValue : chartBaseValue;\n    if ((0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_7__.isNumber)(baseValue) && typeof baseValue === 'number') {\n        return baseValue;\n    }\n    var numericAxis = layout === 'horizontal' ? yAxis : xAxis;\n    var domain = numericAxis.scale.domain();\n    if (numericAxis.type === 'number') {\n        var domainMax = Math.max(domain[0], domain[1]);\n        var domainMin = Math.min(domain[0], domain[1]);\n        if (baseValue === 'dataMin') {\n            return domainMin;\n        }\n        if (baseValue === 'dataMax') {\n            return domainMax;\n        }\n        return domainMax < 0 ? domainMax : Math.max(Math.min(domain[0], domain[1]), 0);\n    }\n    if (baseValue === 'dataMin') {\n        return domain[0];\n    }\n    if (baseValue === 'dataMax') {\n        return domain[1];\n    }\n    return domain[0];\n});\n_defineProperty(Area, \"getComposedData\", function(_ref4) {\n    var props = _ref4.props, item = _ref4.item, xAxis = _ref4.xAxis, yAxis = _ref4.yAxis, xAxisTicks = _ref4.xAxisTicks, yAxisTicks = _ref4.yAxisTicks, bandSize = _ref4.bandSize, dataKey = _ref4.dataKey, stackedData = _ref4.stackedData, dataStartIndex = _ref4.dataStartIndex, displayedData = _ref4.displayedData, offset = _ref4.offset;\n    var layout = props.layout;\n    var hasStack = stackedData && stackedData.length;\n    var baseValue = _Area.getBaseValue(props, item, xAxis, yAxis);\n    var isHorizontalLayout = layout === 'horizontal';\n    var isRange = false;\n    var points = displayedData.map(function(entry, index) {\n        var value;\n        if (hasStack) {\n            value = stackedData[dataStartIndex + index];\n        } else {\n            value = (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_14__.getValueByDataKey)(entry, dataKey);\n            if (!Array.isArray(value)) {\n                value = [\n                    baseValue,\n                    value\n                ];\n            } else {\n                isRange = true;\n            }\n        }\n        var isBreakPoint = value[1] == null || hasStack && (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_14__.getValueByDataKey)(entry, dataKey) == null;\n        if (isHorizontalLayout) {\n            return {\n                x: (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_14__.getCateCoordinateOfLine)({\n                    axis: xAxis,\n                    ticks: xAxisTicks,\n                    bandSize: bandSize,\n                    entry: entry,\n                    index: index\n                }),\n                y: isBreakPoint ? null : yAxis.scale(value[1]),\n                value: value,\n                payload: entry\n            };\n        }\n        return {\n            x: isBreakPoint ? null : xAxis.scale(value[1]),\n            y: (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_14__.getCateCoordinateOfLine)({\n                axis: yAxis,\n                ticks: yAxisTicks,\n                bandSize: bandSize,\n                entry: entry,\n                index: index\n            }),\n            value: value,\n            payload: entry\n        };\n    });\n    var baseLine;\n    if (hasStack || isRange) {\n        baseLine = points.map(function(entry) {\n            var x = Array.isArray(entry.value) ? entry.value[0] : null;\n            if (isHorizontalLayout) {\n                return {\n                    x: entry.x,\n                    y: x != null && entry.y != null ? yAxis.scale(x) : null\n                };\n            }\n            return {\n                x: x != null ? xAxis.scale(x) : null,\n                y: entry.y\n            };\n        });\n    } else {\n        baseLine = isHorizontalLayout ? yAxis.scale(baseValue) : xAxis.scale(baseValue);\n    }\n    return _objectSpread({\n        points: points,\n        baseLine: baseLine,\n        layout: layout,\n        isRange: isRange\n    }, offset);\n});\n_defineProperty(Area, \"renderDotItem\", function(option, props) {\n    var dotItem;\n    if (/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().isValidElement(option)) {\n        dotItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().cloneElement(option, props);\n    } else if (lodash_isFunction__WEBPACK_IMPORTED_MODULE_2___default()(option)) {\n        dotItem = option(props);\n    } else {\n        var className = (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])('recharts-area-dot', typeof option !== 'boolean' ? option.className : '');\n        var key = props.key, rest = _objectWithoutProperties(props, _excluded2);\n        dotItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_shape_Dot__WEBPACK_IMPORTED_MODULE_15__.Dot, _extends({}, rest, {\n            key: key,\n            className: className\n        }));\n    }\n    return dotItem;\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/recharts/es6/cartesian/Area.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/recharts/es6/chart/AreaChart.js":
/*!******************************************************!*\
  !*** ./node_modules/recharts/es6/chart/AreaChart.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AreaChart: () => (/* binding */ AreaChart)\n/* harmony export */ });\n/* harmony import */ var _generateCategoricalChart__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./generateCategoricalChart */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/generateCategoricalChart.js\");\n/* harmony import */ var _cartesian_Area__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../cartesian/Area */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Area.js\");\n/* harmony import */ var _cartesian_XAxis__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../cartesian/XAxis */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _cartesian_YAxis__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../cartesian/YAxis */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _util_CartesianUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../util/CartesianUtils */ \"(app-pages-browser)/./node_modules/recharts/es6/util/CartesianUtils.js\");\n/**\n * @fileOverview Area Chart\n */ \n\n\n\n\nvar AreaChart = (0,_generateCategoricalChart__WEBPACK_IMPORTED_MODULE_0__.generateCategoricalChart)({\n    chartName: 'AreaChart',\n    GraphicalChild: _cartesian_Area__WEBPACK_IMPORTED_MODULE_1__.Area,\n    axisComponents: [\n        {\n            axisType: 'xAxis',\n            AxisComp: _cartesian_XAxis__WEBPACK_IMPORTED_MODULE_2__.XAxis\n        },\n        {\n            axisType: 'yAxis',\n            AxisComp: _cartesian_YAxis__WEBPACK_IMPORTED_MODULE_3__.YAxis\n        }\n    ],\n    formatAxisMap: _util_CartesianUtils__WEBPACK_IMPORTED_MODULE_4__.formatAxisMap\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWNoYXJ0cy9lczYvY2hhcnQvQXJlYUNoYXJ0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFBOztDQUVDLEdBQ3FFO0FBQzdCO0FBQ0U7QUFDQTtBQUNZO0FBQ2hELElBQUlLLFlBQVlMLG1GQUF3QkEsQ0FBQztJQUM5Q00sV0FBVztJQUNYQyxnQkFBZ0JOLGlEQUFJQTtJQUNwQk8sZ0JBQWdCO1FBQUM7WUFDZkMsVUFBVTtZQUNWQyxVQUFVUixtREFBS0E7UUFDakI7UUFBRztZQUNETyxVQUFVO1lBQ1ZDLFVBQVVQLG1EQUFLQTtRQUNqQjtLQUFFO0lBQ0ZDLGVBQWVBLCtEQUFhQTtBQUM5QixHQUFHIiwic291cmNlcyI6WyJEOlxcUHJvamV0XFxzY2hvbGFyaWZ5XFxkYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xccmVjaGFydHNcXGVzNlxcY2hhcnRcXEFyZWFDaGFydC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBmaWxlT3ZlcnZpZXcgQXJlYSBDaGFydFxuICovXG5pbXBvcnQgeyBnZW5lcmF0ZUNhdGVnb3JpY2FsQ2hhcnQgfSBmcm9tICcuL2dlbmVyYXRlQ2F0ZWdvcmljYWxDaGFydCc7XG5pbXBvcnQgeyBBcmVhIH0gZnJvbSAnLi4vY2FydGVzaWFuL0FyZWEnO1xuaW1wb3J0IHsgWEF4aXMgfSBmcm9tICcuLi9jYXJ0ZXNpYW4vWEF4aXMnO1xuaW1wb3J0IHsgWUF4aXMgfSBmcm9tICcuLi9jYXJ0ZXNpYW4vWUF4aXMnO1xuaW1wb3J0IHsgZm9ybWF0QXhpc01hcCB9IGZyb20gJy4uL3V0aWwvQ2FydGVzaWFuVXRpbHMnO1xuZXhwb3J0IHZhciBBcmVhQ2hhcnQgPSBnZW5lcmF0ZUNhdGVnb3JpY2FsQ2hhcnQoe1xuICBjaGFydE5hbWU6ICdBcmVhQ2hhcnQnLFxuICBHcmFwaGljYWxDaGlsZDogQXJlYSxcbiAgYXhpc0NvbXBvbmVudHM6IFt7XG4gICAgYXhpc1R5cGU6ICd4QXhpcycsXG4gICAgQXhpc0NvbXA6IFhBeGlzXG4gIH0sIHtcbiAgICBheGlzVHlwZTogJ3lBeGlzJyxcbiAgICBBeGlzQ29tcDogWUF4aXNcbiAgfV0sXG4gIGZvcm1hdEF4aXNNYXA6IGZvcm1hdEF4aXNNYXBcbn0pOyJdLCJuYW1lcyI6WyJnZW5lcmF0ZUNhdGVnb3JpY2FsQ2hhcnQiLCJBcmVhIiwiWEF4aXMiLCJZQXhpcyIsImZvcm1hdEF4aXNNYXAiLCJBcmVhQ2hhcnQiLCJjaGFydE5hbWUiLCJHcmFwaGljYWxDaGlsZCIsImF4aXNDb21wb25lbnRzIiwiYXhpc1R5cGUiLCJBeGlzQ29tcCJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/recharts/es6/chart/AreaChart.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/(dashboards)/school-admin/dashboard/page.tsx":
/*!**************************************************************!*\
  !*** ./src/app/(dashboards)/school-admin/dashboard/page.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Page)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_GraduationCap_LayoutDashboard_School_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,GraduationCap,LayoutDashboard,School,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_GraduationCap_LayoutDashboard_School_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,GraduationCap,LayoutDashboard,School,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_GraduationCap_LayoutDashboard_School_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,GraduationCap,LayoutDashboard,School,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_GraduationCap_LayoutDashboard_School_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,GraduationCap,LayoutDashboard,School,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_GraduationCap_LayoutDashboard_School_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,GraduationCap,LayoutDashboard,School,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/school.js\");\n/* harmony import */ var _components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Dashboard/Layouts/SchoolLayout */ \"(app-pages-browser)/./src/components/Dashboard/Layouts/SchoolLayout.tsx\");\n/* harmony import */ var _components_widgets_StatsOverview__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/widgets/StatsOverview */ \"(app-pages-browser)/./src/components/widgets/StatsOverview.tsx\");\n/* harmony import */ var _components_utils_AChart__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/utils/AChart */ \"(app-pages-browser)/./src/components/utils/AChart.tsx\");\n/* harmony import */ var _components_utils_PerformanceTable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/utils/PerformanceTable */ \"(app-pages-browser)/./src/components/utils/PerformanceTable.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/widgets/CircularLoader */ \"(app-pages-browser)/./src/components/widgets/CircularLoader.tsx\");\n/* harmony import */ var _app_services_StudentServices__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/services/StudentServices */ \"(app-pages-browser)/./src/app/services/StudentServices.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst BASE_URL = \"/school-admin\";\nconst navigation = {\n    icon: _barrel_optimize_names_DollarSign_GraduationCap_LayoutDashboard_School_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n    baseHref: \"\".concat(BASE_URL, \"/dashboard\"),\n    title: \"Dashboard\"\n};\n// Données de performance statiques pour simuler les données des classes\nconst performanceData = [\n    {\n        id: \"CLS001\",\n        schoolName: \"Class 6A\",\n        metrics: {\n            \"Number of Students\": 32,\n            \"Average Grade\": 85\n        }\n    },\n    {\n        id: \"CLS002\",\n        schoolName: \"Class 7B\",\n        metrics: {\n            \"Number of Students\": 28,\n            \"Average Grade\": 88\n        }\n    },\n    {\n        id: \"CLS003\",\n        schoolName: \"Class 8C\",\n        metrics: {\n            \"Number of Students\": 30,\n            \"Average Grade\": 82\n        }\n    },\n    {\n        id: \"CLS004\",\n        schoolName: \"Class 9A\",\n        metrics: {\n            \"Number of Students\": 25,\n            \"Average Grade\": 90\n        }\n    },\n    {\n        id: \"CLS005\",\n        schoolName: \"Class 10B\",\n        metrics: {\n            \"Number of Students\": 27,\n            \"Average Grade\": 78\n        }\n    }\n];\n// Liste des métriques disponibles\nconst metricOptions = [\n    \"Number of Students\",\n    \"Average Grade\"\n];\nfunction Page() {\n    _s();\n    const { logout, user } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(true);\n    // Simuler le chargement des données des étudiants pour l'école de l'administrateur\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)({\n        \"Page.useEffect\": ()=>{\n            const fetchStudents = {\n                \"Page.useEffect.fetchStudents\": async ()=>{\n                    try {\n                        if (user && user.school_ids && user.school_ids.length > 0) {\n                            // Utiliser le premier ID d'école associé à l'administrateur\n                            const schoolId = user.school_ids[0];\n                            const studentsData = await (0,_app_services_StudentServices__WEBPACK_IMPORTED_MODULE_8__.getStudentsBySchool)(schoolId);\n                            setStudents(studentsData);\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching students:\", error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"Page.useEffect.fetchStudents\"];\n            if (user) {\n                fetchStudents();\n            }\n        }\n    }[\"Page.useEffect\"], [\n        user\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            navigation: navigation,\n            showGoPro: true,\n            onLogout: ()=>logout(),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    size: 32,\n                    color: \"teal\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\dashboard\\\\page.tsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\dashboard\\\\page.tsx\",\n            lineNumber: 98,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        navigation: navigation,\n        showGoPro: true,\n        onLogout: ()=>logout(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_StatsOverview__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            value: \"3,245\",\n                            changePercentage: 5.78,\n                            title: \"Total Revenue (USD)\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_GraduationCap_LayoutDashboard_School_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 98\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_StatsOverview__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            value: \"142\",\n                            changePercentage: 2.35,\n                            title: \"Total Teachers\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_GraduationCap_LayoutDashboard_School_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 91\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_StatsOverview__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            value: students.length.toString(),\n                            changePercentage: 3.48,\n                            title: \"Total Students\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_GraduationCap_LayoutDashboard_School_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 114\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_StatsOverview__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            value: \"32\",\n                            changePercentage: 1.25,\n                            title: \"Total Classes\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_GraduationCap_LayoutDashboard_School_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 89\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 xl:grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-lg border border-stroke p-4 h-max\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_AChart__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_PerformanceTable__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            data: performanceData,\n                            defaultItemsPerPage: 5,\n                            metricOptions: metricOptions\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\dashboard\\\\page.tsx\",\n            lineNumber: 116,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\dashboard\\\\page.tsx\",\n        lineNumber: 111,\n        columnNumber: 5\n    }, this);\n}\n_s(Page, \"bxoA3LozA1O16X8mwfPoI8jcKkk=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c = Page;\nvar _c;\n$RefreshReg$(_c, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboards)/school-admin/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/services/AuthContext.tsx":
/*!******************************************!*\
  !*** ./src/app/services/AuthContext.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthContext: () => (/* binding */ AuthContext),\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   BASE_API_URL: () => (/* binding */ BASE_API_URL)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! js-cookie */ \"(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _UserServices__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./UserServices */ \"(app-pages-browser)/./src/app/services/UserServices.tsx\");\n/* harmony import */ var _utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/httpInterceptor */ \"(app-pages-browser)/./src/app/utils/httpInterceptor.tsx\");\n/* __next_internal_client_entry_do_not_use__ BASE_API_URL,AuthContext,AuthProvider auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst BASE_API_URL = \"https://scolarify.onrender.com/api\";\n// Create a context for authentication\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)(undefined);\n// composant AuthProvider qui fournit le contexte d'authentification\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [redirectAfterLogin, setRedirectAfterLogin] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [authCheckInterval, setAuthCheckInterval] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Fonction pour forcer la déconnexion\n    const forceLogout = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"AuthProvider.useCallback[forceLogout]\": ()=>{\n            console.warn(\"Force logout triggered\");\n            setUser(null);\n            (0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__.clearSession)();\n            if (authCheckInterval) {\n                clearInterval(authCheckInterval);\n                setAuthCheckInterval(null);\n            }\n            if (true) {\n                window.location.href = '/login';\n            }\n        }\n    }[\"AuthProvider.useCallback[forceLogout]\"], [\n        authCheckInterval\n    ]);\n    // Fonction pour vérifier le statut d'authentification\n    const checkAuthStatus = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"AuthProvider.useCallback[checkAuthStatus]\": async ()=>{\n            try {\n                // Vérifier si le token existe\n                const token = js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(\"idToken\");\n                if (!token) {\n                    forceLogout();\n                    return;\n                }\n                // Vérifier si le token est expiré\n                if ((0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__.isTokenExpired)()) {\n                    console.warn(\"Token expired, logging out\");\n                    forceLogout();\n                    return;\n                }\n                // Vérifier avec le serveur\n                const currentUser = await (0,_UserServices__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)();\n                if (!currentUser) {\n                    forceLogout();\n                    return;\n                }\n                setUser(currentUser);\n            } catch (error) {\n                console.error(\"Auth check failed:\", error);\n                forceLogout();\n            }\n        }\n    }[\"AuthProvider.useCallback[checkAuthStatus]\"], [\n        forceLogout\n    ]);\n    // vérifier si un utilisateur est déja connecté\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const checkUserLoggedIn = {\n                \"AuthProvider.useEffect.checkUserLoggedIn\": async ()=>{\n                    try {\n                        // Vérifier d'abord si le token existe et n'est pas expiré\n                        const token = js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(\"idToken\");\n                        if (!token || (0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__.isTokenExpired)()) {\n                            setUser(null);\n                            setLoading(false);\n                            return;\n                        }\n                        const user = await (0,_UserServices__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)();\n                        if (user) {\n                            setUser(user);\n                            // Démarrer la vérification périodique\n                            const interval = setInterval(checkAuthStatus, 60000); // Vérifier toutes les minutes\n                            setAuthCheckInterval(interval);\n                        } else {\n                            js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"idToken\");\n                            setUser(null);\n                        }\n                    } catch (error) {\n                        console.error(\"Error verifying token:\", error);\n                        js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"idToken\");\n                        setUser(null);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.checkUserLoggedIn\"];\n            checkUserLoggedIn();\n            // Cleanup interval on unmount\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    if (authCheckInterval) {\n                        clearInterval(authCheckInterval);\n                    }\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        checkAuthStatus,\n        authCheckInterval\n    ]);\n    const login = async (email, password, rememberMe, redirectUrl)=>{\n        try {\n            const response = await fetch(\"\".concat(BASE_API_URL, \"/auth/login\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email: email,\n                    password: password\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                console.error(\"Login error:\", data.message || \"Unknown error\");\n                throw new Error(data.message || \"Login failed\");\n            }\n            const { idToken } = data;\n            if (!idToken) {\n                throw new Error(\"No idToken received\");\n            }\n            // Stocker le token dans les cookies\n            js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].set(\"idToken\", idToken, {\n                expires: rememberMe ? 30 : 7\n            }); // Expire dans 7 jours\n            const user = await (0,_UserServices__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)(); // Vérifier si l'utilisateur est connecté à nouveau après la connexion réussie\n            if (user) {\n                setUser(user);\n            }\n            // Si une URL de redirection est fournie, stocke-la\n            if (redirectUrl) {\n                setRedirectAfterLogin(redirectUrl);\n            }\n        } catch (error) {\n            console.error(\"Login failed:\", error);\n            throw error;\n        }\n    };\n    const logout = async ()=>{\n        setUser(null);\n        js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"idToken\"); // Supprimer le token des cookies\n        setRedirectAfterLogin(null); // Réinitialiser l'URL de redirection\n        return Promise.resolve();\n    };\n    const isAuthentiacted = !!user; // Vérifier si l'utilisateur est authentifié\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            isAuthenticated: isAuthentiacted,\n            loading,\n            setRedirectAfterLogin,\n            redirectAfterLogin,\n            login,\n            logout,\n            checkAuthStatus,\n            forceLogout\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\services\\\\AuthContext.tsx\",\n        lineNumber: 184,\n        columnNumber: 9\n    }, undefined);\n};\n_s(AuthProvider, \"IXehwLkee0KKjuVl2ztGE/oHnEQ=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/services/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/services/UserServices.tsx":
/*!*******************************************!*\
  !*** ./src/app/services/UserServices.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   deleteAllUsers: () => (/* binding */ deleteAllUsers),\n/* harmony export */   deleteMultipleUsers: () => (/* binding */ deleteMultipleUsers),\n/* harmony export */   deleteUser: () => (/* binding */ deleteUser),\n/* harmony export */   forget_password: () => (/* binding */ forget_password),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getMonthlyUserStarts: () => (/* binding */ getMonthlyUserStarts),\n/* harmony export */   getTokenFromCookie: () => (/* binding */ getTokenFromCookie),\n/* harmony export */   getTotalUsers: () => (/* binding */ getTotalUsers),\n/* harmony export */   getUserById: () => (/* binding */ getUserById),\n/* harmony export */   getUserBy_id: () => (/* binding */ getUserBy_id),\n/* harmony export */   getUserCountWithChange: () => (/* binding */ getUserCountWithChange),\n/* harmony export */   getUsers: () => (/* binding */ getUsers),\n/* harmony export */   handleUserSearch: () => (/* binding */ handleUserSearch),\n/* harmony export */   registerParent: () => (/* binding */ registerParent),\n/* harmony export */   resend_Code: () => (/* binding */ resend_Code),\n/* harmony export */   reset_password: () => (/* binding */ reset_password),\n/* harmony export */   updateUser: () => (/* binding */ updateUser),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verify_otp: () => (/* binding */ verify_otp)\n/* harmony export */ });\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var jwt_decode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jwt-decode */ \"(app-pages-browser)/./node_modules/jwt-decode/build/esm/index.js\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"(app-pages-browser)/./src/app/services/AuthContext.tsx\");\n/* harmony import */ var _utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/httpInterceptor */ \"(app-pages-browser)/./src/app/utils/httpInterceptor.tsx\");\n\n\n\n\nfunction getTokenFromCookie(name) {\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(name);\n    return token;\n}\nasync function getCurrentUser() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        if (!token) {\n            return null;\n        }\n        const decodedUser = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_1__.jwtDecode)(token);\n        const email = decodedUser.email;\n        const response = await (0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_3__.authenticatedGet)(\"/user/get-user-email/\".concat(email));\n        if (!response.ok) {\n            console.error(\"Error fetching user:\", response.statusText);\n            return null;\n        }\n        const user = await response.json();\n        return user;\n    } catch (error) {\n        console.error(\"Error fetching current user:\", error);\n        return null;\n    }\n}\nasync function getUsers() {\n    try {\n        const token = getTokenFromCookie(\"idToken\"); // Assuming this function gets the token from cookies\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/get-users\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching users:\", response.statusText);\n            throw new Error(\"Failed to fetch users data\");\n        }\n        const usersList = await response.json();\n        const users = usersList.map((user)=>{\n            return {\n                _id: user._id,\n                user_id: user.user_id,\n                firebaseUid: user.firebaseUid,\n                name: user.name,\n                email: user.email,\n                phone: user.phone,\n                role: user.role,\n                avatar: user.avatar,\n                address: user.address,\n                school_ids: user.school_ids,\n                isVerified: user.isVerified,\n                verificationCode: user.verificationCode,\n                verificationCodeExpires: user.verificationCodeExpires,\n                lastLogin: user.lastLogin,\n                createdAt: user.createdAt,\n                updatedAt: user.updatedAt\n            };\n        });\n        return users;\n    } catch (error) {\n        console.error(\"Error fetching users:\", error);\n        throw new Error(\"Failed to fetch users data\");\n    }\n}\nasync function createUser(userData) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/register-user\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n            },\n            body: JSON.stringify(userData)\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to create user data\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                // If parsing the error body fails, use default message\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            console.error(\"Error creating user:\", errorMessage);\n            throw new Error(errorMessage);\n        }\n        const data = await response.json(); // Parse the response as JSON\n        return data; // Return the response data (usually the created user object)\n    } catch (error) {\n        console.error(\"Error creating user:\", error);\n        throw new Error(error instanceof Error ? error.message : \"Failed to create user data\");\n    }\n}\nasync function updateUser(user_id, userData) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/update-user/\").concat(user_id), {\n            method: \"PUT\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n            },\n            body: JSON.stringify(userData)\n        });\n        if (!response.ok) {\n            console.error(\"Error updating user:\", response.statusText);\n            throw new Error(\"Failed to update user data\");\n        }\n        const data = await response.json(); // Parse the response as JSON\n        return data; // Return the updated user data (this could be the user object with updated fields)\n    } catch (error) {\n        console.error(\"Error updating user:\", error);\n        throw new Error(\"Failed to update user data\");\n    }\n}\nasync function getUserById(userId) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/get-user/\").concat(userId), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching user:\", response.statusText);\n        throw new Error(\"Failed to fetch user data\");\n    }\n    const data = await response.json();\n    const user = {\n        _id: data._id,\n        user_id: data.user_id,\n        name: data.name,\n        email: data.email,\n        phone: data.phone,\n        role: data.role,\n        address: data.address,\n        school_ids: data.school_ids,\n        isVerified: data.isVerified,\n        description: data.description,\n        firebaseUid: data.firebaseUid,\n        createdAt: data.createdAt,\n        updatedAt: data.updatedAt\n    };\n    return user;\n}\nasync function verifyPassword(password, email) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/auth/verify-password\"), {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n            email: email,\n            password: password\n        })\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching user:\", response.statusText);\n        return false;\n    }\n    return true;\n}\nasync function deleteUser(user_id) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/delete-user/\").concat(user_id), {\n            method: \"DELETE\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n            }\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to delete user\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            console.error(\"Error deleting user:\", errorMessage);\n            throw new Error(errorMessage);\n        }\n        const result = await response.json();\n        return result; // Might return a success message or deleted user data\n    } catch (error) {\n        console.error(\"Error deleting user:\", error);\n        throw new Error(error instanceof Error ? error.message : \"Failed to delete user\");\n    }\n}\nasync function getUserBy_id(_id) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/get-user-by-id/\").concat(_id), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching user:\", response.statusText);\n        throw new Error(\"Failed to fetch user data\");\n    }\n    const data = await response.json();\n    const user = {\n        _id: data._id,\n        user_id: data.user_id,\n        name: data.name,\n        email: data.email,\n        phone: data.phone,\n        role: data.role,\n        address: data.address,\n        school_ids: data.school_ids,\n        isVerified: data.isVerified,\n        description: data.description,\n        firebaseUid: data.firebaseUid,\n        createdAt: data.createdAt,\n        updatedAt: data.updatedAt\n    };\n    return user;\n}\nasync function forget_password(email) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/auth/forgot-password\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email: email\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to send reset password email: check your email\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error sending reset password email:\", error);\n        throw new Error(\"Failed to send reset password email\");\n    }\n}\nasync function verify_otp(code, email) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/auth/verify-code\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                code: code,\n                email: email\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to verify OTP\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error verifying OTP:\", error);\n        throw new Error(\"Failed to verify OTP\");\n    }\n}\nasync function resend_Code(email) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/auth/resend-code\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email: email\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to resend code\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error resending code:\", error);\n        throw new Error(\"Failed to resend code\");\n    }\n}\nasync function reset_password(newPassword, email, code) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/auth/reset-password\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email: email,\n                code: code,\n                newPassword: newPassword\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to reset password\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error resetting password:\", error);\n        throw new Error(\"Failed to reset password\");\n    }\n}\nasync function handleUserSearch(query) {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/search-users?query=\").concat(encodeURIComponent(query)), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error searching users:\", response.statusText);\n            throw new Error(\"Failed to search users\");\n        }\n        const results = await response.json();\n        const users = results.map((user)=>{\n            return {\n                _id: user._id,\n                user_id: user.user_id,\n                firebaseUid: user.firebaseUid,\n                name: user.name,\n                email: user.email,\n                phone: user.phone,\n                role: user.role,\n                avatar: user.avatar,\n                address: user.address,\n                school_ids: user.school_ids,\n                isVerified: user.isVerified,\n                verificationCode: user.verificationCode,\n                verificationCodeExpires: user.verificationCodeExpires,\n                lastLogin: user.lastLogin,\n                createdAt: user.createdAt,\n                updatedAt: user.updatedAt\n            };\n        });\n        return users;\n    } catch (error) {\n        console.error(\"Error searching users:\", error);\n        throw new Error(\"Failed to search users\");\n    }\n}\nasync function registerParent(parentData) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/register-parent\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n            },\n            body: JSON.stringify(parentData)\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to register parent\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            console.error(\"Error registering parent:\", errorMessage);\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data; // This includes user object and generatedPassword (if new)\n    } catch (error) {\n        console.error(\"Error in registerParent service:\", error);\n        throw new Error(error instanceof Error ? error.message : \"Failed to register parent\");\n    }\n}\nasync function deleteMultipleUsers(userIds) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/delete-users\"), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n        },\n        body: JSON.stringify({\n            ids: userIds\n        })\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting multiple users:\", response.statusText);\n        throw new Error(\"Failed to delete multiple users\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function deleteAllUsers() {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/delete-all-users\"), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting all users:\", response.statusText);\n        throw new Error(\"Failed to delete all users\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function getTotalUsers() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/total-users\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching total users:\", response.statusText);\n            throw new Error(\"Failed to fetch total users\");\n        }\n        const data = await response.json();\n        return data.totalUsers;\n    } catch (error) {\n        console.error(\"Error fetching total users:\", error);\n        throw error;\n    }\n}\nasync function getUserCountWithChange() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/users-count-change\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching user count change:\", response.statusText);\n            throw new Error(\"Failed to fetch user count change\");\n        }\n        const data = await response.json();\n        return {\n            totalUsersThisMonth: data.totalUsersThisMonth,\n            percentageChange: data.percentageChange\n        };\n    } catch (error) {\n        console.error(\"Error fetching user count change:\", error);\n        throw error;\n    }\n}\nasync function getMonthlyUserStarts() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/monthly-user-starts\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Failed to fetch monthly user stats:\", response.statusText);\n            throw new Error(\"Failed to fetch monthly user stats\");\n        }\n        const data = await response.json();\n        // data has type { [year: string]: MonthlyUserStat[] }\n        return data;\n    } catch (error) {\n        console.error(\"Error fetching monthly user stats:\", error);\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/services/UserServices.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Dashboard/Layouts/SchoolLayout.tsx":
/*!***********************************************************!*\
  !*** ./src/components/Dashboard/Layouts/SchoolLayout.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,DollarSign,GraduationCap,LayoutDashboard,Presentation,School,Settings,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,DollarSign,GraduationCap,LayoutDashboard,Presentation,School,Settings,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/school.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,DollarSign,GraduationCap,LayoutDashboard,Presentation,School,Settings,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/presentation.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,DollarSign,GraduationCap,LayoutDashboard,Presentation,School,Settings,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,DollarSign,GraduationCap,LayoutDashboard,Presentation,School,Settings,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,DollarSign,GraduationCap,LayoutDashboard,Presentation,School,Settings,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,DollarSign,GraduationCap,LayoutDashboard,Presentation,School,Settings,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,DollarSign,GraduationCap,LayoutDashboard,Presentation,School,Settings,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,DollarSign,GraduationCap,LayoutDashboard,Presentation,School,Settings,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _widgets_Divider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../widgets/Divider */ \"(app-pages-browser)/./src/components/widgets/Divider.tsx\");\n/* harmony import */ var _widgets_SearchBox__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../widgets/SearchBox */ \"(app-pages-browser)/./src/components/widgets/SearchBox.tsx\");\n/* harmony import */ var _SideNavButton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../SideNavButton */ \"(app-pages-browser)/./src/components/Dashboard/SideNavButton.tsx\");\n/* harmony import */ var _Avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../Avatar */ \"(app-pages-browser)/./src/components/Dashboard/Avatar.tsx\");\n/* harmony import */ var _widgets_Logo__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../widgets/Logo */ \"(app-pages-browser)/./src/components/widgets/Logo.tsx\");\n/* harmony import */ var _GoPro__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../GoPro */ \"(app-pages-browser)/./src/components/Dashboard/GoPro.tsx\");\n/* harmony import */ var _NavigationBar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../NavigationBar */ \"(app-pages-browser)/./src/components/Dashboard/NavigationBar.tsx\");\n/* harmony import */ var _barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var _components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/utils/ProtectedRoute */ \"(app-pages-browser)/./src/components/utils/ProtectedRoute.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst SchoolLayout = (param)=>{\n    let { navigation, showGoPro = true, onLogout, children } = param;\n    _s();\n    const { user } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_9__[\"default\"])();\n    const [isSidebarOpen, setIsSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const avatar = {\n        avatarUrl: (user === null || user === void 0 ? void 0 : user.avatar) || \"https://img.freepik.com/free-psd/3d-illustration-person-with-sunglasses_23-2149436188.jpg\",\n        name: (user === null || user === void 0 ? void 0 : user.name) || \"School Admin\",\n        role: (user === null || user === void 0 ? void 0 : user.role) || \"admin\"\n    };\n    const BASE_URL = \"/school-admin\";\n    const sidebarNav = [\n        {\n            icon: _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            name: \"Dashboard\",\n            href: \"\".concat(BASE_URL, \"/dashboard\")\n        },\n        {\n            icon: _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            name: \"School\",\n            href: \"\".concat(BASE_URL, \"/school\")\n        },\n        {\n            icon: _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            name: \"Classes\",\n            href: \"\".concat(BASE_URL, \"/classes\")\n        },\n        {\n            icon: _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            name: \"Students\",\n            href: \"\".concat(BASE_URL, \"/students\")\n        },\n        {\n            icon: _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            name: \"Teachers\",\n            href: \"\".concat(BASE_URL, \"/teachers\")\n        },\n        {\n            icon: _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            name: \"Resources\",\n            href: \"\".concat(BASE_URL, \"/resources\")\n        },\n        {\n            icon: _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            name: \"Fees\",\n            href: \"\".concat(BASE_URL, \"/fees\")\n        },\n        {\n            icon: _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            name: \"Parents\",\n            href: \"\".concat(BASE_URL, \"/parents\")\n        }\n    ];\n    const settingsLink = {\n        icon: _barrel_optimize_names_BookOpen_DollarSign_GraduationCap_LayoutDashboard_Presentation_School_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n        name: \"Settings\",\n        href: \"\".concat(BASE_URL, \"/settings\")\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen overflow-hidden sm:p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"md:hidden p-2 bg-foreground text-background rounded-lg fixed top-4 left-4 z-50\",\n                    onClick: ()=>setIsSidebarOpen(!isSidebarOpen),\n                    children: isSidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 28\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 46\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex w-[290px] flex-col h-full border border-stroke p-2 rounded-lg fixed inset-y-0 left-0 z-40 bg-background transition-transform md:relative md:translate-x-0 \".concat(isSidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-3 overflow-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center gap-2 my-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_Logo__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_Divider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_SearchBox__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col gap-1\",\n                                    children: sidebarNav.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SideNavButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            icon: item.icon,\n                                            name: item.name,\n                                            href: item.href\n                                        }, item.name, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-auto flex flex-col gap-3\",\n                            children: [\n                                showGoPro && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GoPro__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    visible: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 27\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SideNavButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    icon: settingsLink.icon,\n                                    name: settingsLink.name,\n                                    href: settingsLink.href\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_Divider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Avatar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    avatarUrl: avatar.avatarUrl,\n                                    name: avatar.name,\n                                    role: avatar.role,\n                                    onLogout: onLogout\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-2 w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NavigationBar__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            icon: navigation.icon,\n                            baseHref: navigation.baseHref,\n                            title: navigation.title,\n                            onLogout: onLogout\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SchoolLayout, \"kzOdFYiF9c/5ccJ6E6DU+A4WC6E=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    ];\n});\n_c = SchoolLayout;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SchoolLayout);\nvar _c;\n$RefreshReg$(_c, \"SchoolLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Dashboard/Layouts/SchoolLayout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Dashboard/NavigationBar.tsx":
/*!****************************************************!*\
  !*** ./src/components/Dashboard/NavigationBar.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NavigationBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _widgets_SearchBox__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../widgets/SearchBox */ \"(app-pages-browser)/./src/components/widgets/SearchBox.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _BreadCrums__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./BreadCrums */ \"(app-pages-browser)/./src/components/Dashboard/BreadCrums.tsx\");\n/* harmony import */ var _widgets_UserMenuModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../widgets/UserMenuModal */ \"(app-pages-browser)/./src/components/widgets/UserMenuModal.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction NavigationBar(param) {\n    let { icon: Icon, baseHref, title, toggleSidebar, isSidebarOpen, onLogout } = param;\n    _s();\n    const [isDarkMode, setIsDarkMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user, logout } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    // Vérifier le thème au chargement\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NavigationBar.useEffect\": ()=>{\n            const prefersDark = window.matchMedia(\"(prefers-color-scheme: dark)\").matches;\n            setIsDarkMode(prefersDark);\n            if (prefersDark) {\n                document.documentElement.classList.add(\"dark\");\n            }\n        }\n    }[\"NavigationBar.useEffect\"], []);\n    // Basculer entre mode clair et sombre\n    const toggleTheme = ()=>{\n        setIsDarkMode(!isDarkMode);\n        if (isDarkMode) {\n            document.documentElement.classList.remove(\"dark\");\n        } else {\n            document.documentElement.classList.add(\"dark\");\n        }\n    };\n    // Gérer la déconnexion\n    const handleSignOut = ()=>{\n        onLogout();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full flex items-center justify-between p-4 bg-glassy\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                id: \"mobile-sidebar-toggle\",\n                className: \"lg:hidden p-2  text-foreground rounded-lg  top-4 left-4 z-30\",\n                onClick: ()=>toggleSidebar && toggleSidebar(),\n                children: isSidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 26\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 44\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:flex flex-col gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BreadCrums__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        baseHref: baseHref,\n                        icon: Icon\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-2xl font-semibold text-foreground\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_SearchBox__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"hidden lg:flex p-2 text-gray-600 dark:text-gray-300 hover:text-foreground transition\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"w-6 h-6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_UserMenuModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        avatarUrl: (user === null || user === void 0 ? void 0 : user.avatar) || \"https://img.freepik.com/free-psd/3d-illustration-person-with-sunglasses_23-2149436188.jpg\",\n                        userName: (user === null || user === void 0 ? void 0 : user.name) || \"\",\n                        onSignOut: logout,\n                        onToggleTheme: toggleTheme,\n                        isDarkMode: isDarkMode\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n_s(NavigationBar, \"24OZTdcMQjo2aNvu0Uo7vpQ5B80=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c = NavigationBar;\nvar _c;\n$RefreshReg$(_c, \"NavigationBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Dashboard/NavigationBar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Dashboard/SideNavButton.tsx":
/*!****************************************************!*\
  !*** ./src/components/Dashboard/SideNavButton.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst SidebarButton = (param)=>{\n    let { icon: Icon, name, href } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const isActive = pathname.startsWith(href);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n        href: href,\n        className: \"text-sm flex items-center w-full px-4 py-2 rounded-lg transition-all \\n                 hover:text-background hover:bg-foreground hover:shadow-lg\\n                 \".concat(isActive ? \"bg-foreground bg- text-background shadow-lg\" : \"text-foreground\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                className: \"w-5 h-5 mr-2\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\SideNavButton.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: name\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\SideNavButton.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\SideNavButton.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SidebarButton, \"xbyQPtUVMO7MNj7WjJlpdWqRcTo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = SidebarButton;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SidebarButton);\nvar _c;\n$RefreshReg$(_c, \"SidebarButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Dashboard/SideNavButton.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/utils/AChart.tsx":
/*!*****************************************!*\
  !*** ./src/components/utils/AChart.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/AreaChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Area.js\");\n/* harmony import */ var _app_services_UserServices__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/services/UserServices */ \"(app-pages-browser)/./src/app/services/UserServices.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n // update path accordingly\nconst monthShortNames = [\n    \"Jan\",\n    \"Feb\",\n    \"Mar\",\n    \"Apr\",\n    \"May\",\n    \"Jun\",\n    \"Jul\",\n    \"Aug\",\n    \"Sep\",\n    \"Oct\",\n    \"Nov\",\n    \"Dec\"\n];\nconst AChart = ()=>{\n    _s();\n    const [dataByYear, setDataByYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [selectedYear, setSelectedYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Transform backend month full names to short names expected by chart\n    const transformData = (rawData)=>{\n        const transformed = {};\n        for(const year in rawData){\n            transformed[year] = rawData[year].map((param)=>{\n                let { month, users } = param;\n                var _monthShortNames_find;\n                // convert full month name to short month name (e.g., \"January\" → \"Jan\")\n                const shortMonth = (_monthShortNames_find = monthShortNames.find((short, i)=>month.toLowerCase().startsWith(short.toLowerCase()))) !== null && _monthShortNames_find !== void 0 ? _monthShortNames_find : month.slice(0, 3);\n                return {\n                    month: shortMonth,\n                    users: users\n                };\n            });\n        }\n        return transformed;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AChart.useEffect\": ()=>{\n            const fetchData = {\n                \"AChart.useEffect.fetchData\": async ()=>{\n                    setLoading(true);\n                    setError(null);\n                    try {\n                        const rawData = await (0,_app_services_UserServices__WEBPACK_IMPORTED_MODULE_2__.getMonthlyUserStarts)();\n                        const transformedData = transformData(rawData);\n                        setDataByYear(transformedData);\n                        // Default to the latest year available\n                        const years = Object.keys(transformedData).sort({\n                            \"AChart.useEffect.fetchData.years\": (a, b)=>+b - +a\n                        }[\"AChart.useEffect.fetchData.years\"]);\n                        var _years_;\n                        setSelectedYear((_years_ = years[0]) !== null && _years_ !== void 0 ? _years_ : \"\");\n                    } catch (err) {\n                        setError(\"Failed to load data\");\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AChart.useEffect.fetchData\"];\n            fetchData();\n        }\n    }[\"AChart.useEffect\"], []);\n    if (loading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: \"Loading user stats...\"\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\AChart.tsx\",\n        lineNumber: 56,\n        columnNumber: 23\n    }, undefined);\n    if (error) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: error\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\AChart.tsx\",\n        lineNumber: 57,\n        columnNumber: 21\n    }, undefined);\n    var _dataByYear_selectedYear;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col gap-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold\",\n                        children: \"User Growth Trend\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\AChart.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        value: selectedYear,\n                        onChange: (e)=>setSelectedYear(e.target.value),\n                        className: \"   text-sm   border   rounded-md   pl-3   pr-8   py-1   appearance-none   cursor-pointer   w-24   text-sm   focus:outline-none focus:ring-1 focus:ring-foreground focus:border-foreground   bg-white   text-gray-900   dark:bg-gray-800   dark:text-white   \",\n                        children: Object.keys(dataByYear).map((year)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: year,\n                                children: year\n                            }, year, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\AChart.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\AChart.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\AChart.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-[300px]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__.ResponsiveContainer, {\n                    width: \"100%\",\n                    height: \"100%\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__.AreaChart, {\n                        data: (_dataByYear_selectedYear = dataByYear[selectedYear]) !== null && _dataByYear_selectedYear !== void 0 ? _dataByYear_selectedYear : [],\n                        margin: {\n                            top: 0,\n                            right: 0,\n                            left: -25,\n                            bottom: 0\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                    id: \"colorUsers\",\n                                    x1: \"0\",\n                                    y1: \"0\",\n                                    x2: \"0\",\n                                    y2: \"1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                            offset: \"5%\",\n                                            stopColor: \"#01B574\",\n                                            stopOpacity: 0.5\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\AChart.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                            offset: \"80%\",\n                                            stopColor: \"#01B574\",\n                                            stopOpacity: 0\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\AChart.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\AChart.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\AChart.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.XAxis, {\n                                dataKey: \"month\",\n                                axisLine: false,\n                                tick: {\n                                    fontSize: 10\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\AChart.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.YAxis, {\n                                tick: {\n                                    fontSize: 10\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\AChart.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Tooltip, {\n                                contentStyle: {\n                                    backgroundColor: \"bg-background\",\n                                    border: \"1px solid #ddd\",\n                                    borderRadius: \"4px\",\n                                    fontSize: \"12px\"\n                                },\n                                labelStyle: {\n                                    color: \"text-foreground\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\AChart.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.Area, {\n                                type: \"monotone\",\n                                dataKey: \"users\",\n                                stroke: \"#01B574\",\n                                strokeWidth: 2,\n                                fillOpacity: 1,\n                                fill: \"url(#colorUsers)\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\AChart.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\AChart.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\AChart.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\AChart.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\AChart.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AChart, \"+D+GRCPqIVtFe+ksJA6JcMcBEVM=\");\n_c = AChart;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AChart);\nvar _c;\n$RefreshReg$(_c, \"AChart\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/utils/AChart.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/utils/PerformanceTable.tsx":
/*!***************************************************!*\
  !*** ./src/components/utils/PerformanceTable.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../widgets/CircularLoader */ \"(app-pages-browser)/./src/components/widgets/CircularLoader.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst PerformanceTable = (param)=>{\n    let { data, defaultItemsPerPage = 5, metricOptions } = param;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // État pour le chargement\n    // État pour la pagination\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    // État pour le nombre d'éléments par page\n    const [itemsPerPage, setItemsPerPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultItemsPerPage);\n    // État pour le terme de recherche\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // État pour la métrique sélectionnée\n    const [selectedMetric, setSelectedMetric] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(metricOptions[0] || \"\");\n    // Options pour le sélecteur (5, 10, 15, 20, All)\n    const itemsPerPageOptions = [\n        5,\n        10,\n        15,\n        20,\n        \"All\"\n    ];\n    // Trier les données par la métrique sélectionnée (décroissant)\n    const sortedData = [\n        ...data\n    ].sort((a, b)=>{\n        const metricA = a.metrics[selectedMetric] || 0;\n        const metricB = b.metrics[selectedMetric] || 0;\n        return metricB - metricA;\n    });\n    // Filtrer les données en fonction du terme de recherche\n    const filteredData = sortedData.filter((row)=>row.schoolName.toLowerCase().includes(searchTerm.toLowerCase()));\n    // Calculer les données à afficher pour la page actuelle\n    const totalItems = filteredData.length;\n    const effectiveItemsPerPage = itemsPerPage === \"All\" ? totalItems : itemsPerPage;\n    const totalPages = Math.ceil(totalItems / effectiveItemsPerPage);\n    const startIndex = (currentPage - 1) * effectiveItemsPerPage;\n    const endIndex = startIndex + effectiveItemsPerPage;\n    const currentData = filteredData.slice(startIndex, endIndex);\n    // Fonctions pour la pagination\n    const goToPreviousPage = ()=>{\n        if (currentPage > 1) {\n            setCurrentPage(currentPage - 1);\n        }\n    };\n    const goToNextPage = ()=>{\n        if (currentPage < totalPages) {\n            setCurrentPage(currentPage + 1);\n        }\n    };\n    // Gérer le changement du nombre d'éléments par page\n    const handleItemsPerPageChange = (event)=>{\n        const value = event.target.value;\n        setItemsPerPage(value === \"All\" ? \"All\" : Number(value));\n        setCurrentPage(1); // Réinitialiser à la première page lors du changement\n    };\n    // Gérer la recherche\n    const handleSearch = (event)=>{\n        if (event.key === \"Enter\") {\n            setCurrentPage(1);\n            setIsLoading(true); // Afficher le loader pendant la recherche\n            setTimeout(()=>{\n                setIsLoading(false); // Masquer le loader après la recherche\n            }, 1000);\n        }\n    };\n    // Supprimer le filtre de recherche\n    const clearSearchFilter = ()=>{\n        setSearchTerm(\"\");\n        setCurrentPage(1);\n    };\n    // Supprimer tous les filtres\n    const clearAllFilters = ()=>{\n        setSearchTerm(\"\");\n        setCurrentPage(1);\n    };\n    // Gérer le changement de métrique\n    const handleMetricChange = (event)=>{\n        setSelectedMetric(event.target.value);\n        setCurrentPage(1); // Réinitialiser à la première page lors du changement\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full rounded-lg border border-gray-300 darK:border dark:border-gray-800 bg-widget shadow-sm flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative w-full max-w-xs\",\n                            children: [\n                                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute left-1 top-1/2 transform -translate-y-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        size: 8,\n                                        color: \"teal\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Search...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>{\n                                        setIsLoading(true);\n                                        setSearchTerm(e.target.value);\n                                        setTimeout(()=>{\n                                            setIsLoading(false);\n                                        }, 1000);\n                                    },\n                                    onKeyDown: handleSearch,\n                                    className: \"w-full  px-4 py-2 border border-gray-300 rounded-md text-sm text-gray-600 dark:text-foreground dark:bg-gray-700 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-teal\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 25\n                                }, undefined),\n                                searchTerm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: clearSearchFilter,\n                                    className: \"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col  text-right space-x-2 w-full max-w-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"metricSelect\",\n                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                    children: \"Select Performance Metric\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"metricSelect\",\n                                    value: selectedMetric,\n                                    onChange: handleMetricChange,\n                                    className: \"px-2 py-1 border w-full border-gray-300 rounded-md text-sm text-gray-600 dark:text-foreground dark:bg-gray-700 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-teal\",\n                                    children: metricOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: option,\n                                            children: option\n                                        }, option, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 33\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 17\n                }, undefined),\n                searchTerm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between text-teal border border-gray-200 dark:border-gray-700 py-1 px-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                    children: \"Active filters:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center border border-teal space-x-1 bg-teal-200 px-2 py-1 rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-teal\",\n                                            children: [\n                                                \"Search: \",\n                                                searchTerm\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: clearSearchFilter,\n                                            className: \"text-teal\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: clearAllFilters,\n                            className: \"text-gray-500 hover:text-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 21\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-h-[400px] overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"sm:w-full w-max table-auto border-collapse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"bg-gray-50 dark:bg-gray-800 text-left text-sm font-semibold text-foreground p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-4 py-3 w-12\",\n                                                    children: \"Rank\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-4 py-3 w-max\",\n                                                    children: \"School Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-4 py-3 w-max\",\n                                                    children: \"Performance Metric\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-4 py-3 w-max\",\n                                                    children: \"Metric Value\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 37\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        children: currentData.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                colSpan: 4,\n                                                className: \"px-4 py-3 text-center text-gray-500\",\n                                                children: \"No data available\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 37\n                                        }, undefined) : currentData.map((row, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-t border-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-4 py-3 w-12\",\n                                                        children: startIndex + index + 1\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-4 py-3 text-sm text-foreground\",\n                                                        children: row.schoolName\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-4 py-3 text-sm text-foreground\",\n                                                        children: selectedMetric\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-4 py-3 text-sm text-foreground\",\n                                                        children: row.metrics[selectedMetric] || \"N/A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 45\n                                                    }, undefined)\n                                                ]\n                                            }, row.id, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 41\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 21\n                        }, undefined),\n                        filteredData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between px-4 py-3 bg-white border-t border-gray-200 dark:bg-gray-800 dark:text-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden lg:block text-sm text-gray-600 dark:text-gray-400\",\n                                    children: [\n                                        \"Page \",\n                                        currentPage,\n                                        \" of \",\n                                        totalPages\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"itemsPerPage\",\n                                            className: \"hidden md:block text-sm text-gray-600 dark:text-gray-400\",\n                                            children: \"Items per page\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            id: \"itemsPerPage\",\n                                            value: itemsPerPage,\n                                            onChange: handleItemsPerPageChange,\n                                            className: \"hidden sm:block px-2 py-1 border border-gray-300 w-[80px] rounded-md text-sm text-gray-600 dark:text-foreground dark:bg-gray-700 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-teal\",\n                                            children: itemsPerPageOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: option,\n                                                    children: option\n                                                }, option, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 41\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex max-sm:justify-between max-sm:w-full space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: goToPreviousPage,\n                                            disabled: currentPage === 1,\n                                            className: \"p-2 text-foreground hover:text-teal disabled:text-gray-500 \".concat(currentPage === 1 ? \"cursor-not-allowed\" : \"max-sm:border max-sm:border-teal max-sm:rounded-lg\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    size: 20,\n                                                    className: \"hidden sm:block\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sm:hidden\",\n                                                    children: \"Previous\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 37\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        Array.from({\n                                            length: totalPages\n                                        }, (_, i)=>i + 1).map((page)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setCurrentPage(page),\n                                                className: \"px-3 w-[40px] h-[40px] py-1 rounded-full text-sm hidden sm:block \".concat(currentPage === page ? \"focus:outline-none ring-2 ring-teal text-teal\" : \"text-foreground hover:ring-2 hover:ring-teal hover:text-teal\"),\n                                                children: page\n                                            }, page, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 41\n                                            }, undefined)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: goToNextPage,\n                                            disabled: currentPage === totalPages,\n                                            className: \"p-2 text-foreground hover:text-teal disabled:text-gray-500 \".concat(currentPage === totalPages ? \"cursor-not-allowed\" : \"max-sm:border max-sm:border-teal max-sm:rounded-lg\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    size: 20,\n                                                    className: \"hidden sm:block\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sm:hidden\",\n                                                    children: \"Next\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 37\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n            lineNumber: 113,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\PerformanceTable.tsx\",\n        lineNumber: 111,\n        columnNumber: 9\n    }, undefined);\n};\n_s(PerformanceTable, \"zBCCKje40ALyWQWEHUWuNynrcnI=\");\n_c = PerformanceTable;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PerformanceTable);\nvar _c;\n$RefreshReg$(_c, \"PerformanceTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/utils/PerformanceTable.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/utils/ProtectedRoute.tsx":
/*!*************************************************!*\
  !*** ./src/components/utils/ProtectedRoute.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var _widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../widgets/CircularLoader */ \"(app-pages-browser)/./src/components/widgets/CircularLoader.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst ProtectedRoute = (param)=>{\n    let { children } = param;\n    _s();\n    const { isAuthenticated, loading, setRedirectAfterLogin } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProtectedRoute.useEffect\": ()=>{\n            if (!loading && !isAuthenticated) {\n                // Stocker l'URL actuelle pour rediriger après la connexion\n                setRedirectAfterLogin(pathname);\n                router.push(\"/login\");\n            }\n        }\n    }[\"ProtectedRoute.useEffect\"], [\n        loading,\n        isAuthenticated,\n        router,\n        pathname,\n        setRedirectAfterLogin\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-screen w-full absolute top-0 left-0 z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: 40,\n                color: \"teal\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\ProtectedRoute.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\ProtectedRoute.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!isAuthenticated) {\n        return null; // La redirection est gérée par useEffect\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n};\n_s(ProtectedRoute, \"ID4lDsz3ESTPiaN6oNQ7fqj9lxE=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = ProtectedRoute;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProtectedRoute);\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/utils/ProtectedRoute.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/widgets/StatsOverview.tsx":
/*!**************************************************!*\
  !*** ./src/components/widgets/StatsOverview.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StatsOverview)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowDownRight_ArrowUpRight_DollarSign_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownRight,ArrowUpRight,DollarSign!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownRight_ArrowUpRight_DollarSign_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownRight,ArrowUpRight,DollarSign!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownRight_ArrowUpRight_DollarSign_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownRight,ArrowUpRight,DollarSign!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down-right.js\");\n\n\n\nfunction StatsOverview(param) {\n    let { value, changePercentage = 5.78, icon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownRight_ArrowUpRight_DollarSign_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        className: \"h-3 w-3 text-background\"\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\StatsOverview.tsx\",\n        lineNumber: 14,\n        columnNumber: 10\n    }, this), title = \"Total Revenue\" } = param;\n    const isPositive = changePercentage >= 0;\n    const changeColor = isPositive ? 'text-[#01B574]' : 'text-[#FF3B3B]';\n    const ChangeIcon = isPositive ? _barrel_optimize_names_ArrowDownRight_ArrowUpRight_DollarSign_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"] : _barrel_optimize_names_ArrowDownRight_ArrowUpRight_DollarSign_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"rounded-lg flex items-center border border-stroke p-4 shadow-sm bg-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"rounded-lg w-fit bg-foreground p-3\",\n                        children: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(icon, {\n                            className: \"h-3 w-3 text-background\" // Ensures consistent sizing\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\StatsOverview.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-black dark:text-white\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\StatsOverview.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center text-sm \".concat(changeColor),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChangeIcon, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\StatsOverview.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                Math.abs(changePercentage),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\StatsOverview.tsx\",\n                                            lineNumber: 34,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-500 ml-1\",\n                                            children: \"Since last month\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\StatsOverview.tsx\",\n                                            lineNumber: 35,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\StatsOverview.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\StatsOverview.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\StatsOverview.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\StatsOverview.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-2xl\",\n                    children: value\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\StatsOverview.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\StatsOverview.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\StatsOverview.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n_c = StatsOverview;\nvar _c;\n$RefreshReg$(_c, \"StatsOverview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/widgets/StatsOverview.tsx\n"));

/***/ })

});