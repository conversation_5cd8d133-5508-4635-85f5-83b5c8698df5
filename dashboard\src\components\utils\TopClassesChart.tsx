"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON>A<PERSON><PERSON>, Toolt<PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON> } from "recharts";
import { getTopClassesBySchool, TopClassData } from "@/app/services/ClassServices";
import CircularLoader from "@/components/widgets/CircularLoader";

interface TopClassesChartProps {
  schoolId: string;
}

const TopClassesChart: React.FC<TopClassesChartProps> = ({ schoolId }) => {
  const [data, setData] = useState<TopClassData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTopClasses = async () => {
      if (!schoolId) return;
      
      setLoading(true);
      setError(null);
      
      try {
        const topClassesData = await getTopClassesBySchool(schoolId);
        setData(topClassesData);
      } catch (err) {
        console.error("Error fetching top classes:", err);
        setError("Failed to load top classes data");
      } finally {
        setLoading(false);
      }
    };

    fetchTopClasses();
  }, [schoolId]);

  if (loading) {
    return (
      <div className="rounded-lg border border-stroke bg-widget p-4 h-[400px] flex items-center justify-center">
        <CircularLoader size={32} color="teal" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-lg border border-stroke bg-widget p-4 h-[400px] flex items-center justify-center">
        <p className="text-foreground/60">{error}</p>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="rounded-lg border border-stroke bg-widget p-4 h-[400px] flex items-center justify-center">
        <p className="text-foreground/60">No classes data available</p>
      </div>
    );
  }

  // Transform data for the chart
  const chartData = data.map(item => ({
    name: item.className,
    students: item.metrics["Number of Students"],
    grade: item.metrics["Average Grade"],
  }));

  return (
    <div className="rounded-lg border border-stroke bg-widget p-4">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-foreground">Top Classes</h3>
        <p className="text-sm text-foreground/60">Performance overview of top performing classes</p>
      </div>
      
      <div className="h-[300px]">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={chartData}
            margin={{
              top: 20,
              right: 30,
              left: 20,
              bottom: 5,
            }}
          >
            <XAxis 
              dataKey="name" 
              axisLine={false} 
              tick={{ fontSize: 12, fill: "var(--foreground)" }}
              tickLine={false}
            />
            <YAxis 
              tick={{ fontSize: 12, fill: "var(--foreground)" }}
              axisLine={false}
              tickLine={false}
            />
            <Tooltip
              contentStyle={{
                backgroundColor: "var(--widget)",
                border: "1px solid var(--stroke)",
                borderRadius: "8px",
                fontSize: "12px",
                color: "var(--foreground)",
              }}
              labelStyle={{
                color: "var(--foreground)",
              }}
            />
            <Legend 
              wrapperStyle={{
                fontSize: "12px",
                color: "var(--foreground)",
              }}
            />
            <Bar 
              dataKey="students" 
              fill="#17B890" 
              name="Number of Students"
              radius={[4, 4, 0, 0]}
            />
            <Bar 
              dataKey="grade" 
              fill="#0E9B6D" 
              name="Average Grade"
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
      
      <div className="mt-4 grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-[#17B890] rounded"></div>
          <span className="text-sm text-foreground/70">Number of Students</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-[#0E9B6D] rounded"></div>
          <span className="text-sm text-foreground/70">Average Grade</span>
        </div>
      </div>
    </div>
  );
};

export default TopClassesChart;
