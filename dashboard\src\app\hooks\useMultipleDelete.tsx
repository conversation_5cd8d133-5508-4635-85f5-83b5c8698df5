import { useState } from 'react';
import { verifyPassword } from '@/app/services/UserServices';
import useAuth from './useAuth';

interface UseMultipleDeleteProps<T> {
  items: T[];
  setItems: React.Dispatch<React.SetStateAction<T[]>>;
  deleteMultipleService: (ids: string[]) => Promise<any>;
  deleteAllService: () => Promise<any>;
  idAccessor: keyof T;
  itemName: string;
  onSuccess?: (message: string) => void;
  onError?: (message: string) => void;
  clearTableSelection?: () => void; // New prop to clear table selection
}

export function useMultipleDelete<T>({
  items,
  setItems,
  deleteMultipleService,
  deleteAllService,
  idAccessor,
  itemName,
  onSuccess,
  onError,
  clearTableSelection
}: UseMultipleDeleteProps<T>) {
  const { user } = useAuth();
  const [isBulkDeleteModalOpen, setIsBulkDeleteModalOpen] = useState(false);
  const [bulkDeleteType, setBulkDeleteType] = useState<"selected" | "all">("selected");
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<"success" | "failure" | null>(null);

  // Handle opening bulk delete modal for selected items
  const handleDeleteMultiple = async (selectedIds: string[]) => {
    if (selectedIds.length === 0) {
      onError?.(`Please select at least one ${itemName} to delete.`);
      return;
    }
    setSelectedIds(selectedIds);
    setBulkDeleteType("selected");
    setIsBulkDeleteModalOpen(true);
  };

  // Handle opening bulk delete modal for all items
  const handleDeleteAll = async () => {
    if (items.length === 0) {
      onError?.(`No ${itemName}s to delete.`);
      return;
    }
    setBulkDeleteType("all");
    setIsBulkDeleteModalOpen(true);
  };

  // Handle the actual bulk deletion with password confirmation
  const handleBulkDeleteConfirm = async (password: string) => {
    setIsSubmitting(true);
    setSubmitStatus(null);

    // Verify password
    const passwordVerified = user ? await verifyPassword(password, user.email) : false;
    if (!passwordVerified) {
      onError?.("Invalid Password!");
      setIsSubmitting(false);
      setSubmitStatus("failure");
      return;
    }

    try {
      if (bulkDeleteType === "all") {
        await deleteAllService();
        onSuccess?.(`All ${itemName}s deleted successfully!`);
        setItems([]);
      } else {
        await deleteMultipleService(selectedIds);
        onSuccess?.(`${selectedIds.length} ${itemName}(s) deleted successfully!`);
        setItems(prev => prev.filter(item => !selectedIds.includes(String(item[idAccessor]))));
      }

      setSubmitStatus("success");

      // Clear table selection
      clearTableSelection?.();

      // Close modal after success
      setTimeout(() => {
        setIsBulkDeleteModalOpen(false);
        setSubmitStatus(null);
      }, 2000);

    } catch (error) {
      console.error("Error in bulk deletion:", error);
      const errorMessage = error instanceof Error ? error.message : `Failed to delete ${itemName}s`;
      onError?.(errorMessage);
      setSubmitStatus("failure");
    } finally {
      setIsSubmitting(false);
    }
  };

  const closeBulkDeleteModal = () => {
    setIsBulkDeleteModalOpen(false);
    setSubmitStatus(null);
  };

  return {
    // State
    isBulkDeleteModalOpen,
    bulkDeleteType,
    selectedIds,
    isSubmitting,
    submitStatus,
    
    // Handlers
    handleDeleteMultiple,
    handleDeleteAll,
    handleBulkDeleteConfirm,
    closeBulkDeleteModal,
    
    // Modal props
    modalProps: {
      isOpen: isBulkDeleteModalOpen,
      onClose: closeBulkDeleteModal,
      onConfirm: handleBulkDeleteConfirm,
      title: bulkDeleteType === "all" ? `Delete All ${itemName}s` : `Delete Selected ${itemName}s`,
      message: bulkDeleteType === "all"
        ? `Are you sure you want to delete ALL ${items.length} ${itemName}s? This action cannot be undone.`
        : `Are you sure you want to delete ${selectedIds.length} selected ${itemName}(s)? This action cannot be undone.`,
      itemCount: bulkDeleteType === "all" ? items.length : selectedIds.length,
      itemType: itemName + 's',
      isDeleteAll: bulkDeleteType === "all",
      isSubmitting,
      submitStatus,
      requirePassword: true
    }
  };
}
