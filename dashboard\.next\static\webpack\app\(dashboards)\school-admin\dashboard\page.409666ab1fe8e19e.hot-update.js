"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/dashboard/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js":
/*!*******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/trending-down.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ TrendingDown)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.485.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"polyline\",\n        {\n            points: \"22 17 13.5 8.5 8.5 13.5 2 7\",\n            key: \"1r2t7k\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"16 17 22 17 22 11\",\n            key: \"11uiuu\"\n        }\n    ]\n];\nconst TrendingDown = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"trending-down\", __iconNode);\n //# sourceMappingURL=trending-down.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/trending-up.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ TrendingUp)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.485.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"polyline\",\n        {\n            points: \"22 7 13.5 15.5 8.5 10.5 2 17\",\n            key: \"126l90\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"16 7 22 7 22 13\",\n            key: \"kwv8wd\"\n        }\n    ]\n];\nconst TrendingUp = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"trending-up\", __iconNode);\n //# sourceMappingURL=trending-up.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/user-check.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ UserCheck)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.485.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\",\n            key: \"1yyitq\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"9\",\n            cy: \"7\",\n            r: \"4\",\n            key: \"nufk8\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"16 11 18 13 22 9\",\n            key: \"1pwet4\"\n        }\n    ]\n];\nconst UserCheck = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"user-check\", __iconNode);\n //# sourceMappingURL=user-check.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/services/SchoolServices.tsx":
/*!*********************************************!*\
  !*** ./src/app/services/SchoolServices.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSchool: () => (/* binding */ createSchool),\n/* harmony export */   deleteAllSchools: () => (/* binding */ deleteAllSchools),\n/* harmony export */   deleteMultipleSchools: () => (/* binding */ deleteMultipleSchools),\n/* harmony export */   deleteSchool: () => (/* binding */ deleteSchool),\n/* harmony export */   getSchoolById: () => (/* binding */ getSchoolById),\n/* harmony export */   getSchoolBy_id: () => (/* binding */ getSchoolBy_id),\n/* harmony export */   getSchoolCountChange: () => (/* binding */ getSchoolCountChange),\n/* harmony export */   getSchoolCredits: () => (/* binding */ getSchoolCredits),\n/* harmony export */   getSchoolPerformance: () => (/* binding */ getSchoolPerformance),\n/* harmony export */   getSchools: () => (/* binding */ getSchools),\n/* harmony export */   getTotalSchools: () => (/* binding */ getTotalSchools),\n/* harmony export */   updateSchool: () => (/* binding */ updateSchool)\n/* harmony export */ });\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./AuthContext */ \"(app-pages-browser)/./src/app/services/AuthContext.tsx\");\n/* harmony import */ var _UserServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./UserServices */ \"(app-pages-browser)/./src/app/services/UserServices.tsx\");\n\n\nasync function getSchools() {\n    try {\n        const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/get-schools\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching schools:\", response.statusText);\n            throw new Error(\"Failed to fetch schools data\");\n        }\n        const schoolsList = await response.json();\n        const schools = schoolsList.map((school)=>{\n            return {\n                _id: school._id,\n                school_id: school.school_id,\n                name: school.name,\n                email: school.email,\n                address: school.address,\n                website: school.website,\n                phone_number: school.phone_number,\n                principal_name: school.principal_name,\n                established_year: school.established_year,\n                description: school.description\n            };\n        });\n        return schools;\n    } catch (error) {\n        console.error(\"Error fetching schools:\", error);\n        throw new Error(\"Failed to fetch schools data\");\n    }\n}\nasync function getSchoolById(schoolId) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/get-school/\").concat(schoolId), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat((0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching school:\", response.statusText);\n        throw new Error(\"Failed to fetch school data\");\n    }\n    const data = await response.json();\n    const school = {\n        school_id: data.school_id,\n        credit: data.credit,\n        name: data.name,\n        email: data.email,\n        address: data.address,\n        website: data.website,\n        phone_number: data.phone_number,\n        principal_name: data.principal_name,\n        established_year: data.established_year,\n        description: data.description\n    };\n    return school;\n}\nasync function getSchoolBy_id(schoolId) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/get-school_id/\").concat(schoolId), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat((0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching school:\", response.statusText);\n        throw new Error(\"Failed to fetch school data\");\n    }\n    const data = await response.json();\n    const school = {\n        _id: data._id,\n        school_id: data.school_id,\n        credit: data.credit,\n        name: data.name,\n        email: data.email,\n        address: data.address,\n        website: data.website,\n        phone_number: data.phone_number,\n        principal_name: data.principal_name,\n        established_year: data.established_year,\n        description: data.description\n    };\n    return school;\n}\nasync function createSchool(schoolData) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/create-school\"), {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat((0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\"))\n        },\n        body: JSON.stringify(schoolData)\n    });\n    if (!response.ok) {\n        console.error(\"Error creating school:\", response.statusText);\n        throw new Error(\"Failed to create school data\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function updateSchool(schoolId, schoolData) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/update-school/\").concat(schoolId), {\n        method: \"PUT\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat((0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\"))\n        },\n        body: JSON.stringify(schoolData)\n    });\n    if (!response.ok) {\n        console.error(\"Error updating school:\", response.statusText);\n        throw new Error(\"Failed to update school data\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function deleteSchool(schoolId) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/delete-school/\").concat(schoolId), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat((0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting school:\", response.statusText);\n        throw new Error(\"Failed to delete school data\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function deleteMultipleSchools(schoolIds) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/delete-schools\"), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat((0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\"))\n        },\n        body: JSON.stringify({\n            ids: schoolIds\n        })\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting multiple schools:\", response.statusText);\n        throw new Error(\"Failed to delete multiple schools\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function deleteAllSchools() {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/delete-all-schools\"), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat((0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting all schools:\", response.statusText);\n        throw new Error(\"Failed to delete all schools\");\n    }\n    const data = await response.json();\n    return data;\n}\n// Get total number of schools\nasync function getTotalSchools() {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/total-schools\"), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching total schools:\", response.statusText);\n        throw new Error(\"Failed to fetch total schools\");\n    }\n    const data = await response.json();\n    return data.totalSchools;\n}\n// Get schools created this month and percentage change from last month\nasync function getSchoolCountChange() {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/schools-count-change\"), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching school count change:\", response.statusText);\n        throw new Error(\"Failed to fetch school count change\");\n    }\n    const data = await response.json();\n    return {\n        totalThisMonth: data.totalSchoolsThisMonth,\n        percentageChange: data.percentageChange\n    };\n}\nasync function getSchoolPerformance() {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/performance\"), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching performance metrics:\", response.statusText);\n        throw new Error(\"Failed to fetch school performance metrics\");\n    }\n    const data = await response.json();\n    return data;\n}\n// Get school credits/points\nasync function getSchoolCredits(schoolId) {\n    const token = (0,_UserServices__WEBPACK_IMPORTED_MODULE_1__.getTokenFromCookie)(\"idToken\");\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_0__.BASE_API_URL, \"/school/get-school_id/\").concat(schoolId), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching school credits:\", response.statusText);\n        throw new Error(\"Failed to fetch school credits\");\n    }\n    const data = await response.json();\n    return data.credit || 0;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/services/SchoolServices.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Dashboard/Layouts/SchoolLayout.tsx":
/*!***********************************************************!*\
  !*** ./src/components/Dashboard/Layouts/SchoolLayout.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/school.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-cog.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/percent.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock-4.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-no-axes-gantt.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/notebook-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/presentation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/milestone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/megaphone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftRight,BookOpen,ChartNoAxesGantt,Clock4,Coins,CreditCard,DollarSign,FileCheck2,GraduationCap,LayoutDashboard,Megaphone,Menu,Milestone,NotebookPen,Percent,Presentation,School,Settings,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _widgets_Divider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../widgets/Divider */ \"(app-pages-browser)/./src/components/widgets/Divider.tsx\");\n/* harmony import */ var _widgets_SearchBox__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../widgets/SearchBox */ \"(app-pages-browser)/./src/components/widgets/SearchBox.tsx\");\n/* harmony import */ var _SideNavButton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../SideNavButton */ \"(app-pages-browser)/./src/components/Dashboard/SideNavButton.tsx\");\n/* harmony import */ var _SidebarGroup__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../SidebarGroup */ \"(app-pages-browser)/./src/components/Dashboard/SidebarGroup.tsx\");\n/* harmony import */ var _Avatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../Avatar */ \"(app-pages-browser)/./src/components/Dashboard/Avatar.tsx\");\n/* harmony import */ var _widgets_Logo__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../widgets/Logo */ \"(app-pages-browser)/./src/components/widgets/Logo.tsx\");\n/* harmony import */ var _GoPro__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../GoPro */ \"(app-pages-browser)/./src/components/Dashboard/GoPro.tsx\");\n/* harmony import */ var _NavigationBar__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../NavigationBar */ \"(app-pages-browser)/./src/components/Dashboard/NavigationBar.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var _components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/utils/ProtectedRoute */ \"(app-pages-browser)/./src/components/utils/ProtectedRoute.tsx\");\n/* harmony import */ var _BreadCrums__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../BreadCrums */ \"(app-pages-browser)/./src/components/Dashboard/BreadCrums.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst SchoolLayout = (param)=>{\n    let { navigation, showGoPro = true, onLogout, children } = param;\n    _s();\n    const { user } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_10__[\"default\"])();\n    const [isSidebarOpen, setIsSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const avatar = {\n        avatarUrl: (user === null || user === void 0 ? void 0 : user.avatar) || \"https://img.freepik.com/free-psd/3d-illustration-person-with-sunglasses_23-2149436188.jpg\",\n        name: (user === null || user === void 0 ? void 0 : user.name) || \"School Admin\",\n        role: (user === null || user === void 0 ? void 0 : user.role) || \"admin\"\n    };\n    const BASE_URL = \"/school-admin\";\n    // Individual navigation items (not in groups)\n    const individualNavItems = [\n        {\n            icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            name: \"Dashboard\",\n            href: \"\".concat(BASE_URL, \"/dashboard\")\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            name: \"School\",\n            href: \"\".concat(BASE_URL, \"/school\")\n        }\n    ];\n    // Grouped navigation items\n    const navigationGroups = [\n        {\n            title: \"People Management\",\n            icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            items: [\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                    name: \"Staff\",\n                    href: \"\".concat(BASE_URL, \"/staff\")\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                    name: \"Teachers\",\n                    href: \"\".concat(BASE_URL, \"/teachers\")\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                    name: \"Students\",\n                    href: \"\".concat(BASE_URL, \"/students\")\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                    name: \"Parents\",\n                    href: \"\".concat(BASE_URL, \"/parents\")\n                }\n            ]\n        },\n        {\n            title: \"Academic Records\",\n            icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n            items: [\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                    name: \"Grades\",\n                    href: \"\".concat(BASE_URL, \"/grades\")\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                    name: \"Time Table\",\n                    href: \"\".concat(BASE_URL, \"/timetable\")\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n                    name: \"Teacher Assignment\",\n                    href: \"\".concat(BASE_URL, \"/teacher-assignment\")\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                    name: \"Periods\",\n                    href: \"\".concat(BASE_URL, \"/period\")\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n                    name: \"Attendance\",\n                    href: \"\".concat(BASE_URL, \"/attendance\")\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n                    name: \"Subjects\",\n                    href: \"\".concat(BASE_URL, \"/subjects\")\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n                    name: \"Classes\",\n                    href: \"\".concat(BASE_URL, \"/classes\")\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n                    name: \"Discipline\",\n                    href: \"\".concat(BASE_URL, \"/discipline\")\n                }\n            ]\n        },\n        {\n            title: \"Communications\",\n            icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n            items: [\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n                    name: \"Announcements\",\n                    href: \"\".concat(BASE_URL, \"/announcements\")\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                    name: \"Resources\",\n                    href: \"\".concat(BASE_URL, \"/resources\")\n                }\n            ]\n        },\n        {\n            title: \"Financial\",\n            icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"],\n            items: [\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"],\n                    name: \"Fee Types\",\n                    href: \"\".concat(BASE_URL, \"/fees\")\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"],\n                    name: \"Fee Transactions\",\n                    href: \"\".concat(BASE_URL, \"/transaction\")\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"],\n                    name: \"Buy Credit\",\n                    href: \"\".concat(BASE_URL, \"/buy-credit\")\n                }\n            ]\n        }\n    ];\n    const settingsLink = {\n        icon: _barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"],\n        name: \"Settings\",\n        href: \"\".concat(BASE_URL, \"/settings\")\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen overflow-hidden sm:p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"md:hidden p-2 bg-foreground text-background rounded-lg fixed top-4 left-4 z-50\",\n                    onClick: ()=>setIsSidebarOpen(!isSidebarOpen),\n                    children: isSidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 28\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftRight_BookOpen_ChartNoAxesGantt_Clock4_Coins_CreditCard_DollarSign_FileCheck2_GraduationCap_LayoutDashboard_Megaphone_Menu_Milestone_NotebookPen_Percent_Presentation_School_Settings_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 46\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex w-[290px] flex-col border border-gray-300 darK:border dark:border-gray-800 h-full shadow-lg p-2 rounded-lg fixed inset-y-0 left-0 z-40 bg-widget transition-transform lg:relative lg:translate-x-0 \".concat(isSidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-3 overflow-auto subtle-scrollbar\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center gap-2 my-4 \",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_Logo__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_Divider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_SearchBox__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col gap-1\",\n                                    children: [\n                                        individualNavItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SideNavButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                icon: item.icon,\n                                                name: item.name,\n                                                href: item.href\n                                            }, item.name, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 17\n                                            }, undefined)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"my-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_Divider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        navigationGroups.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SidebarGroup__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                title: group.title,\n                                                icon: group.icon,\n                                                items: group.items,\n                                                defaultExpanded: group.title === \"Academic Records\"\n                                            }, group.title, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-auto flex flex-col gap-3\",\n                            children: [\n                                showGoPro && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GoPro__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    visible: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 27\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SideNavButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    icon: settingsLink.icon,\n                                    name: settingsLink.name,\n                                    href: settingsLink.href\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_Divider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Avatar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    avatarUrl: avatar.avatarUrl,\n                                    name: avatar.name,\n                                    role: avatar.role,\n                                    onLogout: onLogout\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"sm:px-6 px-2 py-2 w-full flex flex-col gap-4 lg:w-[80%] overflow-auto custom-scrollbar\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sticky top-0 z-20 flex items-center justify-between   \",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NavigationBar__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                icon: navigation.icon,\n                                baseHref: navigation.baseHref,\n                                title: navigation.title,\n                                isSidebarOpen: isSidebarOpen,\n                                toggleSidebar: ()=>setIsSidebarOpen(!isSidebarOpen),\n                                onLogout: onLogout\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex lg:hidden flex-col gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BreadCrums__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    baseHref: navigation.baseHref,\n                                    icon: navigation.icon\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-semibold text-foreground\",\n                                    children: navigation.title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n            lineNumber: 105,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\Layouts\\\\SchoolLayout.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SchoolLayout, \"kzOdFYiF9c/5ccJ6E6DU+A4WC6E=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    ];\n});\n_c = SchoolLayout;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SchoolLayout);\nvar _c;\n$RefreshReg$(_c, \"SchoolLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Dashboard/Layouts/SchoolLayout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Dashboard/NavigationBar.tsx":
/*!****************************************************!*\
  !*** ./src/components/Dashboard/NavigationBar.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NavigationBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _widgets_SearchBox__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../widgets/SearchBox */ \"(app-pages-browser)/./src/components/widgets/SearchBox.tsx\");\n/* harmony import */ var _barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _BreadCrums__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./BreadCrums */ \"(app-pages-browser)/./src/components/Dashboard/BreadCrums.tsx\");\n/* harmony import */ var _widgets_UserMenuModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../widgets/UserMenuModal */ \"(app-pages-browser)/./src/components/widgets/UserMenuModal.tsx\");\n/* harmony import */ var _widgets_NotificationCenter__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../widgets/NotificationCenter */ \"(app-pages-browser)/./src/components/widgets/NotificationCenter.tsx\");\n/* harmony import */ var _widgets_SchoolPoints__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../widgets/SchoolPoints */ \"(app-pages-browser)/./src/components/widgets/SchoolPoints.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction NavigationBar(param) {\n    let { icon: Icon, baseHref, title, toggleSidebar, isSidebarOpen, onLogout } = param;\n    _s();\n    const [isDarkMode, setIsDarkMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user, logout } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_7__[\"default\"])();\n    // Vérifier le thème au chargement\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NavigationBar.useEffect\": ()=>{\n            const prefersDark = window.matchMedia(\"(prefers-color-scheme: dark)\").matches;\n            setIsDarkMode(prefersDark);\n            if (prefersDark) {\n                document.documentElement.classList.add(\"dark\");\n            }\n        }\n    }[\"NavigationBar.useEffect\"], []);\n    // Basculer entre mode clair et sombre\n    const toggleTheme = ()=>{\n        setIsDarkMode(!isDarkMode);\n        if (isDarkMode) {\n            document.documentElement.classList.remove(\"dark\");\n        } else {\n            document.documentElement.classList.add(\"dark\");\n        }\n    };\n    // Gérer la déconnexion\n    const handleSignOut = ()=>{\n        onLogout();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full flex items-center justify-between p-4 bg-glassy shadow-md border md:border-none md:shadow-none border-gray-300 darK:border dark:border-gray-800\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        id: \"mobile-sidebar-toggle\",\n                        className: \"lg:hidden p-2  text-foreground rounded-lg  top-4 left-4 z-30\",\n                        onClick: ()=>toggleSidebar && toggleSidebar(),\n                        children: isSidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            size: 24\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 28\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            size: 24\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 46\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_SchoolPoints__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"md:hidden\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:flex flex-col gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BreadCrums__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        baseHref: baseHref,\n                        icon: Icon\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-2xl font-semibold text-foreground\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_SearchBox__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_SchoolPoints__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"hidden md:flex\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_NotificationCenter__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"hidden lg:block\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_UserMenuModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        avatarUrl: (user === null || user === void 0 ? void 0 : user.avatar) || \"https://img.freepik.com/free-psd/3d-illustration-person-with-sunglasses_23-2149436188.jpg\",\n                        userName: (user === null || user === void 0 ? void 0 : user.name) || \"\",\n                        onSignOut: logout,\n                        onToggleTheme: toggleTheme,\n                        isDarkMode: isDarkMode\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\Dashboard\\\\NavigationBar.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n_s(NavigationBar, \"24OZTdcMQjo2aNvu0Uo7vpQ5B80=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    ];\n});\n_c = NavigationBar;\nvar _c;\n$RefreshReg$(_c, \"NavigationBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Dashboard/NavigationBar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/widgets/SchoolPoints.tsx":
/*!*************************************************!*\
  !*** ./src/components/widgets/SchoolPoints.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SchoolPoints)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Coins,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Coins,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.js\");\n/* harmony import */ var _barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Coins,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_SchoolServices__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/services/SchoolServices */ \"(app-pages-browser)/./src/app/services/SchoolServices.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction SchoolPoints(param) {\n    let { className = \"\" } = param;\n    var _user_school_ids;\n    _s();\n    const { user } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    const [credits, setCredits] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Get school ID from user\n    const schoolId = (user === null || user === void 0 ? void 0 : (_user_school_ids = user.school_ids) === null || _user_school_ids === void 0 ? void 0 : _user_school_ids[0]) || (user === null || user === void 0 ? void 0 : user.school_id);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SchoolPoints.useEffect\": ()=>{\n            const fetchCredits = {\n                \"SchoolPoints.useEffect.fetchCredits\": async ()=>{\n                    if (!schoolId) {\n                        setLoading(false);\n                        return;\n                    }\n                    try {\n                        setLoading(true);\n                        console.log(\"schoolId\", schoolId);\n                        const schoolCredits = await (0,_app_services_SchoolServices__WEBPACK_IMPORTED_MODULE_2__.getSchoolCredits)(schoolId);\n                        setCredits(schoolCredits);\n                        setError(null);\n                    } catch (err) {\n                        console.error(\"Error fetching school credits:\", err);\n                        setError(\"Failed to load credits\");\n                        setCredits(0);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"SchoolPoints.useEffect.fetchCredits\"];\n            fetchCredits();\n        }\n    }[\"SchoolPoints.useEffect\"], [\n        schoolId\n    ]);\n    // Format number with commas\n    const formatNumber = (num)=>{\n        return new Intl.NumberFormat('en-US').format(num);\n    };\n    // Get color based on credit amount\n    const getCreditColor = ()=>{\n        if (credits >= 1000) return \"text-green-600 dark:text-green-400\";\n        if (credits >= 500) return \"text-blue-600 dark:text-blue-400\";\n        if (credits >= 100) return \"text-yellow-600 dark:text-yellow-400\";\n        return \"text-red-600 dark:text-red-400\";\n    };\n    // Get icon based on credit amount\n    const getCreditIcon = ()=>{\n        if (credits >= 500) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n            lineNumber: 62,\n            columnNumber: 32\n        }, this);\n        if (credits >= 100) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n            lineNumber: 63,\n            columnNumber: 32\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n            lineNumber: 64,\n            columnNumber: 12\n        }, this);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-2 \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden sm:block\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-2 text-red-500 \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-5 w-5\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"hidden sm:block text-sm\",\n                    children: \"Error\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n        initial: {\n            opacity: 0,\n            scale: 0.95\n        },\n        animate: {\n            opacity: 1,\n            scale: 1\n        },\n        className: \"flex items-center space-x-2 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sm:hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    whileHover: {\n                        scale: 1.1\n                    },\n                    whileTap: {\n                        scale: 0.95\n                    },\n                    className: \"relative p-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg shadow-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-5 w-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n                            children: credits > 999 ? '999+' : credits\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden sm:flex items-center space-x-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    whileHover: {\n                        scale: 1.05\n                    },\n                    className: \"flex items-center space-x-2 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 px-3 py-2 rounded-lg border border-yellow-200 dark:border-yellow-800 shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-1.5 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this),\n                                getCreditIcon()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-bold text-lg \".concat(getCreditColor()),\n                                            children: formatNumber(credits)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-foreground/60 font-medium\",\n                                            children: \"pts\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-foreground/50\",\n                                    children: \"School Credits\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden xs:flex sm:hidden items-center space-x-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    whileHover: {\n                        scale: 1.05\n                    },\n                    className: \"flex items-center space-x-2 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 px-2 py-1.5 rounded-lg border border-yellow-200 dark:border-yellow-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-1 bg-gradient-to-r from-yellow-400 to-orange-500 rounded\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-3 w-3 text-white\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-semibold text-sm \".concat(getCreditColor()),\n                            children: formatNumber(credits)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\SchoolPoints.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n_s(SchoolPoints, \"tdA9imYFgZlUC09PQ1mXR0gK1TI=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    ];\n});\n_c = SchoolPoints;\nvar _c;\n$RefreshReg$(_c, \"SchoolPoints\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/widgets/SchoolPoints.tsx\n"));

/***/ })

});