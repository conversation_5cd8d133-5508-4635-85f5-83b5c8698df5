import { getTokenFromCookie } from "@/app/services/UserServices";

import { BASE_API_URL } from "./AuthContext";

export interface TeacherSchema {
  _id: string;
  user_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
  school_ids: string[];
  role: string;
  subjects?: string[];
  classes?: string[];
  created_at: Date;
  updated_at: Date;
}

export interface TeacherCreateSchema {
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
  school_ids: string[];
  subjects?: string[];
  classes?: string[];
}

// Get all teachers
export async function getAllTeachers(): Promise<TeacherSchema[]> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/user/get-users`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching teachers:", response.statusText);
      throw new Error("Failed to fetch teachers");
    }

    const data = await response.json();
    // Filter only teachers
    return data.filter((user: any) => user.role === 'teacher') as TeacherSchema[];
  } catch (error) {
    console.error("Fetch error (teachers):", error);
    throw new Error("Failed to fetch teachers");
  }
}

// Get teachers by school
export async function getTeachersBySchool(schoolId: string): Promise<TeacherSchema[]> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/user/get-users`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching teachers by school:", response.statusText);
      throw new Error("Failed to fetch teachers by school");
    }

    const data = await response.json();
    // Filter teachers by school and role
    return data.filter((user: any) => 
      user.role === 'teacher' && 
      user.school_ids && 
      user.school_ids.includes(schoolId)
    ) as TeacherSchema[];
  } catch (error) {
    console.error("Fetch error (teachers by school):", error);
    throw new Error("Failed to fetch teachers by school");
  }
}

// Get teacher count by school
export async function getTeacherCountBySchool(schoolId: string): Promise<number> {
  try {
    const teachers = await getTeachersBySchool(schoolId);
    return teachers.length;
  } catch (error) {
    console.error("Error getting teacher count:", error);
    return 0;
  }
}

// Create teacher
export async function createTeacher(teacherData: TeacherCreateSchema): Promise<TeacherSchema> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/user/register-user`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        ...teacherData,
        role: 'teacher'
      }),
    });

    if (!response.ok) {
      console.error("Error creating teacher:", response.statusText);
      throw new Error("Failed to create teacher");
    }

    const data = await response.json();
    return data as TeacherSchema;
  } catch (error) {
    console.error("Create error (teacher):", error);
    throw new Error("Failed to create teacher");
  }
}

// Delete teacher
export async function deleteTeacher(teacherId: string): Promise<void> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/user/delete-user/${teacherId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error deleting teacher:", response.statusText);
      throw new Error("Failed to delete teacher");
    }
  } catch (error) {
    console.error("Delete error (teacher):", error);
    throw new Error("Failed to delete teacher");
  }
}

// Update teacher
export async function updateTeacher(teacherId: string, teacherData: Partial<TeacherCreateSchema>): Promise<TeacherSchema> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/user/update-user/${teacherId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(teacherData),
    });

    if (!response.ok) {
      console.error("Error updating teacher:", response.statusText);
      throw new Error("Failed to update teacher");
    }

    const data = await response.json();
    return data as TeacherSchema;
  } catch (error) {
    console.error("Update error (teacher):", error);
    throw new Error("Failed to update teacher");
  }
}
