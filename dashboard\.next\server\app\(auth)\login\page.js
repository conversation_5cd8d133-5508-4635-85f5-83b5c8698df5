/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/(auth)/login/page";
exports.ids = ["app/(auth)/login/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(auth)%2Flogin%2Fpage&page=%2F(auth)%2Flogin%2Fpage&appPaths=%2F(auth)%2Flogin%2Fpage&pagePath=private-next-app-dir%2F(auth)%2Flogin%2Fpage.tsx&appDir=D%3A%5CProjet%5Cscholarify%5Cdashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjet%5Cscholarify%5Cdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(auth)%2Flogin%2Fpage&page=%2F(auth)%2Flogin%2Fpage&appPaths=%2F(auth)%2Flogin%2Fpage&pagePath=private-next-app-dir%2F(auth)%2Flogin%2Fpage.tsx&appDir=D%3A%5CProjet%5Cscholarify%5Cdashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjet%5Cscholarify%5Cdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page7 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(auth)/login/page.tsx */ \"(rsc)/./src/app/(auth)/login/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(auth)',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page7, \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'not-found': [module4, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module5, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module6, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/(auth)/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(auth)%2Flogin%2Fpage&page=%2F(auth)%2Flogin%2Fpage&appPaths=%2F(auth)%2Flogin%2Fpage&pagePath=private-next-app-dir%2F(auth)%2Flogin%2Fpage.tsx&appDir=D%3A%5CProjet%5Cscholarify%5Cdashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjet%5Cscholarify%5Cdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5C(auth)%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5C(auth)%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(auth)/login/page.tsx */ \"(rsc)/./src/app/(auth)/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZXQlNUMlNUNzY2hvbGFyaWZ5JTVDJTVDZGFzaGJvYXJkJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDKGF1dGgpJTVDJTVDbG9naW4lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEtBQXlHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxQcm9qZXRcXFxcc2Nob2xhcmlmeVxcXFxkYXNoYm9hcmRcXFxcc3JjXFxcXGFwcFxcXFwoYXV0aClcXFxcbG9naW5cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5C(auth)%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Bricolage_Grotesque%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22bricolage%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cservices%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Cutils%5C%5CThemeInitializer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Bricolage_Grotesque%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22bricolage%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cservices%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Cutils%5C%5CThemeInitializer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/services/AuthContext.tsx */ \"(rsc)/./src/app/services/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/utils/ThemeInitializer.tsx */ \"(rsc)/./src/utils/ThemeInitializer.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Bricolage_Grotesque%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22bricolage%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cservices%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Cutils%5C%5CThemeInitializer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"32x32\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZXRcXHNjaG9sYXJpZnlcXGRhc2hib2FyZFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjMyeDMyXCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/(auth)/login/page.tsx":
/*!***************************************!*\
  !*** ./src/app/(auth)/login/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projet\\scholarify\\dashboard\\src\\app\\(auth)\\login\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"223ead8c6b9a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcUHJvamV0XFxzY2hvbGFyaWZ5XFxkYXNoYm9hcmRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjIyM2VhZDhjNmI5YVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Bricolage_Grotesque_arguments_subsets_latin_weight_400_700_variableName_bricolage___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Bricolage_Grotesque\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"400\",\"700\"]}],\"variableName\":\"bricolage\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Bricolage_Grotesque\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"400\\\",\\\"700\\\"]}],\\\"variableName\\\":\\\"bricolage\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Bricolage_Grotesque_arguments_subsets_latin_weight_400_700_variableName_bricolage___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Bricolage_Grotesque_arguments_subsets_latin_weight_400_700_variableName_bricolage___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _utils_ThemeInitializer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/ThemeInitializer */ \"(rsc)/./src/utils/ThemeInitializer.tsx\");\n/* harmony import */ var _services_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./services/AuthContext */ \"(rsc)/./src/app/services/AuthContext.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"Scholarify Admin\",\n    description: \"Admin dashboard for Scholarify\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Bricolage_Grotesque_arguments_subsets_latin_weight_400_700_variableName_bricolage___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_ThemeInitializer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_services_AuthContext__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUN1QjtBQVNqQkE7QUFQa0Q7QUFDRjtBQUUvQyxNQUFNRyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUlhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFXWCxnTUFBbUI7OzhCQUNsQyw4REFBQ0MsK0RBQWdCQTs7Ozs7OEJBQ2pCLDhEQUFDQywrREFBWUE7OEJBQ1JLOzs7Ozs7Ozs7Ozs7Ozs7OztBQUtiIiwic291cmNlcyI6WyJEOlxcUHJvamV0XFxzY2hvbGFyaWZ5XFxkYXNoYm9hcmRcXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xyXG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XHJcbmltcG9ydCB7IEJyaWNvbGFnZV9Hcm90ZXNxdWUgfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xyXG5pbXBvcnQgVGhlbWVJbml0aWFsaXplciBmcm9tIFwiQC91dGlscy9UaGVtZUluaXRpYWxpemVyXCI7XHJcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gXCIuL3NlcnZpY2VzL0F1dGhDb250ZXh0XCI7XHJcblxyXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xyXG4gIHRpdGxlOiBcIlNjaG9sYXJpZnkgQWRtaW5cIixcclxuICBkZXNjcmlwdGlvbjogXCJBZG1pbiBkYXNoYm9hcmQgZm9yIFNjaG9sYXJpZnlcIixcclxufTtcclxuY29uc3QgYnJpY29sYWdlID0gQnJpY29sYWdlX0dyb3Rlc3F1ZSh7IHN1YnNldHM6IFtcImxhdGluXCJdLCB3ZWlnaHQ6IFtcIjQwMFwiLCBcIjcwMFwiXSB9KTtcclxuXHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcclxuICBjaGlsZHJlbixcclxufTogUmVhZG9ubHk8e1xyXG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XHJcbn0+KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxyXG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2JyaWNvbGFnZS5jbGFzc05hbWV9PlxyXG4gICAgICAgIDxUaGVtZUluaXRpYWxpemVyIC8+XHJcbiAgICAgICAgPEF1dGhQcm92aWRlcj5cclxuICAgICAgICAgICAge2NoaWxkcmVufVxyXG4gICAgICAgIDwvQXV0aFByb3ZpZGVyPlxyXG4gICAgICA8L2JvZHk+XHJcbiAgICA8L2h0bWw+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiYnJpY29sYWdlIiwiVGhlbWVJbml0aWFsaXplciIsIkF1dGhQcm92aWRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/services/AuthContext.tsx":
/*!******************************************!*\
  !*** ./src/app/services/AuthContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthContext: () => (/* binding */ AuthContext),
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   BASE_API_URL: () => (/* binding */ BASE_API_URL)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const BASE_API_URL = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call BASE_API_URL() from the server but BASE_API_URL is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projet\\scholarify\\dashboard\\src\\app\\services\\AuthContext.tsx",
"BASE_API_URL",
);const AuthContext = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthContext() from the server but AuthContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projet\\scholarify\\dashboard\\src\\app\\services\\AuthContext.tsx",
"AuthContext",
);const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projet\\scholarify\\dashboard\\src\\app\\services\\AuthContext.tsx",
"AuthProvider",
);

/***/ }),

/***/ "(rsc)/./src/utils/ThemeInitializer.tsx":
/*!****************************************!*\
  !*** ./src/utils/ThemeInitializer.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\utils\\\\ThemeInitializer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projet\\scholarify\\dashboard\\src\\utils\\ThemeInitializer.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5C(auth)%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5C(auth)%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(auth)/login/page.tsx */ \"(ssr)/./src/app/(auth)/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZXQlNUMlNUNzY2hvbGFyaWZ5JTVDJTVDZGFzaGJvYXJkJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDKGF1dGgpJTVDJTVDbG9naW4lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEtBQXlHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxQcm9qZXRcXFxcc2Nob2xhcmlmeVxcXFxkYXNoYm9hcmRcXFxcc3JjXFxcXGFwcFxcXFwoYXV0aClcXFxcbG9naW5cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5C(auth)%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Bricolage_Grotesque%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22bricolage%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cservices%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Cutils%5C%5CThemeInitializer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Bricolage_Grotesque%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22bricolage%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cservices%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Cutils%5C%5CThemeInitializer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/services/AuthContext.tsx */ \"(ssr)/./src/app/services/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/utils/ThemeInitializer.tsx */ \"(ssr)/./src/utils/ThemeInitializer.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Bricolage_Grotesque%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22bricolage%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cservices%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjet%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Cutils%5C%5CThemeInitializer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/(auth)/login/page.tsx":
/*!***************************************!*\
  !*** ./src/app/(auth)/login/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Login)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_AppLogo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/AppLogo */ \"(ssr)/./src/components/AppLogo.tsx\");\n/* harmony import */ var _components_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/input */ \"(ssr)/./src/components/input.tsx\");\n/* harmony import */ var _components_FormHeading__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/FormHeading */ \"(ssr)/./src/components/FormHeading.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_NotificationCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/NotificationCard */ \"(ssr)/./src/components/NotificationCard.tsx\");\n/* harmony import */ var _styles_formStyle_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/styles/formStyle.css */ \"(ssr)/./src/styles/formStyle.css\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(ssr)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/widgets/CircularLoader */ \"(ssr)/./src/components/widgets/CircularLoader.tsx\");\n/* harmony import */ var _app_services_UserServices__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/services/UserServices */ \"(ssr)/./src/app/services/UserServices.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\nfunction Login() {\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isForgotPasswordLoading, setIsForgotPasswordLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { login, isAuthenticated, loading, redirectAfterLogin } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_7__[\"default\"])();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    // State pour les champs de formulaire\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [rememberMe, setRememberMe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errorMessage, setErrorMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Login.useEffect\": ()=>{\n            if (!loading && isAuthenticated) {\n                const redirectTo = redirectAfterLogin || '/super-admin/dashboard';\n                router.push(redirectTo);\n            }\n        }\n    }[\"Login.useEffect\"], [\n        isAuthenticated,\n        loading,\n        redirectAfterLogin,\n        router\n    ]);\n    if (loading || isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-screen w-full absolute top-0 left-0 z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                size: 40,\n                color: \"teal-500\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                lineNumber: 37,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n            lineNumber: 36,\n            columnNumber: 13\n        }, this);\n    }\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        try {\n            await login(email, password, rememberMe);\n            setErrorMessage('');\n            setHasError(false);\n            setEmail('');\n            setPassword('');\n            setRememberMe(false);\n            let user = await (0,_app_services_UserServices__WEBPACK_IMPORTED_MODULE_10__.getCurrentUser)();\n            if (user && user.role === 'super') {\n                router.push('/super-admin/dashboard');\n            } else if (user && user.role === 'admin') {\n                router.push('/school-admin/dashboard');\n            } else if (user && user.role === 'teacher') {\n                router.push('/teacher-dashboard');\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"The email or password you entered doesn't match our records. Please double-check and try again.\";\n            setErrorMessage(errorMessage);\n            setHasError(true);\n        } finally{\n            setIsLoading(false);\n            setPassword('');\n        }\n    };\n    // Fonction pour gérer le clic sur le lien \"Forgot Password?\"\n    const handleForgotPasswordClick = (e)=>{\n        e.preventDefault();\n        setIsForgotPasswordLoading(true);\n        // Rediriger vers la page de récupération de mot de passe après un court délai\n        setTimeout(()=>{\n            router.push('/forgot-password');\n        }, 800); // Délai de 800ms pour montrer l'animation\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex bg-white dark:bg-gray-900 dark:text-white  rounded-lg shadow-lg h-screen px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"asideLogo w-[50%] h-screen py-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"asideImage w-full h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"/assets/images/asideImage2.png\",\n                            className: \"h-full w-full rounded-[25px]\",\n                            alt: \"Aside Image\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 17\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 13\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"asideForm  bg-white dark:bg-gray-900 flex flex-col justify-evenly items-center m-auto  \",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" flex flex-col justify-evenly items-center m-auto w-full max-w-[500px]  dark:text-white py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AppLogo__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                logoSrc: \"/assets/logo.png\",\n                                logoAlt: \"Logo\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mx-auto p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FormHeading__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        title: \"Nice to see you!\",\n                                        subtitle: \"Sign in to access your Dashboard\",\n                                        formIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"50\",\n                                            height: \"50\",\n                                            viewBox: \"0 0 50 50\",\n                                            fill: \"none\",\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                    x: \"1.25\",\n                                                    y: \"1.25\",\n                                                    width: \"47.5\",\n                                                    height: \"47.5\",\n                                                    rx: \"13.75\",\n                                                    stroke: \"#17B890\",\n                                                    strokeWidth: \"2.5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 37\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M27.167 17.832H30.5003C30.9424 17.832 31.3663 18.0076 31.6788 18.3202C31.9914 18.6327 32.167 19.0567 32.167 19.4987V31.1654C32.167 31.6074 31.9914 32.0313 31.6788 32.3439C31.3663 32.6564 30.9424 32.832 30.5003 32.832H27.167\",\n                                                    stroke: \"#17B890\",\n                                                    strokeWidth: \"2.5\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 37\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M23 29.4974L27.1667 25.3307L23 21.1641\",\n                                                    stroke: \"#17B890\",\n                                                    strokeWidth: \"2.5\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 37\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M27.167 25.332H17.167\",\n                                                    stroke: \"#17B890\",\n                                                    strokeWidth: \"2.5\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 37\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 33\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NotificationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        title: \"Error\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"20\",\n                                            height: \"20\",\n                                            viewBox: \"0 0 20 20\",\n                                            fill: \"none\",\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M8.4375 6.5625C8.4375 6.31527 8.51081 6.0736 8.64817 5.86804C8.78552 5.66248 8.98074 5.50226 9.20915 5.40765C9.43756 5.31304 9.68889 5.28829 9.93137 5.33652C10.1738 5.38475 10.3966 5.5038 10.5714 5.67862C10.7462 5.85343 10.8653 6.07616 10.9135 6.31864C10.9617 6.56111 10.937 6.81245 10.8424 7.04085C10.7477 7.26926 10.5875 7.46448 10.382 7.60184C10.1764 7.73919 9.93473 7.8125 9.6875 7.8125C9.35598 7.8125 9.03804 7.6808 8.80362 7.44638C8.5692 7.21196 8.4375 6.89402 8.4375 6.5625ZM18.4375 10C18.4375 11.6688 17.9427 13.3001 17.0155 14.6876C16.0884 16.0752 14.7706 17.1566 13.2289 17.7952C11.6871 18.4338 9.99064 18.6009 8.35393 18.2754C6.71721 17.9498 5.2138 17.1462 4.03379 15.9662C2.85378 14.7862 2.05019 13.2828 1.72463 11.6461C1.39907 10.0094 1.56616 8.31286 2.20477 6.77111C2.84338 5.22936 3.92484 3.9116 5.31238 2.98448C6.69992 2.05735 8.33122 1.5625 10 1.5625C12.237 1.56498 14.3817 2.45473 15.9635 4.03653C17.5453 5.61833 18.435 7.763 18.4375 10ZM16.5625 10C16.5625 8.70206 16.1776 7.43327 15.4565 6.35407C14.7354 5.27487 13.7105 4.43374 12.5114 3.93704C11.3122 3.44034 9.99272 3.31038 8.71972 3.5636C7.44672 3.81681 6.2774 4.44183 5.35962 5.35961C4.44183 6.27739 3.81682 7.44672 3.5636 8.71972C3.31038 9.99272 3.44034 11.3122 3.93704 12.5114C4.43374 13.7105 5.27488 14.7354 6.35407 15.4565C7.43327 16.1776 8.70206 16.5625 10 16.5625C11.7399 16.5606 13.408 15.8686 14.6383 14.6383C15.8686 13.408 16.5606 11.7399 16.5625 10ZM10.9375 12.8656V10.3125C10.9375 9.8981 10.7729 9.50067 10.4799 9.20764C10.1868 8.91462 9.7894 8.75 9.375 8.75C9.1536 8.74967 8.93923 8.82771 8.76986 8.97029C8.60048 9.11287 8.48703 9.31079 8.4496 9.52901C8.41217 9.74722 8.45318 9.97164 8.56536 10.1625C8.67754 10.3534 8.85365 10.4984 9.0625 10.5719V13.125C9.0625 13.5394 9.22712 13.9368 9.52015 14.2299C9.81317 14.5229 10.2106 14.6875 10.625 14.6875C10.8464 14.6878 11.0608 14.6098 11.2301 14.4672C11.3995 14.3246 11.513 14.1267 11.5504 13.9085C11.5878 13.6903 11.5468 13.4659 11.4346 13.275C11.3225 13.0841 11.1464 12.9391 10.9375 12.8656Z\",\n                                                fill: \"#F43F5E\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 37\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 33\n                                        }, void 0),\n                                        message: errorMessage,\n                                        type: \"error\",\n                                        isVisible: hasError,\n                                        onClose: ()=>{\n                                            setErrorMessage('');\n                                            setHasError(false);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSubmit,\n                                        className: \"flex flex-col\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_input__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    label: \"Email\",\n                                                    type: \"email\",\n                                                    id: \"email\",\n                                                    prefixIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"20\",\n                                                        height: \"20\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        fill: \"none\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M17.5 3.4375H2.5C2.25136 3.4375 2.0129 3.53627 1.83709 3.71209C1.66127 3.8879 1.5625 4.12636 1.5625 4.375V15C1.5625 15.4144 1.72712 15.8118 2.02015 16.1049C2.31317 16.3979 2.7106 16.5625 3.125 16.5625H16.875C17.2894 16.5625 17.6868 16.3979 17.9799 16.1049C18.2729 15.8118 18.4375 15.4144 18.4375 15V4.375C18.4375 4.12636 18.3387 3.8879 18.1629 3.71209C17.9871 3.53627 17.7486 3.4375 17.5 3.4375ZM15.0898 5.3125L10 9.97813L4.91016 5.3125H15.0898ZM3.4375 14.6875V6.50625L9.36641 11.9414C9.53932 12.1 9.7654 12.1879 10 12.1879C10.2346 12.1879 10.4607 12.1 10.6336 11.9414L16.5625 6.50625V14.6875H3.4375Z\",\n                                                            fill: \"#575D5E\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                            lineNumber: 132,\n                                                            columnNumber: 45\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 41\n                                                    }, void 0),\n                                                    value: email,\n                                                    onChange: (e)=>setEmail(e.target.value),\n                                                    className: \"w-full pl-10 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 33\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_input__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    label: \"Password\",\n                                                    type: \"password\",\n                                                    id: \"password\",\n                                                    prefixIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"20\",\n                                                        height: \"20\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        fill: \"none\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M16.25 5.9375H14.0625V4.375C14.0625 3.29756 13.6345 2.26425 12.8726 1.50238C12.1108 0.740512 11.0774 0.3125 10 0.3125C8.92256 0.3125 7.88925 0.740512 7.12738 1.50238C6.36551 2.26425 5.9375 3.29756 5.9375 4.375V5.9375H3.75C3.3356 5.9375 2.93817 6.10212 2.64515 6.39515C2.35212 6.68817 2.1875 7.0856 2.1875 7.5V16.25C2.1875 16.6644 2.35212 17.0618 2.64515 17.3549C2.93817 17.6479 3.3356 17.8125 3.75 17.8125H16.25C16.6644 17.8125 17.0618 17.6479 17.3549 17.3549C17.6479 17.0618 17.8125 16.6644 17.8125 16.25V7.5C17.8125 7.0856 17.6479 6.68817 17.3549 6.39515C17.0618 6.10212 16.6644 5.9375 16.25 5.9375ZM7.8125 4.375C7.8125 3.79484 8.04297 3.23844 8.4532 2.8282C8.86344 2.41797 9.41984 2.1875 10 2.1875C10.5802 2.1875 11.1366 2.41797 11.5468 2.8282C11.957 3.23844 12.1875 3.79484 12.1875 4.375V5.9375H7.8125V4.375ZM15.9375 15.9375H4.0625V7.8125H15.9375V15.9375Z\",\n                                                            fill: \"#575D5E\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 45\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 41\n                                                    }, void 0),\n                                                    value: password,\n                                                    onChange: (e)=>setPassword(e.target.value),\n                                                    className: \"w-full pl-10 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 33\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: rememberMe,\n                                                                onChange: (e)=>setRememberMe(e.target.checked),\n                                                                className: \"mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                                lineNumber: 161,\n                                                                columnNumber: 37\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-800 dark:text-gray-200\",\n                                                                children: \"Remember for 30 days\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                                lineNumber: 167,\n                                                                columnNumber: 37\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/forgot-password\",\n                                                        onClick: handleForgotPasswordClick,\n                                                        className: \"text-sm text-blue-600 hover:underline flex items-center\",\n                                                        children: isForgotPasswordLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-3 w-3 animate-spin mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 176,\n                                                                    columnNumber: 45\n                                                                }, this),\n                                                                \"Redirecting...\"\n                                                            ]\n                                                        }, void 0, true) : \"Forgot Password?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                className: \"w-full flex justify-center gap-2 items-center bg-[#17B890] text-white py-2 rounded-full hover:bg-[#17b890c4] transition duration-200\",\n                                                children: [\n                                                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 47\n                                                    }, this),\n                                                    \"Sign In\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"21\",\n                                                        height: \"20\",\n                                                        viewBox: \"0 0 21 20\",\n                                                        fill: \"none\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M10.1875 16.875C10.1875 17.1236 10.0887 17.3621 9.91291 17.5379C9.7371 17.7137 9.49864 17.8125 9.25 17.8125H4.25C4.00136 17.8125 3.7629 17.7137 3.58709 17.5379C3.41127 17.3621 3.3125 17.1236 3.3125 16.875V3.125C3.3125 2.87636 3.41127 2.6379 3.58709 2.46209C3.7629 2.28627 4.00136 2.1875 4.25 2.1875H9.25C9.49864 2.1875 9.7371 2.28627 9.91291 2.46209C10.0887 2.6379 10.1875 2.87636 10.1875 3.125C10.1875 3.37364 10.0887 3.6121 9.91291 3.78791C9.7371 3.96373 9.49864 4.0625 9.25 4.0625H5.1875V15.9375H9.25C9.49864 15.9375 9.7371 16.0363 9.91291 16.2121C10.0887 16.3879 10.1875 16.6264 10.1875 16.875ZM18.6633 9.33672L15.5383 6.21172C15.3622 6.0356 15.1233 5.93665 14.8742 5.93665C14.6251 5.93665 14.3863 6.0356 14.2102 6.21172C14.034 6.38784 13.9351 6.62671 13.9351 6.87578C13.9351 7.12485 14.034 7.36372 14.2102 7.53984L15.7344 9.0625H9.25C9.00136 9.0625 8.7629 9.16127 8.58709 9.33709C8.41127 9.5129 8.3125 9.75136 8.3125 10C8.3125 10.2486 8.41127 10.4871 8.58709 10.6629C8.7629 10.8387 9.00136 10.9375 9.25 10.9375H15.7344L14.2094 12.4617C14.0333 12.6378 13.9343 12.8767 13.9343 13.1258C13.9343 13.3749 14.0333 13.6137 14.2094 13.7898C14.3855 13.966 14.6244 14.0649 14.8734 14.0649C15.1225 14.0649 15.3614 13.966 15.5375 13.7898L18.6625 10.6648C18.7499 10.5778 18.8194 10.4743 18.8667 10.3604C18.9141 10.2465 18.9386 10.1243 18.9386 10.0009C18.9387 9.87755 18.9144 9.75537 18.8672 9.64138C18.8199 9.5274 18.7506 9.42387 18.6633 9.33672Z\",\n                                                            fill: \"#F5F6FA\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"flex justify-center items-center gap-5 py-4 text-gray-800 dark:text-gray-200 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"hover:text-[#17B890]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                children: \"Licence\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 66\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"hover:text-[#17B890]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                children: \"Terms of Use\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 66\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"hover:text-[#17B890]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                children: \"Blog\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 66\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 17\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 13\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n            lineNumber: 85,\n            columnNumber: 9\n        }, this)\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(auth)/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/hooks/useAuth.tsx":
/*!***********************************!*\
  !*** ./src/app/hooks/useAuth.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _services_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/AuthContext */ \"(ssr)/./src/app/services/AuthContext.tsx\");\n\n\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_services_AuthContext__WEBPACK_IMPORTED_MODULE_1__.AuthContext);\n    if (!context) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2hvb2tzL3VzZUF1dGgudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBbUM7QUFDbUI7QUFHdkMsU0FBU0U7SUFDcEIsTUFBTUMsVUFBVUgsaURBQVVBLENBQUNDLDhEQUFXQTtJQUN0QyxJQUFJLENBQUNFLFNBQVM7UUFDVixNQUFNLElBQUlDLE1BQU07SUFDcEI7SUFDQSxPQUFPRDtBQUNYIiwic291cmNlcyI6WyJEOlxcUHJvamV0XFxzY2hvbGFyaWZ5XFxkYXNoYm9hcmRcXHNyY1xcYXBwXFxob29rc1xcdXNlQXV0aC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlQ29udGV4dCB9IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgeyBBdXRoQ29udGV4dCB9IGZyb20gXCIuLi9zZXJ2aWNlcy9BdXRoQ29udGV4dFwiO1xyXG5cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHVzZUF1dGgoKXtcclxuICAgIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KEF1dGhDb250ZXh0KTtcclxuICAgIGlmICghY29udGV4dCkge1xyXG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcInVzZUF1dGggbXVzdCBiZSB1c2VkIHdpdGhpbiBhbiBBdXRoUHJvdmlkZXJcIik7XHJcbiAgICB9XHJcbiAgICByZXR1cm4gY29udGV4dDtcclxufSJdLCJuYW1lcyI6WyJ1c2VDb250ZXh0IiwiQXV0aENvbnRleHQiLCJ1c2VBdXRoIiwiY29udGV4dCIsIkVycm9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/hooks/useAuth.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/services/AuthContext.tsx":
/*!******************************************!*\
  !*** ./src/app/services/AuthContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthContext: () => (/* binding */ AuthContext),\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   BASE_API_URL: () => (/* binding */ BASE_API_URL)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _UserServices__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./UserServices */ \"(ssr)/./src/app/services/UserServices.tsx\");\n/* harmony import */ var _utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/httpInterceptor */ \"(ssr)/./src/app/utils/httpInterceptor.tsx\");\n/* __next_internal_client_entry_do_not_use__ BASE_API_URL,AuthContext,AuthProvider auto */ \n\n\n\n\nconst BASE_API_URL = process.env.BASE_API_URL || \"https://scolarify.onrender.com/api\";\n// Create a context for authentication\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)(undefined);\n// composant AuthProvider qui fournit le contexte d'authentification\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [redirectAfterLogin, setRedirectAfterLogin] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [authCheckInterval, setAuthCheckInterval] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Fonction pour forcer la déconnexion\n    const forceLogout = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"AuthProvider.useCallback[forceLogout]\": ()=>{\n            console.warn(\"Force logout triggered\");\n            setUser(null);\n            (0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__.clearSession)();\n            if (authCheckInterval) {\n                clearInterval(authCheckInterval);\n                setAuthCheckInterval(null);\n            }\n            if (false) {}\n        }\n    }[\"AuthProvider.useCallback[forceLogout]\"], [\n        authCheckInterval\n    ]);\n    // Fonction pour vérifier le statut d'authentification\n    const checkAuthStatus = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"AuthProvider.useCallback[checkAuthStatus]\": async ()=>{\n            try {\n                // Vérifier si le token existe\n                const token = js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(\"idToken\");\n                if (!token) {\n                    forceLogout();\n                    return;\n                }\n                // Vérifier si le token est expiré\n                if ((0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__.isTokenExpired)()) {\n                    console.warn(\"Token expired, logging out\");\n                    forceLogout();\n                    return;\n                }\n                // Vérifier avec le serveur\n                const currentUser = await (0,_UserServices__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)();\n                if (!currentUser) {\n                    forceLogout();\n                    return;\n                }\n                setUser(currentUser);\n            } catch (error) {\n                console.error(\"Auth check failed:\", error);\n                forceLogout();\n            }\n        }\n    }[\"AuthProvider.useCallback[checkAuthStatus]\"], [\n        forceLogout\n    ]);\n    // vérifier si un utilisateur est déja connecté\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const checkUserLoggedIn = {\n                \"AuthProvider.useEffect.checkUserLoggedIn\": async ()=>{\n                    try {\n                        // Vérifier d'abord si le token existe et n'est pas expiré\n                        const token = js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(\"idToken\");\n                        if (!token || (0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__.isTokenExpired)()) {\n                            setUser(null);\n                            setLoading(false);\n                            return;\n                        }\n                        const user = await (0,_UserServices__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)();\n                        if (user) {\n                            setUser(user);\n                            // Démarrer la vérification périodique\n                            const interval = setInterval(checkAuthStatus, 60000); // Vérifier toutes les minutes\n                            setAuthCheckInterval(interval);\n                        } else {\n                            js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"idToken\");\n                            setUser(null);\n                        }\n                    } catch (error) {\n                        console.error(\"Error verifying token:\", error);\n                        js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"idToken\");\n                        setUser(null);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.checkUserLoggedIn\"];\n            checkUserLoggedIn();\n            // Cleanup interval on unmount\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    if (authCheckInterval) {\n                        clearInterval(authCheckInterval);\n                    }\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        checkAuthStatus,\n        authCheckInterval\n    ]);\n    const login = async (email, password, rememberMe, redirectUrl)=>{\n        try {\n            const response = await fetch(`${BASE_API_URL}/auth/login`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email: email,\n                    password: password\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                console.error(\"Login error:\", data.message || \"Unknown error\");\n                throw new Error(data.message || \"Login failed\");\n            }\n            const { idToken } = data;\n            if (!idToken) {\n                throw new Error(\"No idToken received\");\n            }\n            // Stocker le token dans les cookies\n            js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].set(\"idToken\", idToken, {\n                expires: rememberMe ? 30 : 7\n            }); // Expire dans 7 jours\n            const user = await (0,_UserServices__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)(); // Vérifier si l'utilisateur est connecté à nouveau après la connexion réussie\n            if (user) {\n                setUser(user);\n            }\n            // Si une URL de redirection est fournie, stocke-la\n            if (redirectUrl) {\n                setRedirectAfterLogin(redirectUrl);\n            }\n        } catch (error) {\n            console.error(\"Login failed:\", error);\n            throw error;\n        }\n    };\n    const logout = async ()=>{\n        setUser(null);\n        js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"idToken\"); // Supprimer le token des cookies\n        setRedirectAfterLogin(null); // Réinitialiser l'URL de redirection\n        return Promise.resolve();\n    };\n    const isAuthentiacted = !!user; // Vérifier si l'utilisateur est authentifié\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            isAuthenticated: isAuthentiacted,\n            loading,\n            setRedirectAfterLogin,\n            redirectAfterLogin,\n            login,\n            logout,\n            checkAuthStatus,\n            forceLogout\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\services\\\\AuthContext.tsx\",\n        lineNumber: 184,\n        columnNumber: 9\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3NlcnZpY2VzL0F1dGhDb250ZXh0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUNnQztBQUN3QztBQUV4QjtBQUV3QjtBQUdqRSxNQUFNUSxlQUFlQyxRQUFRQyxHQUFHLENBQUNGLFlBQVksSUFBSSxxQ0FBcUM7QUFrQjdGLHNDQUFzQztBQUMvQixNQUFNRyw0QkFBY1Ysb0RBQWFBLENBQThCVyxXQUFXO0FBT2pGLG9FQUFvRTtBQUU3RCxNQUFNQyxlQUE0QyxDQUFDLEVBQUVDLFFBQVEsRUFBRTtJQUNsRSxNQUFNLENBQUNDLE1BQU1DLFFBQVEsR0FBR2IsK0NBQVFBLENBQW9CO0lBQ3BELE1BQU0sQ0FBQ2MsU0FBU0MsV0FBVyxHQUFHZiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNnQixvQkFBb0JDLHNCQUFzQixHQUFHakIsK0NBQVFBLENBQWdCO0lBQzVFLE1BQU0sQ0FBQ2tCLG1CQUFtQkMscUJBQXFCLEdBQUduQiwrQ0FBUUEsQ0FBd0I7SUFFbEYsc0NBQXNDO0lBQ3RDLE1BQU1vQixjQUFjbkIsa0RBQVdBO2lEQUFDO1lBQzVCb0IsUUFBUUMsSUFBSSxDQUFDO1lBQ2JULFFBQVE7WUFDUlQsb0VBQVlBO1lBQ1osSUFBSWMsbUJBQW1CO2dCQUNuQkssY0FBY0w7Z0JBQ2RDLHFCQUFxQjtZQUN6QjtZQUNBLElBQUksS0FBNkIsRUFBRSxFQUVsQztRQUNMO2dEQUFHO1FBQUNEO0tBQWtCO0lBRXRCLHNEQUFzRDtJQUN0RCxNQUFNUyxrQkFBa0IxQixrREFBV0E7cURBQUM7WUFDaEMsSUFBSTtnQkFDQSw4QkFBOEI7Z0JBQzlCLE1BQU0yQixRQUFRL0IsaURBQU9BLENBQUNnQyxHQUFHLENBQUM7Z0JBQzFCLElBQUksQ0FBQ0QsT0FBTztvQkFDUlI7b0JBQ0E7Z0JBQ0o7Z0JBRUEsa0NBQWtDO2dCQUNsQyxJQUFJakIsc0VBQWNBLElBQUk7b0JBQ2xCa0IsUUFBUUMsSUFBSSxDQUFDO29CQUNiRjtvQkFDQTtnQkFDSjtnQkFFQSwyQkFBMkI7Z0JBQzNCLE1BQU1VLGNBQWMsTUFBTTVCLDZEQUFjQTtnQkFDeEMsSUFBSSxDQUFDNEIsYUFBYTtvQkFDZFY7b0JBQ0E7Z0JBQ0o7Z0JBRUFQLFFBQVFpQjtZQUNaLEVBQUUsT0FBT0MsT0FBTztnQkFDWlYsUUFBUVUsS0FBSyxDQUFDLHNCQUFzQkE7Z0JBQ3BDWDtZQUNKO1FBQ0o7b0RBQUc7UUFBQ0E7S0FBWTtJQUVoQiwrQ0FBK0M7SUFDL0NyQixnREFBU0E7a0NBQUM7WUFDTixNQUFNaUM7NERBQW9CO29CQUN0QixJQUFJO3dCQUNBLDBEQUEwRDt3QkFDMUQsTUFBTUosUUFBUS9CLGlEQUFPQSxDQUFDZ0MsR0FBRyxDQUFDO3dCQUMxQixJQUFJLENBQUNELFNBQVN6QixzRUFBY0EsSUFBSTs0QkFDNUJVLFFBQVE7NEJBQ1JFLFdBQVc7NEJBQ1g7d0JBQ0o7d0JBRUEsTUFBTUgsT0FBMEIsTUFBTVYsNkRBQWNBO3dCQUNwRCxJQUFJVSxNQUFNOzRCQUNOQyxRQUFRRDs0QkFFUixzQ0FBc0M7NEJBQ3RDLE1BQU1xQixXQUFXQyxZQUFZUCxpQkFBaUIsUUFBUSw4QkFBOEI7NEJBQ3BGUixxQkFBcUJjO3dCQUN6QixPQUFPOzRCQUNIcEMsaURBQU9BLENBQUNzQyxNQUFNLENBQUM7NEJBQ2Z0QixRQUFRO3dCQUNaO29CQUVKLEVBQUUsT0FBT2tCLE9BQU87d0JBQ1pWLFFBQVFVLEtBQUssQ0FBQywwQkFBMEJBO3dCQUN4Q2xDLGlEQUFPQSxDQUFDc0MsTUFBTSxDQUFDO3dCQUNmdEIsUUFBUTtvQkFDWixTQUFVO3dCQUNORSxXQUFXO29CQUNmO2dCQUNKOztZQUNBaUI7WUFFQSw4QkFBOEI7WUFDOUI7MENBQU87b0JBQ0gsSUFBSWQsbUJBQW1CO3dCQUNuQkssY0FBY0w7b0JBQ2xCO2dCQUNKOztRQUNKO2lDQUFHO1FBQUNTO1FBQWlCVDtLQUFrQjtJQUV2QyxNQUFNa0IsUUFBUSxPQUFPQyxPQUFlQyxVQUFrQkMsWUFBcUJDO1FBQ3ZFLElBQUk7WUFDQSxNQUFNQyxXQUFXLE1BQU1DLE1BQU0sR0FBR3JDLGFBQWEsV0FBVyxDQUFDLEVBQUU7Z0JBQ3ZEc0MsUUFBUTtnQkFDUkMsU0FBUztvQkFDTCxnQkFBZ0I7Z0JBQ3BCO2dCQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQ2pCVixPQUFPQTtvQkFDUEMsVUFBVUE7Z0JBQ2Q7WUFDSjtZQUVBLE1BQU1VLE9BQU8sTUFBTVAsU0FBU1EsSUFBSTtZQUNoQyxJQUFJLENBQUNSLFNBQVNTLEVBQUUsRUFBRTtnQkFDZDdCLFFBQVFVLEtBQUssQ0FBQyxnQkFBZ0JpQixLQUFLRyxPQUFPLElBQUk7Z0JBQzlDLE1BQU0sSUFBSUMsTUFBTUosS0FBS0csT0FBTyxJQUFJO1lBQ3BDO1lBRUEsTUFBTSxFQUFFRSxPQUFPLEVBQUUsR0FBR0w7WUFDcEIsSUFBSSxDQUFDSyxTQUFTO2dCQUNWLE1BQU0sSUFBSUQsTUFBTTtZQUNwQjtZQUVBLG9DQUFvQztZQUNwQ3ZELGlEQUFPQSxDQUFDeUQsR0FBRyxDQUFDLFdBQVdELFNBQVM7Z0JBQUVFLFNBQVNoQixhQUFhLEtBQUs7WUFBRSxJQUFJLHNCQUFzQjtZQUV6RixNQUFNM0IsT0FBMEIsTUFBTVYsNkRBQWNBLElBQUksOEVBQThFO1lBQ3RJLElBQUlVLE1BQU07Z0JBQ05DLFFBQVFEO1lBQ1o7WUFFQSxtREFBbUQ7WUFDbkQsSUFBSTRCLGFBQWE7Z0JBQ2J2QixzQkFBc0J1QjtZQUMxQjtRQUVKLEVBQUUsT0FBT1QsT0FBTztZQUNaVixRQUFRVSxLQUFLLENBQUMsaUJBQWlCQTtZQUMvQixNQUFNQTtRQUNWO0lBQ0o7SUFFQSxNQUFNeUIsU0FBUztRQUNYM0MsUUFBUTtRQUNSaEIsaURBQU9BLENBQUNzQyxNQUFNLENBQUMsWUFBWSxpQ0FBaUM7UUFDNURsQixzQkFBc0IsT0FBTyxxQ0FBcUM7UUFDbEUsT0FBT3dDLFFBQVFDLE9BQU87SUFDMUI7SUFFQSxNQUFNQyxrQkFBa0IsQ0FBQyxDQUFDL0MsTUFBTSw0Q0FBNEM7SUFFNUUscUJBQ0ksOERBQUNKLFlBQVlvRCxRQUFRO1FBQUNDLE9BQU87WUFDekJqRDtZQUNBa0QsaUJBQWlCSDtZQUNqQjdDO1lBQ0FHO1lBQ0FEO1lBQ0FvQjtZQUNBb0I7WUFDQTdCO1lBQ0FQO1FBQ0o7a0JBQ0tUOzs7Ozs7QUFHYixFQUFDIiwic291cmNlcyI6WyJEOlxcUHJvamV0XFxzY2hvbGFyaWZ5XFxkYXNoYm9hcmRcXHNyY1xcYXBwXFxzZXJ2aWNlc1xcQXV0aENvbnRleHQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuaW1wb3J0IENvb2tpZXMgZnJvbSBcImpzLWNvb2tpZVwiO1xyXG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0LCB1c2VFZmZlY3QsIHVzZVN0YXRlLCB1c2VDYWxsYmFjayB9IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgeyBqd3REZWNvZGUgfSBmcm9tIFwiand0LWRlY29kZVwiO1xyXG5pbXBvcnQgeyBnZXRDdXJyZW50VXNlciB9IGZyb20gXCIuL1VzZXJTZXJ2aWNlc1wiO1xyXG5pbXBvcnQgeyBVc2VyU2NoZW1hIH0gZnJvbSBcIi4uL21vZGVscy9Vc2VyTW9kZWxcIjtcclxuaW1wb3J0IHsgaXNUb2tlbkV4cGlyZWQsIGNsZWFyU2Vzc2lvbiB9IGZyb20gXCIuLi91dGlscy9odHRwSW50ZXJjZXB0b3JcIjtcclxuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSBcIm5leHQvbmF2aWdhdGlvblwiO1xyXG5cclxuZXhwb3J0IGNvbnN0IEJBU0VfQVBJX1VSTCA9IHByb2Nlc3MuZW52LkJBU0VfQVBJX1VSTCB8fCBcImh0dHBzOi8vc2NvbGFyaWZ5Lm9ucmVuZGVyLmNvbS9hcGlcIjsgXHJcblxyXG5cclxuXHJcbi8vIENvbnRleHQgZm9yIGF1dGhlbnRpY2F0aW9uIGFuZCB1c2VyIGRhdGFcclxuXHJcbmludGVyZmFjZSBBdXRoQ29udGV4dFR5cGUge1xyXG4gICAgdXNlcjogVXNlclNjaGVtYSB8IG51bGw7XHJcbiAgICBpc0F1dGhlbnRpY2F0ZWQ6IGJvb2xlYW47XHJcbiAgICBsb2FkaW5nOiBib29sZWFuO1xyXG4gICAgbG9naW46IChlbWFpbDogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nLCByZW1lbWJlck1lOiBib29sZWFuLCByZWRpcmVjdFVybD86IHN0cmluZykgPT4gUHJvbWlzZTx2b2lkPjtcclxuICAgIGxvZ291dDogKCkgPT4gUHJvbWlzZTx2b2lkPjtcclxuICAgIHJlZGlyZWN0QWZ0ZXJMb2dpbjogc3RyaW5nIHwgbnVsbDtcclxuICAgIHNldFJlZGlyZWN0QWZ0ZXJMb2dpbjogKHVybDogc3RyaW5nKSA9PiB2b2lkO1xyXG4gICAgY2hlY2tBdXRoU3RhdHVzOiAoKSA9PiBQcm9taXNlPHZvaWQ+O1xyXG4gICAgZm9yY2VMb2dvdXQ6ICgpID0+IHZvaWQ7XHJcbn1cclxuXHJcbi8vIENyZWF0ZSBhIGNvbnRleHQgZm9yIGF1dGhlbnRpY2F0aW9uXHJcbmV4cG9ydCBjb25zdCBBdXRoQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8QXV0aENvbnRleHRUeXBlIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpO1xyXG5cclxuLy8gSW50ZXJmYWNlIGZvciB0aGUgcHJvdmlkZXIgcHJvcHNcclxuaW50ZXJmYWNlIEF1dGhQcm92aWRlclByb3BzIHtcclxuICAgIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XHJcbn1cclxuXHJcbi8vIGNvbXBvc2FudCBBdXRoUHJvdmlkZXIgcXVpIGZvdXJuaXQgbGUgY29udGV4dGUgZCdhdXRoZW50aWZpY2F0aW9uXHJcblxyXG5leHBvcnQgY29uc3QgQXV0aFByb3ZpZGVyOiBSZWFjdC5GQzxBdXRoUHJvdmlkZXJQcm9wcz4gPSAoeyBjaGlsZHJlbiB9KSA9PiB7XHJcbiAgICBjb25zdCBbdXNlciwgc2V0VXNlcl0gPSB1c2VTdGF0ZTxVc2VyU2NoZW1hIHwgbnVsbD4obnVsbCk7XHJcbiAgICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcclxuICAgIGNvbnN0IFtyZWRpcmVjdEFmdGVyTG9naW4sIHNldFJlZGlyZWN0QWZ0ZXJMb2dpbl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcclxuICAgIGNvbnN0IFthdXRoQ2hlY2tJbnRlcnZhbCwgc2V0QXV0aENoZWNrSW50ZXJ2YWxdID0gdXNlU3RhdGU8Tm9kZUpTLlRpbWVvdXQgfCBudWxsPihudWxsKTtcclxuXHJcbiAgICAvLyBGb25jdGlvbiBwb3VyIGZvcmNlciBsYSBkw6ljb25uZXhpb25cclxuICAgIGNvbnN0IGZvcmNlTG9nb3V0ID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xyXG4gICAgICAgIGNvbnNvbGUud2FybihcIkZvcmNlIGxvZ291dCB0cmlnZ2VyZWRcIik7XHJcbiAgICAgICAgc2V0VXNlcihudWxsKTtcclxuICAgICAgICBjbGVhclNlc3Npb24oKTtcclxuICAgICAgICBpZiAoYXV0aENoZWNrSW50ZXJ2YWwpIHtcclxuICAgICAgICAgICAgY2xlYXJJbnRlcnZhbChhdXRoQ2hlY2tJbnRlcnZhbCk7XHJcbiAgICAgICAgICAgIHNldEF1dGhDaGVja0ludGVydmFsKG51bGwpO1xyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcclxuICAgICAgICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSAnL2xvZ2luJztcclxuICAgICAgICB9XHJcbiAgICB9LCBbYXV0aENoZWNrSW50ZXJ2YWxdKTtcclxuXHJcbiAgICAvLyBGb25jdGlvbiBwb3VyIHbDqXJpZmllciBsZSBzdGF0dXQgZCdhdXRoZW50aWZpY2F0aW9uXHJcbiAgICBjb25zdCBjaGVja0F1dGhTdGF0dXMgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgLy8gVsOpcmlmaWVyIHNpIGxlIHRva2VuIGV4aXN0ZVxyXG4gICAgICAgICAgICBjb25zdCB0b2tlbiA9IENvb2tpZXMuZ2V0KFwiaWRUb2tlblwiKTtcclxuICAgICAgICAgICAgaWYgKCF0b2tlbikge1xyXG4gICAgICAgICAgICAgICAgZm9yY2VMb2dvdXQoKTtcclxuICAgICAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLy8gVsOpcmlmaWVyIHNpIGxlIHRva2VuIGVzdCBleHBpcsOpXHJcbiAgICAgICAgICAgIGlmIChpc1Rva2VuRXhwaXJlZCgpKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oXCJUb2tlbiBleHBpcmVkLCBsb2dnaW5nIG91dFwiKTtcclxuICAgICAgICAgICAgICAgIGZvcmNlTG9nb3V0KCk7XHJcbiAgICAgICAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIC8vIFbDqXJpZmllciBhdmVjIGxlIHNlcnZldXJcclxuICAgICAgICAgICAgY29uc3QgY3VycmVudFVzZXIgPSBhd2FpdCBnZXRDdXJyZW50VXNlcigpO1xyXG4gICAgICAgICAgICBpZiAoIWN1cnJlbnRVc2VyKSB7XHJcbiAgICAgICAgICAgICAgICBmb3JjZUxvZ291dCgpO1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICBzZXRVc2VyKGN1cnJlbnRVc2VyKTtcclxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFwiQXV0aCBjaGVjayBmYWlsZWQ6XCIsIGVycm9yKTtcclxuICAgICAgICAgICAgZm9yY2VMb2dvdXQoKTtcclxuICAgICAgICB9XHJcbiAgICB9LCBbZm9yY2VMb2dvdXRdKTtcclxuXHJcbiAgICAvLyB2w6lyaWZpZXIgc2kgdW4gdXRpbGlzYXRldXIgZXN0IGTDqWphIGNvbm5lY3TDqVxyXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgICAgICBjb25zdCBjaGVja1VzZXJMb2dnZWRJbiA9IGFzeW5jICgpID0+IHtcclxuICAgICAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgICAgIC8vIFbDqXJpZmllciBkJ2Fib3JkIHNpIGxlIHRva2VuIGV4aXN0ZSBldCBuJ2VzdCBwYXMgZXhwaXLDqVxyXG4gICAgICAgICAgICAgICAgY29uc3QgdG9rZW4gPSBDb29raWVzLmdldChcImlkVG9rZW5cIik7XHJcbiAgICAgICAgICAgICAgICBpZiAoIXRva2VuIHx8IGlzVG9rZW5FeHBpcmVkKCkpIHtcclxuICAgICAgICAgICAgICAgICAgICBzZXRVc2VyKG51bGwpO1xyXG4gICAgICAgICAgICAgICAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICBjb25zdCB1c2VyOiBVc2VyU2NoZW1hIHwgbnVsbCA9IGF3YWl0IGdldEN1cnJlbnRVc2VyKCk7XHJcbiAgICAgICAgICAgICAgICBpZiAodXNlcikge1xyXG4gICAgICAgICAgICAgICAgICAgIHNldFVzZXIodXNlcik7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIC8vIETDqW1hcnJlciBsYSB2w6lyaWZpY2F0aW9uIHDDqXJpb2RpcXVlXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgaW50ZXJ2YWwgPSBzZXRJbnRlcnZhbChjaGVja0F1dGhTdGF0dXMsIDYwMDAwKTsgLy8gVsOpcmlmaWVyIHRvdXRlcyBsZXMgbWludXRlc1xyXG4gICAgICAgICAgICAgICAgICAgIHNldEF1dGhDaGVja0ludGVydmFsKGludGVydmFsKTtcclxuICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgQ29va2llcy5yZW1vdmUoXCJpZFRva2VuXCIpO1xyXG4gICAgICAgICAgICAgICAgICAgIHNldFVzZXIobnVsbCk7XHJcbiAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIHZlcmlmeWluZyB0b2tlbjpcIiwgZXJyb3IpO1xyXG4gICAgICAgICAgICAgICAgQ29va2llcy5yZW1vdmUoXCJpZFRva2VuXCIpO1xyXG4gICAgICAgICAgICAgICAgc2V0VXNlcihudWxsKTtcclxuICAgICAgICAgICAgfSBmaW5hbGx5IHtcclxuICAgICAgICAgICAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfTtcclxuICAgICAgICBjaGVja1VzZXJMb2dnZWRJbigpO1xyXG5cclxuICAgICAgICAvLyBDbGVhbnVwIGludGVydmFsIG9uIHVubW91bnRcclxuICAgICAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICAgICAgICBpZiAoYXV0aENoZWNrSW50ZXJ2YWwpIHtcclxuICAgICAgICAgICAgICAgIGNsZWFySW50ZXJ2YWwoYXV0aENoZWNrSW50ZXJ2YWwpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfTtcclxuICAgIH0sIFtjaGVja0F1dGhTdGF0dXMsIGF1dGhDaGVja0ludGVydmFsXSk7XHJcblxyXG4gICAgY29uc3QgbG9naW4gPSBhc3luYyAoZW1haWw6IHN0cmluZywgcGFzc3dvcmQ6IHN0cmluZywgcmVtZW1iZXJNZTogYm9vbGVhbiwgcmVkaXJlY3RVcmw/OiBzdHJpbmcpID0+IHtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke0JBU0VfQVBJX1VSTH0vYXV0aC9sb2dpbmAsIHtcclxuICAgICAgICAgICAgICAgIG1ldGhvZDogXCJQT1NUXCIsXHJcbiAgICAgICAgICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xyXG4gICAgICAgICAgICAgICAgICAgIGVtYWlsOiBlbWFpbCxcclxuICAgICAgICAgICAgICAgICAgICBwYXNzd29yZDogcGFzc3dvcmQsXHJcbiAgICAgICAgICAgICAgICB9KSxcclxuICAgICAgICAgICAgfSk7XHJcblxyXG4gICAgICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICAgICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKFwiTG9naW4gZXJyb3I6XCIsIGRhdGEubWVzc2FnZSB8fCBcIlVua25vd24gZXJyb3JcIik7XHJcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZGF0YS5tZXNzYWdlIHx8IFwiTG9naW4gZmFpbGVkXCIpO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICBjb25zdCB7IGlkVG9rZW4gfSA9IGRhdGE7XHJcbiAgICAgICAgICAgIGlmICghaWRUb2tlbikge1xyXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiTm8gaWRUb2tlbiByZWNlaXZlZFwiKTtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLy8gU3RvY2tlciBsZSB0b2tlbiBkYW5zIGxlcyBjb29raWVzXHJcbiAgICAgICAgICAgIENvb2tpZXMuc2V0KFwiaWRUb2tlblwiLCBpZFRva2VuLCB7IGV4cGlyZXM6IHJlbWVtYmVyTWUgPyAzMCA6IDcgfSk7IC8vIEV4cGlyZSBkYW5zIDcgam91cnNcclxuXHJcbiAgICAgICAgICAgIGNvbnN0IHVzZXI6IFVzZXJTY2hlbWEgfCBudWxsID0gYXdhaXQgZ2V0Q3VycmVudFVzZXIoKTsgLy8gVsOpcmlmaWVyIHNpIGwndXRpbGlzYXRldXIgZXN0IGNvbm5lY3TDqSDDoCBub3V2ZWF1IGFwcsOocyBsYSBjb25uZXhpb24gcsOpdXNzaWVcclxuICAgICAgICAgICAgaWYgKHVzZXIpIHtcclxuICAgICAgICAgICAgICAgIHNldFVzZXIodXNlcik7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIC8vIFNpIHVuZSBVUkwgZGUgcmVkaXJlY3Rpb24gZXN0IGZvdXJuaWUsIHN0b2NrZS1sYVxyXG4gICAgICAgICAgICBpZiAocmVkaXJlY3RVcmwpIHtcclxuICAgICAgICAgICAgICAgIHNldFJlZGlyZWN0QWZ0ZXJMb2dpbihyZWRpcmVjdFVybCk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgXHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIkxvZ2luIGZhaWxlZDpcIiwgZXJyb3IpO1xyXG4gICAgICAgICAgICB0aHJvdyBlcnJvcjtcclxuICAgICAgICB9XHJcbiAgICB9O1xyXG5cclxuICAgIGNvbnN0IGxvZ291dCA9IGFzeW5jICgpID0+IHtcclxuICAgICAgICBzZXRVc2VyKG51bGwpO1xyXG4gICAgICAgIENvb2tpZXMucmVtb3ZlKFwiaWRUb2tlblwiKTsgLy8gU3VwcHJpbWVyIGxlIHRva2VuIGRlcyBjb29raWVzXHJcbiAgICAgICAgc2V0UmVkaXJlY3RBZnRlckxvZ2luKG51bGwpOyAvLyBSw6lpbml0aWFsaXNlciBsJ1VSTCBkZSByZWRpcmVjdGlvblxyXG4gICAgICAgIHJldHVybiBQcm9taXNlLnJlc29sdmUoKTtcclxuICAgIH07XHJcblxyXG4gICAgY29uc3QgaXNBdXRoZW50aWFjdGVkID0gISF1c2VyOyAvLyBWw6lyaWZpZXIgc2kgbCd1dGlsaXNhdGV1ciBlc3QgYXV0aGVudGlmacOpXHJcblxyXG4gICAgcmV0dXJuIChcclxuICAgICAgICA8QXV0aENvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3tcclxuICAgICAgICAgICAgdXNlcixcclxuICAgICAgICAgICAgaXNBdXRoZW50aWNhdGVkOiBpc0F1dGhlbnRpYWN0ZWQsXHJcbiAgICAgICAgICAgIGxvYWRpbmcsXHJcbiAgICAgICAgICAgIHNldFJlZGlyZWN0QWZ0ZXJMb2dpbixcclxuICAgICAgICAgICAgcmVkaXJlY3RBZnRlckxvZ2luLFxyXG4gICAgICAgICAgICBsb2dpbixcclxuICAgICAgICAgICAgbG9nb3V0LFxyXG4gICAgICAgICAgICBjaGVja0F1dGhTdGF0dXMsXHJcbiAgICAgICAgICAgIGZvcmNlTG9nb3V0XHJcbiAgICAgICAgfX0+XHJcbiAgICAgICAgICAgIHtjaGlsZHJlbn1cclxuICAgICAgICA8L0F1dGhDb250ZXh0LlByb3ZpZGVyPlxyXG4gICAgKTtcclxufSJdLCJuYW1lcyI6WyJDb29raWVzIiwiY3JlYXRlQ29udGV4dCIsInVzZUVmZmVjdCIsInVzZVN0YXRlIiwidXNlQ2FsbGJhY2siLCJnZXRDdXJyZW50VXNlciIsImlzVG9rZW5FeHBpcmVkIiwiY2xlYXJTZXNzaW9uIiwiQkFTRV9BUElfVVJMIiwicHJvY2VzcyIsImVudiIsIkF1dGhDb250ZXh0IiwidW5kZWZpbmVkIiwiQXV0aFByb3ZpZGVyIiwiY2hpbGRyZW4iLCJ1c2VyIiwic2V0VXNlciIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwicmVkaXJlY3RBZnRlckxvZ2luIiwic2V0UmVkaXJlY3RBZnRlckxvZ2luIiwiYXV0aENoZWNrSW50ZXJ2YWwiLCJzZXRBdXRoQ2hlY2tJbnRlcnZhbCIsImZvcmNlTG9nb3V0IiwiY29uc29sZSIsIndhcm4iLCJjbGVhckludGVydmFsIiwid2luZG93IiwibG9jYXRpb24iLCJocmVmIiwiY2hlY2tBdXRoU3RhdHVzIiwidG9rZW4iLCJnZXQiLCJjdXJyZW50VXNlciIsImVycm9yIiwiY2hlY2tVc2VyTG9nZ2VkSW4iLCJpbnRlcnZhbCIsInNldEludGVydmFsIiwicmVtb3ZlIiwibG9naW4iLCJlbWFpbCIsInBhc3N3b3JkIiwicmVtZW1iZXJNZSIsInJlZGlyZWN0VXJsIiwicmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsImRhdGEiLCJqc29uIiwib2siLCJtZXNzYWdlIiwiRXJyb3IiLCJpZFRva2VuIiwic2V0IiwiZXhwaXJlcyIsImxvZ291dCIsIlByb21pc2UiLCJyZXNvbHZlIiwiaXNBdXRoZW50aWFjdGVkIiwiUHJvdmlkZXIiLCJ2YWx1ZSIsImlzQXV0aGVudGljYXRlZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/services/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/services/UserServices.tsx":
/*!*******************************************!*\
  !*** ./src/app/services/UserServices.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   deleteAllUsers: () => (/* binding */ deleteAllUsers),\n/* harmony export */   deleteMultipleUsers: () => (/* binding */ deleteMultipleUsers),\n/* harmony export */   deleteUser: () => (/* binding */ deleteUser),\n/* harmony export */   forget_password: () => (/* binding */ forget_password),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getMonthlyUserStarts: () => (/* binding */ getMonthlyUserStarts),\n/* harmony export */   getParents: () => (/* binding */ getParents),\n/* harmony export */   getTokenFromCookie: () => (/* binding */ getTokenFromCookie),\n/* harmony export */   getTotalUsers: () => (/* binding */ getTotalUsers),\n/* harmony export */   getUserById: () => (/* binding */ getUserById),\n/* harmony export */   getUserBy_id: () => (/* binding */ getUserBy_id),\n/* harmony export */   getUserCountWithChange: () => (/* binding */ getUserCountWithChange),\n/* harmony export */   getUsers: () => (/* binding */ getUsers),\n/* harmony export */   handleUserSearch: () => (/* binding */ handleUserSearch),\n/* harmony export */   registerParent: () => (/* binding */ registerParent),\n/* harmony export */   resend_Code: () => (/* binding */ resend_Code),\n/* harmony export */   resetParentPasswordService: () => (/* binding */ resetParentPasswordService),\n/* harmony export */   reset_password: () => (/* binding */ reset_password),\n/* harmony export */   updateUser: () => (/* binding */ updateUser),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verify_otp: () => (/* binding */ verify_otp)\n/* harmony export */ });\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var jwt_decode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jwt-decode */ \"(ssr)/./node_modules/jwt-decode/build/esm/index.js\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"(ssr)/./src/app/services/AuthContext.tsx\");\n/* harmony import */ var _utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/httpInterceptor */ \"(ssr)/./src/app/utils/httpInterceptor.tsx\");\n\n\n\n\nfunction getTokenFromCookie(name) {\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(name);\n    return token;\n}\nasync function getCurrentUser() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        if (!token) {\n            return null;\n        }\n        const decodedUser = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_1__.jwtDecode)(token);\n        const email = decodedUser.email;\n        const response = await (0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_3__.authenticatedGet)(`/user/get-user-email/${email}`);\n        if (!response.ok) {\n            console.error(\"Error fetching user:\", response.statusText);\n            return null;\n        }\n        const user = await response.json();\n        return user;\n    } catch (error) {\n        console.error(\"Error fetching current user:\", error);\n        return null;\n    }\n}\nasync function getParents() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/get-parents`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${token}`\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching parents:\", response.statusText);\n            throw new Error(\"Failed to fetch parents data\");\n        }\n        const parentsList = await response.json();\n        const parents = parentsList.map((user)=>({\n                _id: user._id,\n                user_id: user.user_id,\n                firebaseUid: user.firebaseUid,\n                name: user.name,\n                email: user.email,\n                phone: user.phone,\n                role: user.role,\n                avatar: user.avatar,\n                address: user.address,\n                school_ids: user.school_ids,\n                student_ids: user.student_ids,\n                isVerified: user.isVerified,\n                verificationCode: user.verificationCode,\n                verificationCodeExpires: user.verificationCodeExpires,\n                lastLogin: user.lastLogin,\n                createdAt: user.createdAt,\n                updatedAt: user.updatedAt\n            }));\n        return parents;\n    } catch (error) {\n        console.error(\"Error fetching parents:\", error);\n        throw new Error(\"Failed to fetch parents data\");\n    }\n}\nasync function getUsers() {\n    try {\n        const token = getTokenFromCookie(\"idToken\"); // Assuming this function gets the token from cookies\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/get-users`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${token}`\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching users:\", response.statusText);\n            throw new Error(\"Failed to fetch users data\");\n        }\n        const usersList = await response.json();\n        const users = usersList.map((user)=>{\n            return {\n                _id: user._id,\n                user_id: user.user_id,\n                firebaseUid: user.firebaseUid,\n                name: user.name,\n                email: user.email,\n                phone: user.phone,\n                role: user.role,\n                avatar: user.avatar,\n                address: user.address,\n                school_ids: user.school_ids,\n                isVerified: user.isVerified,\n                verificationCode: user.verificationCode,\n                verificationCodeExpires: user.verificationCodeExpires,\n                lastLogin: user.lastLogin,\n                createdAt: user.createdAt,\n                updatedAt: user.updatedAt\n            };\n        });\n        return users;\n    } catch (error) {\n        console.error(\"Error fetching users:\", error);\n        throw new Error(\"Failed to fetch users data\");\n    }\n}\nasync function createUser(userData) {\n    try {\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/register-user`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${getTokenFromCookie(\"idToken\")}`\n            },\n            body: JSON.stringify(userData)\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to create user data\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = errorBody?.message || errorMessage;\n            } catch (parseError) {\n                // If parsing the error body fails, use default message\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            console.error(\"Error creating user:\", errorMessage);\n            throw new Error(errorMessage);\n        }\n        const data = await response.json(); // Parse the response as JSON\n        return data; // Return the response data (usually the created user object)\n    } catch (error) {\n        console.error(\"Error creating user:\", error);\n        throw new Error(error instanceof Error ? error.message : \"Failed to create user data\");\n    }\n}\nasync function updateUser(user_id, userData) {\n    try {\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/update-user/${user_id}`, {\n            method: \"PUT\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${getTokenFromCookie(\"idToken\")}`\n            },\n            body: JSON.stringify(userData)\n        });\n        if (!response.ok) {\n            console.error(\"Error updating user:\", response.statusText);\n            throw new Error(\"Failed to update user data\");\n        }\n        const data = await response.json(); // Parse the response as JSON\n        return data; // Return the updated user data (this could be the user object with updated fields)\n    } catch (error) {\n        console.error(\"Error updating user:\", error);\n        throw new Error(\"Failed to update user data\");\n    }\n}\nasync function getUserById(userId) {\n    const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/get-user/${userId}`, {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${getTokenFromCookie(\"idToken\")}`\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching user:\", response.statusText);\n        throw new Error(\"Failed to fetch user data\");\n    }\n    const data = await response.json();\n    const user = {\n        _id: data._id,\n        user_id: data.user_id,\n        name: data.name,\n        email: data.email,\n        phone: data.phone,\n        role: data.role,\n        address: data.address,\n        school_ids: data.school_ids,\n        isVerified: data.isVerified,\n        description: data.description,\n        firebaseUid: data.firebaseUid,\n        createdAt: data.createdAt,\n        updatedAt: data.updatedAt\n    };\n    return user;\n}\nasync function verifyPassword(password, email) {\n    const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/auth/verify-password`, {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n            email: email,\n            password: password\n        })\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching user:\", response.statusText);\n        return false;\n    }\n    return true;\n}\nasync function deleteUser(user_id) {\n    try {\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/delete-user/${user_id}`, {\n            method: \"DELETE\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${getTokenFromCookie(\"idToken\")}`\n            }\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to delete user\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = errorBody?.message || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            console.error(\"Error deleting user:\", errorMessage);\n            throw new Error(errorMessage);\n        }\n        const result = await response.json();\n        return result; // Might return a success message or deleted user data\n    } catch (error) {\n        console.error(\"Error deleting user:\", error);\n        throw new Error(error instanceof Error ? error.message : \"Failed to delete user\");\n    }\n}\nasync function getUserBy_id(_id) {\n    const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/get-user-by-id/${_id}`, {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${getTokenFromCookie(\"idToken\")}`\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching user:\", response.statusText);\n        throw new Error(\"Failed to fetch user data\");\n    }\n    const data = await response.json();\n    const user = {\n        _id: data._id,\n        user_id: data.user_id,\n        name: data.name,\n        email: data.email,\n        phone: data.phone,\n        role: data.role,\n        address: data.address,\n        school_ids: data.school_ids,\n        isVerified: data.isVerified,\n        description: data.description,\n        firebaseUid: data.firebaseUid,\n        createdAt: data.createdAt,\n        updatedAt: data.updatedAt\n    };\n    return user;\n}\nasync function forget_password(email) {\n    try {\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/auth/forgot-password`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email: email\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to send reset password email: check your email\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = errorBody?.message || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error sending reset password email:\", error);\n        throw new Error(\"Failed to send reset password email\");\n    }\n}\nasync function verify_otp(code, email) {\n    try {\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/auth/verify-code`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                code: code,\n                email: email\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to verify OTP\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = errorBody?.message || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error verifying OTP:\", error);\n        throw new Error(\"Failed to verify OTP\");\n    }\n}\nasync function resend_Code(email) {\n    try {\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/auth/resend-code`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email: email\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to resend code\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = errorBody?.message || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error resending code:\", error);\n        throw new Error(\"Failed to resend code\");\n    }\n}\nasync function reset_password(newPassword, email, code) {\n    try {\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/auth/reset-password`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email: email,\n                code: code,\n                newPassword: newPassword\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to reset password\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = errorBody?.message || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error resetting password:\", error);\n        throw new Error(\"Failed to reset password\");\n    }\n}\nasync function handleUserSearch(query) {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/search-users?query=${encodeURIComponent(query)}`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${token}`\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error searching users:\", response.statusText);\n            throw new Error(\"Failed to search users\");\n        }\n        const results = await response.json();\n        const users = results.map((user)=>{\n            return {\n                _id: user._id,\n                user_id: user.user_id,\n                firebaseUid: user.firebaseUid,\n                name: user.name,\n                email: user.email,\n                phone: user.phone,\n                role: user.role,\n                avatar: user.avatar,\n                address: user.address,\n                school_ids: user.school_ids,\n                isVerified: user.isVerified,\n                verificationCode: user.verificationCode,\n                verificationCodeExpires: user.verificationCodeExpires,\n                lastLogin: user.lastLogin,\n                createdAt: user.createdAt,\n                updatedAt: user.updatedAt\n            };\n        });\n        return users;\n    } catch (error) {\n        console.error(\"Error searching users:\", error);\n        throw new Error(\"Failed to search users\");\n    }\n}\nasync function registerParent(parentData) {\n    try {\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/register-parent`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${getTokenFromCookie(\"idToken\")}`\n            },\n            body: JSON.stringify(parentData)\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to register parent\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = errorBody?.message || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            console.error(\"Error registering parent:\", errorMessage);\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data; // This includes user object and generatedPassword (if new)\n    } catch (error) {\n        console.error(\"Error in registerParent service:\", error);\n        throw new Error(error instanceof Error ? error.message : \"Failed to register parent\");\n    }\n}\nasync function deleteMultipleUsers(userIds) {\n    const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/delete-users`, {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${getTokenFromCookie(\"idToken\")}`\n        },\n        body: JSON.stringify({\n            ids: userIds\n        })\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting multiple users:\", response.statusText);\n        throw new Error(\"Failed to delete multiple users\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function deleteAllUsers() {\n    const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/delete-all-users`, {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${getTokenFromCookie(\"idToken\")}`\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting all users:\", response.statusText);\n        throw new Error(\"Failed to delete all users\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function getTotalUsers() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/total-users`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${token}`\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching total users:\", response.statusText);\n            throw new Error(\"Failed to fetch total users\");\n        }\n        const data = await response.json();\n        return data.totalUsers;\n    } catch (error) {\n        console.error(\"Error fetching total users:\", error);\n        throw error;\n    }\n}\nasync function getUserCountWithChange() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/users-count-change`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${token}`\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching user count change:\", response.statusText);\n            throw new Error(\"Failed to fetch user count change\");\n        }\n        const data = await response.json();\n        return {\n            totalUsersThisMonth: data.totalUsersThisMonth,\n            percentageChange: data.percentageChange\n        };\n    } catch (error) {\n        console.error(\"Error fetching user count change:\", error);\n        throw error;\n    }\n}\nasync function getMonthlyUserStarts() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/monthly-user-starts`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${token}`\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Failed to fetch monthly user stats:\", response.statusText);\n            throw new Error(\"Failed to fetch monthly user stats\");\n        }\n        const data = await response.json();\n        // data has type { [year: string]: MonthlyUserStat[] }\n        return data;\n    } catch (error) {\n        console.error(\"Error fetching monthly user stats:\", error);\n        throw error;\n    }\n}\nasync function resetParentPasswordService({ email, phone }) {\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"idToken\");\n    if (!token) {\n        throw new Error(\"You must be logged in to perform this action.\");\n    }\n    if (!email && !phone) {\n        throw new Error(\"Email or phone number is required to reset password.\");\n    }\n    const payload = {};\n    if (email) payload.email = email;\n    if (phone) payload.phone = phone;\n    const response = await fetch(`${_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL}/user/reset-parent-password`, {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${token}`\n        },\n        body: JSON.stringify(payload)\n    });\n    if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || \"Failed to reset password\");\n    }\n    const result = await response.json();\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/services/UserServices.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/utils/httpInterceptor.tsx":
/*!*******************************************!*\
  !*** ./src/app/utils/httpInterceptor.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticatedDelete: () => (/* binding */ authenticatedDelete),\n/* harmony export */   authenticatedFetch: () => (/* binding */ authenticatedFetch),\n/* harmony export */   authenticatedGet: () => (/* binding */ authenticatedGet),\n/* harmony export */   authenticatedPost: () => (/* binding */ authenticatedPost),\n/* harmony export */   authenticatedPut: () => (/* binding */ authenticatedPut),\n/* harmony export */   checkAuthStatus: () => (/* binding */ checkAuthStatus),\n/* harmony export */   clearSession: () => (/* binding */ clearSession),\n/* harmony export */   isTokenExpired: () => (/* binding */ isTokenExpired)\n/* harmony export */ });\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _services_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/AuthContext */ \"(ssr)/./src/app/services/AuthContext.tsx\");\n\n\n// Fonction pour gérer la déconnexion automatique\nconst handleUnauthorized = ()=>{\n    // Supprimer le token\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"idToken\");\n    // Rediriger vers login\n    if (false) {}\n};\n// Fonction pour obtenir le token\nconst getAuthToken = ()=>{\n    return js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"idToken\") || null;\n};\n// Intercepteur HTTP personnalisé\nconst authenticatedFetch = async (url, options = {})=>{\n    const token = getAuthToken();\n    // Si pas de token, rediriger immédiatement\n    if (!token) {\n        handleUnauthorized();\n        throw new Error(\"No authentication token found\");\n    }\n    // Ajouter le token aux headers\n    const headers = {\n        'Content-Type': 'application/json',\n        ...options.headers,\n        'Authorization': `Bearer ${token}`\n    };\n    // Construire l'URL complète si nécessaire\n    const fullUrl = url.startsWith('http') ? url : `${_services_AuthContext__WEBPACK_IMPORTED_MODULE_1__.BASE_API_URL}${url}`;\n    try {\n        const response = await fetch(fullUrl, {\n            ...options,\n            headers\n        });\n        // Vérifier si la réponse indique une erreur d'authentification\n        if (response.status === 401) {\n            console.warn(\"Token expired or invalid, redirecting to login\");\n            handleUnauthorized();\n            throw new Error(\"Authentication failed\");\n        }\n        // Vérifier si la réponse indique un token expiré\n        if (response.status === 403) {\n            const errorData = await response.clone().json().catch(()=>({}));\n            if (errorData.message?.includes('token') || errorData.message?.includes('expired')) {\n                console.warn(\"Token expired, redirecting to login\");\n                handleUnauthorized();\n                throw new Error(\"Token expired\");\n            }\n        }\n        return response;\n    } catch (error) {\n        // Si erreur réseau ou autre, vérifier si c'est lié à l'auth\n        if (error instanceof TypeError && error.message.includes('fetch')) {\n            console.error(\"Network error during authenticated request:\", error);\n        }\n        throw error;\n    }\n};\n// Wrapper pour les requêtes GET\nconst authenticatedGet = async (url)=>{\n    return authenticatedFetch(url, {\n        method: 'GET'\n    });\n};\n// Wrapper pour les requêtes POST\nconst authenticatedPost = async (url, data)=>{\n    return authenticatedFetch(url, {\n        method: 'POST',\n        body: JSON.stringify(data)\n    });\n};\n// Wrapper pour les requêtes PUT\nconst authenticatedPut = async (url, data)=>{\n    return authenticatedFetch(url, {\n        method: 'PUT',\n        body: JSON.stringify(data)\n    });\n};\n// Wrapper pour les requêtes DELETE\nconst authenticatedDelete = async (url, data)=>{\n    const options = {\n        method: 'DELETE'\n    };\n    if (data) {\n        options.body = JSON.stringify(data);\n    }\n    return authenticatedFetch(url, options);\n};\n// Fonction pour vérifier si l'utilisateur est toujours authentifié\nconst checkAuthStatus = async ()=>{\n    const token = getAuthToken();\n    if (!token) {\n        return false;\n    }\n    try {\n        // Faire une requête simple pour vérifier le token\n        const response = await authenticatedGet('/user/check-auth');\n        return response.ok;\n    } catch (error) {\n        console.error(\"Auth check failed:\", error);\n        return false;\n    }\n};\n// Fonction pour décoder et vérifier l'expiration du token JWT\nconst isTokenExpired = ()=>{\n    const token = getAuthToken();\n    if (!token) {\n        return true;\n    }\n    try {\n        // Décoder le token JWT (partie payload)\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        const currentTime = Math.floor(Date.now() / 1000);\n        // Vérifier si le token est expiré\n        return payload.exp < currentTime;\n    } catch (error) {\n        console.error(\"Error decoding token:\", error);\n        return true;\n    }\n};\n// Fonction pour nettoyer la session\nconst clearSession = ()=>{\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"idToken\");\n    if (false) {}\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/utils/httpInterceptor.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AppLogo.tsx":
/*!************************************!*\
  !*** ./src/components/AppLogo.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AppLogo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n\n\nfunction AppLogo({ className, logoSrc, logoAlt }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center py-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                src: logoSrc,\n                alt: logoAlt,\n                width: 200,\n                height: 200\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\AppLogo.tsx\",\n                lineNumber: 13,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\AppLogo.tsx\",\n            lineNumber: 12,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\AppLogo.tsx\",\n        lineNumber: 11,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9BcHBMb2dvLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUErQjtBQVFoQixTQUFTQyxRQUFRLEVBQUVDLFNBQVMsRUFBRUMsT0FBTyxFQUFFQyxPQUFPLEVBQWdCO0lBQ3pFLHFCQUNJLDhEQUFDQztRQUFJSCxXQUFXQTtrQkFDWiw0RUFBQ0c7WUFBSUgsV0FBVTtzQkFDWCw0RUFBQ0Ysa0RBQUtBO2dCQUFDTSxLQUFLSDtnQkFBU0ksS0FBS0g7Z0JBQVNJLE9BQU87Z0JBQUtDLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJdkUiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZXRcXHNjaG9sYXJpZnlcXGRhc2hib2FyZFxcc3JjXFxjb21wb25lbnRzXFxBcHBMb2dvLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgSW1hZ2UgZnJvbSBcIm5leHQvaW1hZ2VcIjtcclxuXHJcbmludGVyZmFjZSBBcHBMb2dvUHJvcHMge1xyXG4gICAgY2xhc3NOYW1lPzogc3RyaW5nO1xyXG4gICAgbG9nb1NyYzogc3RyaW5nO1xyXG4gICAgbG9nb0FsdDogc3RyaW5nO1xyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHBMb2dvKHsgY2xhc3NOYW1lLCBsb2dvU3JjLCBsb2dvQWx0IH06IEFwcExvZ29Qcm9wcykge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17Y2xhc3NOYW1lfT5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktY2VudGVyIHB5LTZcIj5cclxuICAgICAgICAgICAgICAgIDxJbWFnZSBzcmM9e2xvZ29TcmN9IGFsdD17bG9nb0FsdH0gd2lkdGg9ezIwMH0gaGVpZ2h0PXsyMDB9IC8+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgKVxyXG59Il0sIm5hbWVzIjpbIkltYWdlIiwiQXBwTG9nbyIsImNsYXNzTmFtZSIsImxvZ29TcmMiLCJsb2dvQWx0IiwiZGl2Iiwic3JjIiwiYWx0Iiwid2lkdGgiLCJoZWlnaHQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AppLogo.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/FormHeading.tsx":
/*!****************************************!*\
  !*** ./src/components/FormHeading.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FormHeading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction FormHeading({ title, subtitle, formIcon, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center mb-4\",\n                children: formIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center mb-4\",\n                    children: formIcon\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\FormHeading.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\FormHeading.tsx\",\n                lineNumber: 12,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-2xl font-bold text-center text-gray-800 dark:text-[#17B890] mb-2\",\n                children: [\n                    title,\n                    \" \"\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\FormHeading.tsx\",\n                lineNumber: 20,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-center dark:text-gray-500 mb-6\",\n                children: [\n                    subtitle,\n                    \" \"\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\FormHeading.tsx\",\n                lineNumber: 21,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\FormHeading.tsx\",\n        lineNumber: 11,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Gb3JtSGVhZGluZy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQVFlLFNBQVNBLFlBQVksRUFBRUMsS0FBSyxFQUFFQyxRQUFRLEVBQUVDLFFBQVEsRUFBRSxHQUFHQyxPQUF3QjtJQUN4RixxQkFDSSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBQ1gsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNWSCwwQkFDRyw4REFBQ0U7b0JBQUlDLFdBQVU7OEJBQ1ZIOzs7Ozs7Ozs7OzswQkFLYiw4REFBQ0k7Z0JBQUdELFdBQVU7O29CQUF5RUw7b0JBQU07Ozs7Ozs7MEJBQzdGLDhEQUFDTztnQkFBRUYsV0FBVTs7b0JBQXVDSjtvQkFBUzs7Ozs7Ozs7Ozs7OztBQUd6RSIsInNvdXJjZXMiOlsiRDpcXFByb2pldFxcc2Nob2xhcmlmeVxcZGFzaGJvYXJkXFxzcmNcXGNvbXBvbmVudHNcXEZvcm1IZWFkaW5nLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcclxuaW50ZXJmYWNlIEZvcm1IZWFkaW5nUHJvcHMge1xyXG4gICAgdGl0bGU6IHN0cmluZztcclxuICAgIHN1YnRpdGxlOiBzdHJpbmc7XHJcbiAgICBmb3JtSWNvbj86IFJlYWN0LlJlYWN0Tm9kZTsgLy8gUG91ciBwYXNzZXIgdW5lIGljw7RuZSBTVkcgb3UgYXV0cmVcclxuXHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEZvcm1IZWFkaW5nKHsgdGl0bGUsIHN1YnRpdGxlLCBmb3JtSWNvbiwgLi4ucHJvcHN9OiBGb3JtSGVhZGluZ1Byb3BzICkge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIlwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXIgbWItNFwiPlxyXG4gICAgICAgICAgICAgICAge2Zvcm1JY29uICYmIChcclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXIgbWItNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybUljb259XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1jZW50ZXIgdGV4dC1ncmF5LTgwMCBkYXJrOnRleHQtWyMxN0I4OTBdIG1iLTJcIj57dGl0bGV9IDwvaDI+XHJcbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtY2VudGVyIGRhcms6dGV4dC1ncmF5LTUwMCBtYi02XCI+e3N1YnRpdGxlfSA8L3A+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICApXHJcbn0iXSwibmFtZXMiOlsiRm9ybUhlYWRpbmciLCJ0aXRsZSIsInN1YnRpdGxlIiwiZm9ybUljb24iLCJwcm9wcyIsImRpdiIsImNsYXNzTmFtZSIsImgyIiwicCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/FormHeading.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/NotificationCard.tsx":
/*!*********************************************!*\
  !*** ./src/components/NotificationCard.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n// Classe pour gérer les configurations des notifications\nclass NotificationConfig {\n    static getTitle(type) {\n        const titles = {\n            success: 'Success',\n            error: 'Error',\n            info: 'Information',\n            warning: 'Warning'\n        };\n        return titles[type];\n    }\n    static getIcon(type) {\n        switch(type){\n            case 'success':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 20 20\",\n                    fill: \"none\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM8 15L3 10L4.41 8.59L8 12.17L15.59 4.58L17 6L8 15Z\",\n                        fill: \"#15803d\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 11\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 20 20\",\n                    fill: \"none\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M8.4375 6.5625C8.4375 6.31527 8.51081 6.0736 8.64817 5.86804C8.78552 5.66248 8.98074 5.50226 9.20915 5.40765C9.43756 5.31304 9.68889 5.28829 9.93137 5.33652C10.1738 5.38475 10.3966 5.5038 10.5714 5.67862C10.7462 5.85343 10.8653 6.07616 10.9135 6.31864C10.9617 6.56111 10.937 6.81245 10.8424 7.04085C10.7477 7.26926 10.5875 7.46448 10.382 7.60184C10.1764 7.73919 9.93473 7.8125 9.6875 7.8125C9.35598 7.8125 9.03804 7.6808 8.80362 7.44638C8.5692 7.21196 8.4375 6.89402 8.4375 6.5625ZM18.4375 10C18.4375 11.6688 17.9427 13.3001 17.0155 14.6876C16.0884 16.0752 14.7706 17.1566 13.2289 17.7952C11.6871 18.4338 9.99064 18.6009 8.35393 18.2754C6.71721 17.9498 5.2138 17.1462 4.03379 15.9662C2.85378 14.7862 2.05019 13.2828 1.72463 11.6461C1.39907 10.0094 1.56616 8.31286 2.20477 6.77111C2.84338 5.22936 3.92484 3.9116 5.31238 2.98448C6.69992 2.05735 8.33122 1.5625 10 1.5625C12.237 1.56498 14.3817 2.45473 15.9635 4.03653C17.5453 5.61833 18.435 7.763 18.4375 10ZM16.5625 10C16.5625 8.70206 16.1776 7.43327 15.4565 6.35407C14.7354 5.27487 13.7105 4.43374 12.5114 3.93704C11.3122 3.44034 9.99272 3.31038 8.71972 3.5636C7.44672 3.81681 6.2774 4.44183 5.35962 5.35961C4.44183 6.27739 3.81682 7.44672 3.5636 8.71972C3.31038 9.99272 3.44034 11.3122 3.93704 12.5114C4.43374 13.7105 5.27488 14.7354 6.35407 15.4565C7.43327 16.1776 8.70206 16.5625 10 16.5625C11.7399 16.5606 13.408 15.8686 14.6383 14.6383C15.8686 13.408 16.5606 11.7399 16.5625 10ZM10.9375 12.8656V10.3125C10.9375 9.8981 10.7729 9.50067 10.4799 9.20764C10.1868 8.91462 9.7894 8.75 9.375 8.75C9.1536 8.74967 8.93923 8.82771 8.76986 8.97029C8.60048 9.11287 8.48703 9.31079 8.4496 9.52901C8.41217 9.74722 8.45318 9.97164 8.56536 10.1625C8.67754 10.3534 8.85365 10.4984 9.0625 10.5719V13.125C9.0625 13.5394 9.22712 13.9368 9.52015 14.2299C9.81317 14.5229 10.2106 14.6875 10.625 14.6875C10.8464 14.6878 11.0608 14.6098 11.2301 14.4672C11.3995 14.3246 11.513 14.1267 11.5504 13.9085C11.5878 13.6903 11.5468 13.4659 11.4346 13.275C11.3225 13.0841 11.1464 12.9391 10.9375 12.8656Z\",\n                        fill: \"#F43F5E\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 11\n                }, this);\n            case 'info':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 20 20\",\n                    fill: \"none\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM11 15H9V9H11V15ZM11 7H9V5H11V7Z\",\n                        fill: \"#3b82f6\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 11\n                }, this);\n            case 'warning':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 20 20\",\n                    fill: \"none\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM11 15H9V13H11V15ZM11 11H9V5H11V11Z\",\n                        fill: \"#eab308\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 11\n                }, this);\n            default:\n                return null;\n        }\n    }\n    static getStyles(type) {\n        const styles = {\n            success: 'bg-green-100 border-green-500 text-green-700',\n            error: 'bg-red-100 border-red-500 text-red-700',\n            info: 'bg-blue-100 border-blue-500 text-blue-700',\n            warning: 'bg-yellow-100 border-yellow-500 text-yellow-700'\n        };\n        return styles[type];\n    }\n}\nfunction NotificationCard({ title, message, icon, type, isVisible, isFixed = false, autoClose = true, duration = 5000, onClose }) {\n    // Si la notification n'est pas visible, on ne rend rien\n    if (!isVisible) return null;\n    // Utiliser le titre par défaut si aucun n'est fourni\n    const displayTitle = title || NotificationConfig.getTitle(type);\n    // Utiliser l'icône par défaut si aucune n'est fournie\n    const displayIcon = icon || NotificationConfig.getIcon(type);\n    // Obtenir les styles en fonction du type\n    const typeStyle = NotificationConfig.getStyles(type);\n    // Fermer automatiquement la notification après la durée spécifiée\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationCard.useEffect\": ()=>{\n            let timer;\n            if (isVisible && autoClose) {\n                timer = setTimeout({\n                    \"NotificationCard.useEffect\": ()=>{\n                        onClose();\n                    }\n                }[\"NotificationCard.useEffect\"], duration);\n            }\n            return ({\n                \"NotificationCard.useEffect\": ()=>{\n                    if (timer) clearTimeout(timer);\n                }\n            })[\"NotificationCard.useEffect\"];\n        }\n    }[\"NotificationCard.useEffect\"], [\n        isVisible,\n        autoClose,\n        duration,\n        onClose\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `mb-4 top-4 right-4 w-full p-4 rounded-lg shadow-lg border-l-4 ${typeStyle} flex items-start space-x-3 transition-all duration-300 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-2'} ${isFixed ? 'fixed max-w-sm z-50' : ''}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0\",\n                children: displayIcon\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-semibold\",\n                        children: displayTitle\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm\",\n                        children: message\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onClose,\n                className: \"flex-shrink-0 text-gray-500 hover:text-gray-700 focus:outline-none\",\n                \"aria-label\": \"Close notification\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"h-5 w-5\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: \"2\",\n                        d: \"M6 18L18 6M6 6l12 12\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\NotificationCard.tsx\",\n        lineNumber: 112,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/NotificationCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/input.tsx":
/*!**********************************!*\
  !*** ./src/components/input.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Input({ label, prefixIcon, error, type = \"text\", className = \"\", onChange, ...props }) {\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isPassword = type === \"password\";\n    const handleChange = (e)=>{\n        if (onChange) {\n            onChange(e);\n        }\n    };\n    const togglePasswordVisibility = ()=>{\n        setShowPassword(!showPassword);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-4\",\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                className: \"block text-gray-800 dark:text-gray-200 text-sm font-semibold mb-2\",\n                children: label\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\input.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    prefixIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute inset-y-0 left-0 flex items-center pl-3\",\n                        children: prefixIcon\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\input.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: isPassword ? showPassword ? \"text\" : \"password\" : type,\n                        onChange: handleChange,\n                        className: `w-full ${prefixIcon ? 'pl-10' : 'pl-3'} ${isPassword ? 'pr-12' : 'pr-3'} py-2 border rounded-[20px] focus:outline-none focus:ring-1 focus:ring-[#17B890] dark:bg-gray-900 dark:text-white text-gray-700 ${error ? 'border-red-500' : 'dark:border-gray-500 border-gray-300'} ${className}`,\n                        ...props\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\input.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    isPassword && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-y-0 right-0 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-[60%] w-px bg-gray-300 dark:bg-gray-600 mx-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\input.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: togglePasswordVisibility,\n                                className: \"pr-3 focus:outline-none\",\n                                \"aria-label\": showPassword ? \"Hide password\" : \"Show password\",\n                                children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"h-5 w-5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\input.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-5 w-5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\input.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\input.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\input.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\input.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-red-500 text-sm mt-1\",\n                children: error\n            }, void 0, false, {\n                fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\input.tsx\",\n                lineNumber: 77,\n                columnNumber: 17\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\input.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/widgets/CircularLoader.tsx":
/*!***************************************************!*\
  !*** ./src/components/widgets/CircularLoader.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst CircularLoader = ({ size = 32, color = \"teal\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex justify-center items-center h-full w-full `,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `animate-spin rounded-full border-t-2 border-r-2 border-${color}`,\n            style: {\n                width: `${size}px`,\n                height: `${size}px`,\n                borderColor: \"transparent\",\n                borderTopColor: `var(--${color})`,\n                borderRightColor: `var(--${color})`\n            }\n        }, void 0, false, {\n            fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\CircularLoader.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projet\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\widgets\\\\CircularLoader.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CircularLoader);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/widgets/CircularLoader.tsx\n");

/***/ }),

/***/ "(ssr)/./src/styles/formStyle.css":
/*!**********************************!*\
  !*** ./src/styles/formStyle.css ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"80ee06bad970\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvc3R5bGVzL2Zvcm1TdHlsZS5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZXRcXHNjaG9sYXJpZnlcXGRhc2hib2FyZFxcc3JjXFxzdHlsZXNcXGZvcm1TdHlsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4MGVlMDZiYWQ5NzBcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/styles/formStyle.css\n");

/***/ }),

/***/ "(ssr)/./src/utils/ThemeInitializer.tsx":
/*!****************************************!*\
  !*** ./src/utils/ThemeInitializer.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeInitializer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction ThemeInitializer() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"ThemeInitializer.useEffect\": ()=>{\n            if (localStorage.getItem(\"theme\") === \"dark\") {\n                document.documentElement.classList.add(\"dark\");\n            } else {\n                document.documentElement.classList.remove(\"dark\");\n            }\n        }\n    }[\"ThemeInitializer.useEffect\"], []);\n    return null; // This component only runs once to apply theme\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdXRpbHMvVGhlbWVJbml0aWFsaXplci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OzZEQUNrQztBQUVuQixTQUFTQztJQUN0QkQsZ0RBQVNBO3NDQUFDO1lBQ1IsSUFBSUUsYUFBYUMsT0FBTyxDQUFDLGFBQWEsUUFBUTtnQkFDNUNDLFNBQVNDLGVBQWUsQ0FBQ0MsU0FBUyxDQUFDQyxHQUFHLENBQUM7WUFDekMsT0FBTztnQkFDTEgsU0FBU0MsZUFBZSxDQUFDQyxTQUFTLENBQUNFLE1BQU0sQ0FBQztZQUM1QztRQUNGO3FDQUFHLEVBQUU7SUFFTCxPQUFPLE1BQU0sK0NBQStDO0FBQzlEIiwic291cmNlcyI6WyJEOlxcUHJvamV0XFxzY2hvbGFyaWZ5XFxkYXNoYm9hcmRcXHNyY1xcdXRpbHNcXFRoZW1lSW5pdGlhbGl6ZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5pbXBvcnQgeyB1c2VFZmZlY3QgfSBmcm9tIFwicmVhY3RcIjtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFRoZW1lSW5pdGlhbGl6ZXIoKSB7XHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChsb2NhbFN0b3JhZ2UuZ2V0SXRlbShcInRoZW1lXCIpID09PSBcImRhcmtcIikge1xyXG4gICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuY2xhc3NMaXN0LmFkZChcImRhcmtcIik7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuY2xhc3NMaXN0LnJlbW92ZShcImRhcmtcIik7XHJcbiAgICB9XHJcbiAgfSwgW10pO1xyXG5cclxuICByZXR1cm4gbnVsbDsgLy8gVGhpcyBjb21wb25lbnQgb25seSBydW5zIG9uY2UgdG8gYXBwbHkgdGhlbWVcclxufVxyXG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwiVGhlbWVJbml0aWFsaXplciIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJkb2N1bWVudCIsImRvY3VtZW50RWxlbWVudCIsImNsYXNzTGlzdCIsImFkZCIsInJlbW92ZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/ThemeInitializer.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/js-cookie","vendor-chunks/jwt-decode"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(auth)%2Flogin%2Fpage&page=%2F(auth)%2Flogin%2Fpage&appPaths=%2F(auth)%2Flogin%2Fpage&pagePath=private-next-app-dir%2F(auth)%2Flogin%2Fpage.tsx&appDir=D%3A%5CProjet%5Cscholarify%5Cdashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjet%5Cscholarify%5Cdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();