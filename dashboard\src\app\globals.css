@tailwind base;
@tailwind components;
@tailwind utilities;
@plugin "daisyui";

@import url('https://fonts.googleapis.com/css2?family=Bricolage+Grotesque:wght@400;700&display=swap');
@import '../styles/formStyle.css';

:root {
  --background: #ffffff;
  --widget:#f6faf6;;
  --foreground: #1E3D59;
  --stroke : #2D3436;
  --teal: #17B890; /* teal do not modify this color*/
  --tealdarker: #0E9B6D; /* teal-600 do not modify this color*/
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #f3f4f6 ;
    --foreground: #1E3D59;
    --stroke : #636E72;
  } 
}
html.dark {
  --background: #111827;
  --foreground: #f3f4f6;
  --stroke: #f3f4f6;
  --widget:#111827;;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: 'Bricolage Grotesque', sans-serif;
}
.bg-tealdarker {
  background-color: var(--tealdarker);
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Thin scrollbar */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: teal #e5e7eb;
}

/* For WebKit (Chrome, Edge) */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #e5e7eb;
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: teal;
  border-radius: 10px;
  border: 1px solid #e5e7eb;
}
/* Subtle Scrollbar */
.subtle-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db transparent; /* thumb color, transparent track */
}

/* WebKit (Chrome, Safari, Edge) */
.subtle-scrollbar::-webkit-scrollbar {
  width: 3px;

}

.subtle-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.subtle-scrollbar::-webkit-scrollbar-thumb {
  background-color: #d1d5db; /* very light gray */
  border-radius: 6px;
  border: 2px solid transparent; /* creates padding effect */
}
.bg-widget{
  background:var(--widget) ;
}
.bg-glassy {

  backdrop-filter: blur(10px); /* Apply blur effect to the background */
  border-radius: 5px; /* Optional: Add rounded corners */
}
/* Remove default blue outline and replace with teal */
:focus-visible {
  outline: none !important;
}

/* Apply teal outline to all form elements on focus */
select:focus, input:focus, textarea:focus, input[type="checkbox"]:focus {
  outline: 2px solid #14b8a6 !important; /* Teal outline */
  box-shadow: 0 0 0 2px rgba(20, 184, 166, 0.3) !important; /* Teal shadow */
}

/* Apply teal background and border when checkbox is checked */
input[type="checkbox"]:checked {
  background-color: #14b8a6 !important; /* Teal background when checked */
  border-color: #14b8a6 !important; /* Teal border */
}

/* Remove blue outline from select elements */
select:focus {
  border-color: #14b8a6 !important; /* Teal border */
  box-shadow: 0 0 0 2px rgba(20, 184, 166, 0.3) !important; /* Teal shadow */
}

/* Apply teal color on hover and focus */
input[type="checkbox"]:checked {
  background-color: #14b8a6 !important;
  border-color: #14b8a6 !important;
}

input[type="checkbox"]:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(20, 184, 166, 0.3) !important; /* Teal shadow */
}

/* Teal background for options in the select */
select option:checked {
  background-color: #14b8a6 !important;
  color: white !important;
}

/* Remove default blue outline from form inputs */
input:focus, textarea:focus, select:focus {
  outline: none !important;
  border-color: #14b8a6 !important; /* Teal border */
  box-shadow: 0 0 0 2px rgba(20, 184, 166, 0.3) !important; /* Teal shadow */
}

@keyframes pulseBar {
  0% {
    transform: scaleY(0.5);
  }
  50% {
    transform: scaleY(1.5);
  }
  100% {
    transform: scaleY(0.5);
  }
}

.animate-pulseBar {
  animation: pulseBar 1.5s ease-in-out infinite;
}
