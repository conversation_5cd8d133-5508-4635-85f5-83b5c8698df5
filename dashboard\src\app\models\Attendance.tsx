export type AttendanceStatus = 'Present' | 'Absent' | 'Late' | 'Excused';

export interface AttendanceSchema extends Record<string, unknown> {
  _id: string;
  school_id: string;            // MongoDB ObjectId as string
  student_id: string;           // MongoDB ObjectId as string
  schedule_id: string;          // MongoDB ObjectId as string (refers to ClassSchedule)
  status: AttendanceStatus;
  academic_year: string;        // e.g., "2024-2025"
  date: string;                 // ISO date string
  createdAt?: string;           // Auto-generated timestamp
  updatedAt?: string;           // Auto-generated timestamp
}

export interface AttendanceCreateSchema extends Record<string, unknown> {
  school_id: string;            // Required
  student_id: string;           // Required
  schedule_id: string;          // Required
  status: AttendanceStatus;     // Required
  academic_year: string;        // Required
  date: string;                 // Required (ISO format)
}

export interface AttendanceUpdateSchema extends Record<string, unknown> {
  _id: string;                  // Required for identifying the attendance record
  school_id?: string;
  student_id?: string;
  schedule_id?: string;
  status?: AttendanceStatus;
  academic_year?: string;
  date?: string;
}

export interface AttendanceDeleteSchema extends Record<string, unknown> {
  _id: string;                  // Required MongoDB ID to delete
}
