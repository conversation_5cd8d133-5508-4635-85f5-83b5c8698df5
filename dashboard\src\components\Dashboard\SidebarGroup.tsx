"use client";

import React, { useState } from "react";
import { ChevronDown, ChevronRight } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import SidebarButton from "./SideNavButton";

interface SidebarItem {
  icon: React.ElementType;
  name: string;
  href: string;
  disabled?: boolean;
}

interface SidebarGroupProps {
  title: string;
  icon: React.ElementType;
  items: SidebarItem[];
  defaultExpanded?: boolean;
}

const SidebarGroup: React.FC<SidebarGroupProps> = ({
  title,
  icon: GroupIcon,
  items,
  defaultExpanded = false,
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className="mb-2">
      {/* Group Header */}
      <button
        onClick={toggleExpanded}
        className="w-full flex items-center justify-between px-3 py-2 text-sm font-medium text-foreground/70 hover:text-foreground hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors group"
      >
        <div className="flex items-center space-x-2">
          <GroupIcon className="h-4 w-4" />
          <span>{title}</span>
        </div>
        <motion.div
          animate={{ rotate: isExpanded ? 90 : 0 }}
          transition={{ duration: 0.2 }}
        >
          <ChevronRight className="h-4 w-4" />
        </motion.div>
      </button>

      {/* Group Items */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            <div className="ml-4 mt-1 space-y-1 border-l border-gray-200 dark:border-gray-700 pl-2">
              {items.map((item) => (
                <div key={item.name} className="relative">
                  {/* Connection line */}
                  <div className="absolute -left-2 top-1/2 w-2 h-px bg-gray-200 dark:bg-gray-700"></div>
                  
                  <SidebarButton
                    icon={item.icon}
                    name={item.name}
                    href={item.href}
                    isSubItem={true}
                    disabled={item.disabled}
                  />
                </div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SidebarGroup;
