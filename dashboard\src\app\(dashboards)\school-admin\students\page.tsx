"use client";

import { Presentation } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import CircularLoader from "@/components/widgets/CircularLoader";
import React, { Suspense} from "react";
import useAuth from "@/app/hooks/useAuth";
import StudentComponent from "@/components/Dashboard/ReusableComponents/StudentComponent";

const BASE_URL = "/school-admin";

const navigation = {
  icon: Presentation,
  baseHref: `${BASE_URL}/classes`,
  title: "Classes"
};

export default function Page() {
  const { logout } = useAuth();
  const { user } = useAuth();
  return (
    <Suspense fallback={
      <div>
        <div className="flex justify-center items-center h-screen absolute top-0 left-0 z-50">
          <CircularLoader size={32} color="teal" />
        </div>
      </div>
    }>
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        {/* You might want to pass schoolId from somewhere here */}
        {user && <StudentComponent user={user} />}
      </SchoolLayout>
    </Suspense>
  );
}
