import {
  Page,
  Text,
  View,
  Document,
  StyleSheet,
  Font,
  Image,
} from '@react-pdf/renderer';

// Font registration
Font.register({
  family: 'Roboto',
  fonts: [
    { src: '/fonts/Roboto-Regular.ttf', fontWeight: 'normal' },
    { src: '/fonts/Roboto-Italic.ttf', fontWeight: 'normal', fontStyle: 'italic' },
    { src: '/fonts/Roboto-Bold.ttf', fontWeight: 'bold' },
  ],
});

const styles = StyleSheet.create({
  page: {
    fontFamily: 'Roboto',
    fontSize: 12,
    padding: 30,
    backgroundColor: '#F5F6FA', // background
    color: '#1E3D59', // foreground
  },
  header: {
    textAlign: 'center',
    fontSize: 24,
    fontWeight: 'bold',
    color: '#17B890', // teal
    marginBottom: 10,
    letterSpacing: 1.5,
  },
  logo: {
    width: '20%',
    height: 50,
    marginBottom: 10,
  },
  receiptInfoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
    fontSize: 11,
    color: '#1E3D59', // foreground
  },
  section: {
    marginBottom: 18,
    paddingHorizontal: 8,
  },
  sectionTitle: {
    fontWeight: 'bold',
    fontSize: 14,
    marginBottom: 6,
    borderBottomWidth: 1,
    borderBottomColor: '#2D3436', // stroke
    paddingBottom: 4,
    letterSpacing: 0.5,
    color: '#2D3436', // stroke
    backgroundColor:'transparent'
  },
  studentDetails: {
    fontSize: 12,
    color: '#444',
    lineHeight: 1.4,
  },
  table: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    overflow: 'hidden',
    marginTop: 8,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#E0F7F4', // soft teal background (light version of teal)
    borderBottomWidth: 1,
    borderBottomColor: '#0E9B6D', // tealdarker
    paddingVertical: 6,
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    paddingVertical: 6,
  },
  lastRow: {
    borderBottomWidth: 0,
  },
  cellIndex: {
    width: '10%',
    paddingLeft: 10,
    fontWeight: 'bold',
    color: '#17B890', // teal
  },
  cellDesc: {
    width: '60%',
    paddingLeft: 12,
  },
  cellAmount: {
    width: '30%',
    textAlign: 'right',
    paddingRight: 12,
    fontVariantNumeric: 'tabular-nums',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 4,
    fontSize: 12,
  },
  summaryTotal: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    marginTop: 6,
    borderTopWidth: 1,
    borderTopColor: '#0E9B6D', // tealdarker
    fontWeight: 'bold',
    fontSize: 14,
    color: '#0E9B6D', // tealdarker
  },
  installmentBox: {
    borderWidth: 1,
    borderColor: '#17B890',
    borderRadius: 6,
    padding: 10,
    backgroundColor: '#E8FAF6', // very light teal
    marginTop: 10,
  },
  installmentLine: {
    fontSize: 12,
    marginBottom: 4,
  },
  paidStamp: {
    position: 'absolute',
    top: 250,
    left: 200,
    fontSize: 56,
    color: '#2D3436', // stroke (more visible than red)
    opacity: 0.1,
    transform: 'rotate(-20deg)',
    fontWeight: 'bold',
  },
  note: {
    fontSize: 10,
    fontStyle: 'italic',
    color: '#666',
    marginTop: 20,
  },
  footer: {
    marginTop: 40,
    fontSize: 10,
    color: '#999',
    textAlign: 'center',
  },
  poweredByContainer: {
    marginTop: 20,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  poweredByText: {
    fontSize: 10,
    color: '#999',
    marginRight: 6,
  },
  poweredByLogo: {
    width: 50,
    height: 15,
  },
});

type Student = {
  student_id: string;
  first_name: string;
  last_name: string;
  class_level?: string;
  class_id?: string;
};

type School = {
  name: string;
  logoUrl?: string;
};

type PaymentItem = {
  description: string;
  amount: number;
};

type ReceiptPDFProps = {
  student: Student;
  school: School;
  paymentItems: PaymentItem[];
  receiptId: string;
  date: string | number | Date;
  taxRate?: number;
  applyScholarship?: boolean;
  scholarshipPercentage?: number;
  installments?: number;
  installmentDates?: string[];
  paidInstallmentNumber?: number; // New: to track which installment this receipt represents
};

const ReceiptPDF: React.FC<ReceiptPDFProps> = ({
  student,
  school,
  paymentItems,
  receiptId,
  date,
  taxRate = 0,
  applyScholarship = false,
  scholarshipPercentage = 0,
  installments,
  installmentDates,
  paidInstallmentNumber = 1,
}) => {
  const subTotal = paymentItems.reduce((sum, item) => sum + item.amount, 0);
  const taxAmount = subTotal * taxRate;
  const scholarshipDiscount = applyScholarship ? (scholarshipPercentage / 100) * subTotal : 0;
  const totalAfterScholarship = subTotal - scholarshipDiscount;
  const total = totalAfterScholarship + taxAmount;

  const isInstallment = installments && installments > 1;
  const amountPerInstallment = isInstallment ? total / installments : total;
  const paidInstallment = paidInstallmentNumber;
  const amountPaid = amountPerInstallment;
  const totalPaidSoFar = amountPerInstallment * paidInstallment;
  const remainingBalance = total - totalPaidSoFar;

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Stamp first so it's drawn last (on top) */}
        {!isInstallment && (
          <Text style={styles.paidStamp}>PAID</Text>
        )}
        {isInstallment && (
          <Text style={styles.paidStamp}>
            {paidInstallment >= installments ? 'FULLY PAID' : 'PARTIALLY PAID'}
          </Text>
        )}
        {/* Logo and Header */}
        <View style={{ alignItems: 'center', marginBottom: 10 }}>
          <Image
            style={styles.logo}
            src={school.logoUrl ? school.logoUrl : '/assets/logo.png'}
          />
          <Text style={styles.header}>SCHOOL FEE RECEIPT</Text>
        </View>


        {/* Info */}
        <View style={styles.receiptInfoRow}>
          <Text>Receipt No: {receiptId}</Text>
          <Text>Date: {new Date(date).toLocaleDateString()}</Text>
        </View>

        {/* Student */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Student Details</Text>
          <View style={styles.studentDetails}>
            <Text>ID: {student.student_id}</Text>
            <Text>Name: {student.first_name} {student.last_name}</Text>
            <Text>Grade/Class: {student.class_level || student.class_id}</Text>
            <Text>School: {school.name}</Text>
          </View>
        </View>

        {/* Breakdown */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Payment Breakdown</Text>
          <View style={styles.table}>
            <View style={styles.tableHeader}>
              <Text style={styles.cellIndex}>#</Text>
              <Text style={styles.cellDesc}>Description</Text>
              <Text style={styles.cellAmount}>Amount (XAF)</Text>
            </View>
            {paymentItems.map((item, index) => (
              <View key={index} style={[styles.tableRow, ...(index === paymentItems.length - 1 ? [styles.lastRow] : [])]}>
                <Text style={styles.cellIndex}>{String(index + 1).padStart(2, '0')}</Text>
                <Text style={styles.cellDesc}>{item.description}</Text>
                <Text style={styles.cellAmount}>{(item.amount ?? 0).toFixed(2)}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Summary Section */}
        <View style={styles.section}>
          <View style={styles.summaryRow}>
            <Text>Subtotal:</Text>
            <Text>XAF {subTotal.toFixed(2)}</Text>
          </View>
          {applyScholarship && (
            <View style={styles.summaryRow}>
              <Text>Scholarship ({scholarshipPercentage}%):</Text>
              <Text>- XAF {scholarshipDiscount.toFixed(2)}</Text>
            </View>
          )}
          <View style={styles.summaryRow}>
            <Text>Tax Rate:</Text>
            <Text>{(taxRate * 100).toFixed(0)}%</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text>Tax Amount:</Text>
            <Text>XAF {taxAmount.toFixed(2)}</Text>
          </View>

          {!isInstallment && (
            <View style={styles.summaryTotal}>
              <Text>Total Payable:</Text>
              <Text>XAF {total.toFixed(2)}</Text>
            </View>
          )}
        </View>

        {/* Installment Plan */}
        {isInstallment && installmentDates && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Installment Plan</Text>
            <View style={styles.installmentBox}>
              <Text style={styles.installmentLine}>Total Payable: XAF {total.toFixed(2)}</Text>
              <Text style={styles.installmentLine}>Installments: {installments}</Text>
              <Text style={styles.installmentLine}>Due Dates:</Text>
              {installmentDates.map((date, i) => (
                <Text key={i} style={styles.installmentLine}>
                  Installment {i + 1} - {date}: XAF {(total / installments).toFixed(2)}
                </Text>
              ))}
            </View>
          </View>
        )}

        {/* Paid Installment Info */}
        {isInstallment && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Installment Payment Received</Text>
            <View style={styles.installmentBox}>
              <Text style={styles.installmentLine}>Installment Paid: {paidInstallment} of {installments}</Text>
              <Text style={styles.installmentLine}>Amount Paid (This Installment): XAF {amountPaid.toFixed(2)}</Text>
              <Text style={styles.installmentLine}>Total Paid So Far: XAF {totalPaidSoFar.toFixed(2)}</Text>
              <Text style={styles.installmentLine}>Remaining Balance: XAF {remainingBalance.toFixed(2)}</Text>

            </View>
          </View>
        )}

        {/* Footer Notes */}
        <View style={styles.section}>
          <Text style={styles.note}>
            Note: Keep this receipt for future reference. Thank you for your payment!
          </Text>
        </View>

        <Text style={styles.footer}>
          {school.name} | Contact: <EMAIL> | +1 555 123 4567
        </Text>

        <View style={styles.poweredByContainer}>
          <Text style={styles.poweredByText}>Powered by</Text>
          <Image style={styles.poweredByLogo} src="/assets/logo.png" />
        </View>


      </Page>
    </Document>
  );
};

export default ReceiptPDF;
