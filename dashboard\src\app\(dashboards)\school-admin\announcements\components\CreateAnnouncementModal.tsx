"use client";

import React, { useState, useEffect } from "react";
import { X } from "lucide-react";
import { AnnouncementCreateSchema, AnnouncementSchema } from "@/app/services/AnnouncementServices";
import CustomInput from "@/components/inputs/CustomInput";
import SubmissionFeedback from "@/components/widgets/SubmissionFeedback";
import CircularLoader from "@/components/widgets/CircularLoader";
import { motion } from "framer-motion";

interface CreateAnnouncementModalProps {
  onClose: () => void;
  onSave: (announcementData: AnnouncementCreateSchema) => Promise<void>;
  initialData?: AnnouncementSchema | null;
  submitStatus: "success" | "failure" | null;
  isSubmitting: boolean;
}

export default function CreateAnnouncementModal({
  onClose,
  onSave,
  initialData,
  submitStatus,
  isSubmitting,
}: CreateAnnouncementModalProps) {
  const [formData, setFormData] = useState<AnnouncementCreateSchema>({
    title: "",
    content: "",
    school_id: "",
    target_audience: "all",
    priority: "medium",
    is_published: false,
  });

  // Populate form with initial data if editing
  useEffect(() => {
    if (initialData) {
      setFormData({
        title: initialData.title,
        content: initialData.content,
        school_id: initialData.school_id,
        target_audience: initialData.target_audience,
        priority: initialData.priority,
        is_published: initialData.is_published,
        expires_at: initialData.expires_at ? new Date(initialData.expires_at) : undefined,
      });
    }
  }, [initialData]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await onSave(formData);
  };

  const handleInputChange = (field: keyof AnnouncementCreateSchema, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="max-h-svh overflow-y-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-2xl mx-4 sm:mx-6 md:mx-0 p-6 relative">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold text-foreground">
            {initialData ? "Edit Announcement" : "Add New Announcement"}
          </h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X size={20} />
          </button>
        </div>

        {submitStatus ? (
          <SubmissionFeedback 
            status={submitStatus}
            message={
              submitStatus === "success"
                ? `Announcement has been ${initialData ? "updated" : "created"} successfully!`
                : "There was an error trying to save the announcement. Try again and if this persists, contact support."
            } 
          />
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Title */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-foreground">
                Title <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => handleInputChange("title", e.target.value)}
                placeholder="Enter announcement title"
                required
                className="w-full px-3 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent bg-widget text-foreground"
              />
            </div>

            {/* Content */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-foreground">
                Content <span className="text-red-500">*</span>
              </label>
              <textarea
                value={formData.content}
                onChange={(e) => handleInputChange("content", e.target.value)}
                placeholder="Enter announcement content"
                required
                rows={6}
                className="w-full px-3 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent bg-widget text-foreground"
              />
            </div>

            {/* Target Audience */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-foreground">
                Target Audience <span className="text-red-500">*</span>
              </label>
              <select
                value={formData.target_audience}
                onChange={(e) => handleInputChange("target_audience", e.target.value)}
                required
                className="w-full px-3 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent bg-widget text-foreground"
              >
                <option value="all">All</option>
                <option value="teachers">Teachers</option>
                <option value="parents">Parents</option>
                <option value="students">Students</option>
              </select>
            </div>

            {/* Priority */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-foreground">
                Priority <span className="text-red-500">*</span>
              </label>
              <select
                value={formData.priority}
                onChange={(e) => handleInputChange("priority", e.target.value)}
                required
                className="w-full px-3 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent bg-widget text-foreground"
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="urgent">Urgent</option>
              </select>
            </div>

            {/* Expiry Date */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-foreground">
                Expiry Date (Optional)
              </label>
              <div className="relative">
                <input
                  type="datetime-local"
                  value={formData.expires_at ? new Date(formData.expires_at).toISOString().slice(0, 16) : ""}
                  onChange={(e) => handleInputChange("expires_at", e.target.value ? new Date(e.target.value) : undefined)}
                  className="w-full px-3 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent bg-widget text-foreground [&::-webkit-calendar-picker-indicator]:cursor-pointer [&::-webkit-calendar-picker-indicator]:opacity-100"
                  style={{
                    colorScheme: 'light dark'
                  }}
                />
              </div>
            </div>

            {/* Publish Status */}
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="is_published"
                checked={formData.is_published}
                onChange={(e) => handleInputChange("is_published", e.target.checked)}
                className="w-4 h-4 text-teal bg-widget border-stroke rounded focus:ring-teal focus:ring-2"
              />
              <label htmlFor="is_published" className="text-sm font-medium text-foreground">
                Publish immediately
              </label>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              >
                {isSubmitting && <CircularLoader size={16} color="white" />}
                <span>{initialData ? "Update" : "Create"} Announcement</span>
              </motion.button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
}
