import { getTokenFromCookie } from "./UserServices";

const BASE_API_URL = process.env.BASE_API_URL || "https://scolarify.onrender.com/api";


export interface TeacherAssignment {
  _id: string;
  teacher_id: string;
  school_id: string;
  assigned_classes: Array<{
    _id: string;
    name: string;
    level: string;
  }>;
  assigned_subjects: Array<{
    _id: string;
    name: string;
    code: string;
  }>;
}

// Get teacher's assigned classes and subjects for a specific school
export async function getTeacherAssignments(schoolId: string): Promise<TeacherAssignment> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/teacher/assignments/${schoolId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching teacher assignments:", response.statusText);
      throw new Error("Failed to fetch teacher assignments");
    }

    const data = await response.json();
    return data as TeacherAssignment;
  } catch (error) {
    console.error("Fetch teacher assignments error:", error);
    throw new Error("Failed to fetch teacher assignments");
  }
}

// Get classes assigned to the current teacher
export async function getTeacherClasses(schoolId: string): Promise<Array<{_id: string, name: string, level: string}>> {
  try {
    const assignments = await getTeacherAssignments(schoolId);
    return assignments.assigned_classes;
  } catch (error) {
    console.error("Error fetching teacher classes:", error);
    return [];
  }
}

// Get subjects assigned to the current teacher
export async function getTeacherSubjects(schoolId: string): Promise<Array<{_id: string, name: string, code: string}>> {
  try {
    const assignments = await getTeacherAssignments(schoolId);
    return assignments.assigned_subjects;
  } catch (error) {
    console.error("Error fetching teacher subjects:", error);
    return [];
  }
}

// Get students in teacher's assigned classes
export async function getTeacherStudents(schoolId: string): Promise<Array<{
  _id: string;
  first_name: string;
  last_name: string;
  email: string;
  class_id: string;
  class_name: string;
}>> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/teacher/students/${schoolId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching teacher students:", response.statusText);
      throw new Error("Failed to fetch teacher students");
    }

    const data = await response.json();
    return data.students || [];
  } catch (error) {
    console.error("Fetch teacher students error:", error);
    return [];
  }
}
